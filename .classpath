<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8"/>
	<classpathentry kind="lib" path="install/lib/log4j-1.2.15.jar"/>
	<classpathentry kind="lib" path="install/lib/wrapper.jar"/>
	<classpathentry kind="lib" path="install/lib/mysql-connector-java-3.1.12-bin.jar"/>
	<classpathentry kind="lib" path="install/lib/commons-dbcp-1.2.1.jar"/>
	<classpathentry kind="lib" path="install/lib/commons-pool-1.3.jar"/>
	<classpathentry kind="lib" path="install/lib/ojdbc14.jar"/>
	<classpathentry kind="lib" path="install/lib/providerutil.jar"/>
	<classpathentry kind="lib" path="install/lib/fscontext.jar"/>
	<classpathentry kind="lib" path="install/lib/ifxjdbc.jar"/>
	<classpathentry kind="lib" path="install/lib/commons-codec-1.7.jar"/>
	<classpathentry kind="lib" path="install/lib/mssql-jdbc-12.2.0.jre8.jar"/>
	<classpathentry kind="lib" path="install/lib/sqljdbc42.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
