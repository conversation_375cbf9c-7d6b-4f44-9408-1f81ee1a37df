<?xml version="1.0" encoding="UTF-8"?>
<project default="make.common" name="build-kskyb-magent">
	<target name="make.common">
		<delete file="F:/agent_sms_export/lib/mAgent.jar" />
		<jar destfile="F:/agent_sms_export/lib/mAgent.jar" compress="true">
			<manifest>
				<attribute name="Main-Class" value="com.kskyb.magent.Main" />
				<!-- Information about the program itself -->
				<attribute name="Implementation-Vendor" value="KskyB inc." />
				<attribute name="Implementation-Title" value="M-Agent" />
				<attribute name="Implementation-Version" value="1.0" />
			</manifest>
			<fileset dir="bin">
				<include name="**/*" />
			</fileset>
		</jar>
	</target>
</project>
