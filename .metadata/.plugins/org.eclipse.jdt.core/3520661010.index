 INDEX VERSION 1.127  
 2sun/net/spi/nameservice/dns/DNSNameService$1.class+ 2+ 
ThreadContext*  * 
Descriptor    $assertionsDisabled���� val$name���� val$ctx���� nsList���� this$0     val$ids���� 
contextRef���� nameProviderUrl���� val$env     dirCtxt���� 
domainList����   - run/0     getAttributes/2���� get/1���� textToNumericFormatV4/1���� 
toString/0���� 	hasNext/0���� put/2���� get/0���� appendIfLiteralAddress/2���� 	resolve/4���� getException/0���� getMessage/0���� length/0���� size/0���� createProviderURL/1���� nextToken/0���� getAll/0���� searchlist/0���� substring/1���� getByAddress/2���� hasMoreTokens/0���� 
toHexString/1���� open/0���� 	isEmpty/0���� next/0���� set/1���� addAll/1���� substring/2���� equals/1���� 
nameservers/0���� desiredAssertionStatus/0���� doPrivileged/1���� textToNumericFormatV6/1���� getTemporaryContext/0���� isIPv6LiteralAddress/1���� isIPv4LiteralAddress/1���� 
iterator/0���� add/1���� append/1���� getID/0���� 
endsWith/1���� 	indexOf/1���� getInitialContext/1     hasMoreElements/0���� dirContext/0����    	resolve/4���� createNameService/0���� run/0     getProviderName/0���� appendIfLiteralAddress/2���� getHostByAddr/1���� dirContext/0���� 
nameservers/0���� createProviderURL/1���� 	getType/0���� getTemporaryContext/0���� lookupAllHostAddr/1����   H 
LinkedList���� util���� naming     
Attributes    AssertionError���� security     
IPAddressUtil���� action���� AccessController���� Context     $assertionsDisabled���� DNSNameServiceDescriptor���� 	directory     val$env     ResolverConfiguration���� 
InetAddress[]���� Object     InetAddress���� Class���� boolean���� dns     
domainList���� StringTokenizer���� val$name���� 
SoftReference���� nameProviderUrl���� DNSNameService     DNSNameService$2    net     Error���� Iterator���� 	ArrayList���� sun     DNSNameService$ThreadContext    GetPropertyAction���� 
contextRef���� String    void���� int���� NamingEnumeration���� 
DirContext     lang     UnknownHostException���� PrivilegedActionException���� spi     val$ctx���� Integer���� java     	Attribute���� javax     	Exception     this$0     
NamingManager     DNSNameService$1     StringBuffer���� nameservice     NameServiceDescriptor���� String[]    	Hashtable     NamingException     ThreadLocal���� ref���� dirCtxt���� val$ids���� nsList���� List    byte[]���� 
ThreadContext    
StringBuilder���� RuntimeException���� PrivilegedExceptionAction     NameService       /1/ ��     /3/ ������ 2DNSNameService/0/1 /sun.net.spi.nameservice.dns/ ���� <DNSNameServiceDescriptor/0/1 /sun.net.spi.nameservice.dns/ ���� ThreadContext/1/
������    GetPropertyAction/1���� ArrayList/0���� DNSNameService/0���� LinkedList/0���� DNSNameService$2/3���� 2/3���� Hashtable/0���� 1/1���� RuntimeException/1���� AssertionError/0���� 
ThreadLocal/0���� DNSNameService$ThreadContext/2���� UnknownHostException/0���� Error/1���� StringBuffer/0���� DNSNameService$1/1���� StringTokenizer/2���� ThreadContext/2���� Object/0     StringBuilder/0���� UnknownHostException/1���� SoftReference/1����    ;ThreadContext/sun.net.spi.nameservice.dns/DNSNameService/
 ���� 8DNSNameServiceDescriptor/sun.net.spi.nameservice.dns//1 ���� !/sun.net.spi.nameservice.dns/0/       .DNSNameService/sun.net.spi.nameservice.dns//1 ����    TNameService/sun.net.spi.nameservice/DNSNameService///sun.net.spi.nameservice.dns/IC1���� hNameServiceDescriptor/sun.net.spi.nameservice/DNSNameServiceDescriptor///sun.net.spi.nameservice.dns/IC1���� KObject/java.lang/DNSNameServiceDescriptor///sun.net.spi.nameservice.dns/CC1���� 4Object/java.lang//0//sun.net.spi.nameservice.dns/CC      AObject/java.lang/DNSNameService///sun.net.spi.nameservice.dns/CC1���� KPrivilegedExceptionAction/java.security//0//sun.net.spi.nameservice.dns/IC      NObject/java.lang/ThreadContext/DNSNameService//sun.net.spi.nameservice.dns/CC
����   |      v    	fieldDecl   u 	methodRef  # 
methodDecl  U ref  Y constructorDecl  
a constructorRef   typeDecl  � superRef  
�