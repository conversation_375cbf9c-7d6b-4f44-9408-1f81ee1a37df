 INDEX VERSION 1.127  �@ 2com/microsoft/jdbc/sqlserver/SQLServerColumn.class( nnectio& DepacketizingDataProvider' riv& EscapeTranslato& ImplConnection* DatabaseMetaData* 	ResultSet3 ServerSideCursor* 	Statement& License' ocalMessages( ngDataStream& PacketizingDataConsumer tds/TDSCancelRequest% onnection$TransliteratorForLCID.  ' stants% ursorRequest$
 DTC% ateTime$ ExecuteRequest$
 Login'
 out$ RPCParameter' Request%   x/sqlserver/SQLServerDataSource1 Factory' ImplXAResource  � TDS_VARCHAR���� TDS_LOGIN_REQUEST_BASE_LEN���� actualScrollType���� BASEYEAR���� CURS_PREV_NOADJUST���� positionedOnValidRow���� TDS_DATETIME���� TDS_SQLVARIANT���� connectProps���� READWRITEUNKNOWN���� TDS_SMALLFLOAT���� rowsInTable���� CURS_REFRESHPOS���� TDS_SMALLMONEY���� TDS_VOID���� defaultMaxFieldSize���� maxPrecision���� CASESENSITIVE_BIT���� sql���� 
USE_DB_OFF���� TDS_BITN���� DEFAULT_FETCH_SIZE���� 
OWNER_TERM���� COLINFO_EXPRESSION���� 	CURS_PREV���� USER_NORMAL���� databaseName���� 
CURS_RELATIVE���� ERROR���� savedByteValue���� RETURNVALUE���� depacketizer���� SQL_ANSI_V1���� 	fetchSize���� commandText���� RPC_OPTION_SENT_WITH_RECOMPILE���� TDS_FLT8���� typesForTDS_07000000���� numRowsParam���� 
parameters���� BASEBIAS���� ENVCHANGE_UNICODECOMP���� SQL_ANSI92_FULL���� SQL_DFLT���� 	ENVCHANGE���� CURS_INSERT���� 
TDS_UNDEFINED���� useServerSideCursor���� 
CURS_FIRST���� UNHANDLED_DATA_TYPE���� ENVCHANGE_SQLCOLLATION���� DONE���� 	DBMS_NAME���� requestedScrollType���� TDS_BIT���� writer   	 MAX_INDEX_COLS���� CURS_FETCH_UPDATED���� MULTI_RESULT_SETS���� num300thSecondsSinceMidnight���� 
TDS_VARBINARY���� TDS_INT2���� typesForTDS_07010000���� MAX_QUAL_LENGTH���� 	precision���� USER_SERVER���� SQLDROPPROCEDURE���� 	exception���� 	DONE_MORE���� numReturnedParameters���� cursorHandle���� IDENTITY_BIT���� READONLY���� UPDATABLE_BITS���� TDS_BIGVARCHAR���� TDS_MESSAGE_TYPE_TABLERESPONSE���� 
USERID_LENGTH���� USER_SQLREPL���� CURS_FORWARD_ONLY���� CURS_FAST_FORWARD_ONLY���� 	TDS_NTEXT���� socketProvider���� TDS_FLTN���� XA_START���� CURS_FETCH_SUCCEEDED���� DONE_INXACT���� 
CURS_OPTCCVAL���� 	resultSet���� transliterators���� ENVCHANGE_LANG���� executingSQLDirectly���� PROCESS_TIL_ROW���� DUMPLOAD_OFF���� RPC_OPTION_NONE���� DONEPROC���� nativeTypeName���� DUMPLOAD_ON���� CURS_KEYSET_ACCEPTABLE���� 
DONEINPROC���� JTA_NOT_SUPPORTED_IN_DIRECT���� TDS_STATUS_FLAG_ATTENTIONACK���� CURS_SKIP_UPDT_CNCY���� FLOAT_IEEE_754���� socketConsumer���� TDS_CHAR���� INFO���� SQLFETCHCURSOR���� reader   	 ENVCHANGE_UNICODELCID���� ENVCHANGE_PACKETSIZE���� 	CURS_NEXT���� 
cursorType���� DESCENDING_INDEXES���� baseDataType     
tdsVersion    isInput���� 
SQL_ANSI89_L1���� TDS_07000000���� TDS_UNIQUEID���� COMPUTE_BY_CLAUSE_NOT_SUPPORTED���� maxFieldSize���� sqlEx���� returnValueType    COLINFO���� nXids���� IntegerZero���� 
resultType���� TDS_MESSAGE_TYPE_PRETDS7LOGIN���� numRowsFetchedFromResultSet���� UDT_NOT_SUPPORTED���� CURS_STATIC_ACCEPTABLE���� CURS_DYNAMIC_ACCEPTABLE���� MAX_NTEXT_BYTES���� CURS_OPTIMISTIC_ACCEPTABLE���� 	isServer7���� TDS_MESSAGE_TYPE_BULKLOADDATA���� 
INIT_DB_FATAL���� &NUM_NANOSECONDS_IN_A_300TH_OF_A_SECOND���� password���� this$0���� TDS_MESSAGE_TYPE_ATTENTION���� 
TDS_BIGINT���� TDS_MESSAGE_HEADER_SIZE���� 	CURS_LOCK���� warnings���� SQL_ANSI92_ENTRY���� ERROR_ON_DISCONNECT���� SS7_CONNECTION_REFUSED���� 
DONE_COUNT���� sendStringParametersAsUnicode    SQLDROPTABLE���� 
headerBuff���� RESERVED_ODBC_BITS���� COMPUTED_BIT���� CURS_LOCKS_ACCEPTABLE���� TDS_STATUS_FLAG_ENDOFMESSAGE���� 
SQLALTERTABLE���� selectMethod���� ds���� 	READWRITE���� CURS_SETABSOLUTE���� TDS_NVARCHAR���� XA_CLOSE���� 
TABLE_TERM���� SQLPOSUPDATE���� CURS_AUTO_FETCH���� 	USE_DB_ON���� CURS_READ_ONLY_ACCEPTABLE���� CURS_FORWARD_ONLY_ACCEPTABLE���� hasProcReturnParam���� 	CURS_LAST���� DROP_COLUMN���� SYS_SPROC_VERSION���� SET_LANG_ON���� XA_OPEN���� resultTypeReported���� 	paramName���� ORDER_68000���� DEFAULT_PACKET_SIZE���� COLINFO_HIDDEN���� 
connection���� 	XA_FORGET���� tdsType���� resultTypeHasBeenRequested���� lastRowReturned���� cookie���� 	SP_RENAME���� TDS_SMALLDATETIME���� 
MAXNICSIZE���� 
TDS_BINARY���� SQLCREATETABLE���� bufferedConsumer���� SQL_ANSI89_IEF���� rowNumberParam���� databaseMetaData���� isOutput���� conn   	 CURS_CHECK_ACCEPTED_TYPES���� ALTMETADATA���� CACHE_CONNECTION_OFF���� CURS_FETCH_ENDOFKEYSET���� 
RENAME_COLUMN���� 
CURS_READONLY���� bufferedProvider���� 
CURS_ABSOLUTE���� SET_LANG_OFF���� PARAM_IS_FOR_OUTPUT���� username���� TDSType     SQLServerRangeBegin���� 
XA_PREPARE���� INCREASE_COLUMN_LENGTH���� TDS_STATUS_FLAG_IGNOREEVENT���� CURS_OPEN_ON_ANY_SQL���� TABNAME���� lookaheadTokenType���� rowsInTableParam���� data     CHARSET_EBCDIC���� 
CHARSET_ASCII���� TDS_MESSAGE_TYPE_RPC���� ORDER���� 
XA_RECOVER���� statusField���� TDS_07010000���� TDS_INT8���� cursorTypeParam���� CURS_FAST_FORWARD_ACCEPTABLE���� ALTROW���� COLMETADATA���� 
TDS_MONEYN���� CURS_AUTO_CLOSE���� LOGINACK���� ERROR_DURING_LOGON_ATTEMPT���� numRowsAffected���� 	CURS_INFO���� enlisted���� 
netAddress    SQL_TSQL���� TDS_TEXT���� TRAN_BOUNDARY_OFF���� XP_XA���� ACCESSIBLE_SPROC���� INVALID_OR_INACTIVE_INSTANCE���� CANNOT_ESTABLISH_SOCKET���� programName    hostProcess    TDS_DECIMALN���� TDS_MESSAGE_TYPE_PROTOCOLERROR���� 
SQL_ANSI89_L2���� scale���� TDS_INT4���� CURS_REFRESH���� 
RPCOptions���� TDS_MESSAGE_TYPE_DTCREQUEST���� TDS_NUMERIC���� CURS_KEYSET_DRIVEN���� TDS_MESSAGE_TYPE_SSPIMESSAGE���� CURS_INSENSITIVE���� ACCESSIBLE_TABLES���� SQLPOSDELETE���� COLINFO_DIFFERENT_NAME���� 
TDS_DATETIMEN���� DONE_RPCINBATCH���� TDS_BIGCHAR���� TDS_INTN���� TDS_MESSAGE_TYPE_SQLBATCH���� maxRows���� 	maxLength���� USER_REMUSER���� 
nativeColumns���� 
COLLATION_SEQ���� isUCS2Stream���� numDaysSince_1_1_1900���� NUM_300TH_SECONDS_IN_ONE_MINUTE���� XA_END���� 
totalDataSize���� QUALIFIER_TERM���� ENVCHANGE_CHARSET���� RENAME_TABLE���� isCursorSelect���� CURS_UPDATE���� codePage���� XA_ROLLBACK���� WSID    TDS_NUMERICN���� MAX_TEXT_BYTES���� UNSPECIFIED_DBMS_ERROR���� columnDescriptions���� isClosed���� INIT_DB_WARN���� 
cursorName���� nonUnicodeTransliterator���� CURS_TEXTPTR_ONLY���� 
tdsRequest���� 
DONE_ERROR���� nonUnicodeCharTransliterator     concurrencyOptions���� SAVEPOINT_SUPPORT���� timeout���� RPC_OPTION_NO_METADATA_RETURNED���� 
CURS_OPTCC���� 	FLOAT_VAX���� ENVCHANGE_DATABASE���� returnValue���� 	TDS_IMAGE���� 	SQLCURSOR���� CURS_DELETE���� SQL_ANSI92_TRANS���� 	XA_COMMIT���� DDL_IN_TRANSACTION���� SQLCREATEPROCEDURE���� catalog���� 	footprint    	

 FRACTIONAL_SECOND_TRUNCATION����  lastColumnProcessedForCurrentRow���� cancelRequestSubmitted���� savedStatusFieldPos���� 
CURS_TEXTDATA���� connectionProps���� 
packetizer���� socket���� TDS_BIGVARBINARY���� CURS_UPDATE_KEYSET_INPLACE���� txnConn���� sqlCollation���� INVALID_YEAR���� 
TDS_BIGBINARY���� numBytesReturned���� 	SQLSELECT���� 	TDS_MONEY���� MAX_OWNER_NAME_LENGTH���� CURS_DYNAMIC���� MAX_NTEXT_CHARS���� COLINFO_KEY���� requestedConcurrency���� resultSetConcurrency���� USE_DEFAULT_PARAM_VALUE���� CURS_PARAMETERIZED_SQL���� TDS_DECIMAL���� TABLE_LENGTH���� REMOTE_SPROC���� CURS_FETCH_ADDED���� IDENTIFIER_CASE���� cursorHandleParam���� transliterator���� defaultMaxRows���� CURS_FETCH_MISSING���� UNEXPECTED_LCID���� OFFSET���� TDS_MESSAGE_TYPE_LOGOUT���� 	DONE_ATTN���� unicodeTransliterator���� UNEXPECTED_SORTID���� implConnection���� resultSetScrollType���� processMode���� FLOAT_ND5000���� SQL_ANSI92_INTER���� CURS_FETCH_ENDOFRESULTS���� 	DONE_PROC���� xidBytes���� PROCESS_NORMAL���� DONE_INPROC���� INIT_LANG_FATAL���� MAX_IMAGE_BYTES���� ROW���� TDS_MESSAGE_TYPE_TDS7LOGIN���� mayReturnResultRows���� 
CURS_BY_VALUE���� CURS_SETPOSITION���� 
exceptions   	 
tableNames���� ODBC_OFF���� 	timestamp���� 	TDS_NCHAR���� INIT_LANG_WARN���� value���� NAMED_TRANSACTIONS���� CURS_CURSOR_NAME���� ODBC_ON���� actualLength���� RETURNSTATUS���� INTEGRATED_SECURITY_ON���� NULLABLE_BIT���� TDS_INT1���� DBMS_VER���� CURS_LOCKCC���� %INFO_EXECUTING_SQL_DIRECTLY_NO_CURSOR���� messageType   
 
UDT_TIMESTAMP���� TRAN_BOUNDARY_ON���� TDS_STATUS_FLAG_PARTOFBATCH���� UNEXPECTED_TOKEN_TYPE���� UNHANDLED_TOKEN_TYPE���� 
COLUMN_LENGTH���� CACHE_CONNECTION_ON���� MAXYEAR���� request   	 SPROC_AS_LANGUAGE���� PROCESS_TIL_END���� concurrencyOptionsParam���� longColumnsCount���� 	ORDER_X86���� tdsCursorRequest���� TX_ISOLATION���� INTEGRATED_SECURITY_OFF���� 
procedureName����    enlistConnection/1���� initializeUserParam/4���� openCursor/1���� getInt/1���� getTransliterator/0���� 
intValue/0   	 processReplyToken/2    getNumResultRows/0���� size/0    getParameterCount/0���� setupExceptionHandling/0���� setWriter/1���� getMinutes/0    getException/3    GetNewTransliterator/1���� 
setBytes/2���� unenlistConnection/0���� getResultType/0   	 removeReference/0���� setSoTimeout/1���� getReason/0���� 
unenlist/0���� movePointRight/1���� toBigInteger/0���� set/2���� readUnsignedInt16/0    getExecutingSQLDirectly/0���� getGlobalTransactionId/0���� shortValue/0    getSqlVariantValue/6���� 	valueOf/1   	 getConnectProperties/0���� setCatalog/1���� getColumn/1   	 
getBytes/1���� equals/1    addParameter/1   	 
getFloat/1���� readInt64/0���� setSendPacketWhenFull/1���� getFormatted/0���� max/2���� setup/1���� getColumnDataForRow/2   	 	getByte/1���� substring/1���� encode/1���� getRow/1   	 getInteger/1���� getException/2    getColumnInfoBeforeExecute/0���� getAllParameters/0���� completeRowProcessing/1    	println/1���� getNameForTokenType/1���� signalEndOfPacket/0���� getCharacterStreamReader/3���� 	reverse/1    close/0   	 valueOfDatabaseMetaDataParam/2���� read/0���� getReturnedValue/6    submitRequest/0   	 
allocate/2���� executeXaRpc/1���� registerOutParameter/2���� 	receive/0    getReader/1���� leapCount/1���� changeParameterMarkers/1���� getByName/1���� writeInt64/1���� readString/1    writeInt8/1   
 processInfoToken/1���� 	setType/1���� getBigDecimal/1���� 
toString/1���� getEscapeType/0���� writeStreamWithLength16/2���� getMessage/0    writeAsciiStreamWithLength16/2���� writeReaderWithLength16/2���� processTabName/0���� processServerInfo/0���� 
getBytes/0���� formatCatalogQualifier/1���� 
getHostName/0���� setTDSVersion/1���� postSetupInitialize/0���� longValue/0���� getParameterName/2���� resetCount/0���� get/1   	 decode/1���� 
concatenate/2���� 	getYear/0    mapODBCTypeToJDBCType/1    empty/0���� setReader/1���� open/2���� returnOutputParams/0���� writeStringWithLength16/1���� getOutputParam/1   	 getXAConnection/2���� setSatisfied/0���� getException/1    	setData/2���� signalStartOfPacket/0���� getTDSVersion/0   	 getNextResultSet/0���� regionMatches/5���� 
doubleValue/0���� byteValue/0���� equalsIgnoreCase/1    getLocalHostname/0���� 
toString/0   
	 booleanValue/0   	 setTransactionIsolation/1���� 	indexOf/2���� 
getHours/0���� add/2���� 
getMetaData/0���� getSecondsFraction/0���� getRestriction/1���� encodeNetAddress/0���� getPacketizer/0���� sendCursorFetch/3���� setFetchSize/1   	 getDepacketizer/0   	 getBoolean/1���� startsWith/1���� scale/0���� 
writeString/1    concat/1���� getFunctionType/0���� 
getRootName/2���� getBranchQualifier/0���� getProcedureName/0���� readIEEE32BitFloat/0���� readInt16/0    setMessageType/1    
getMonth/0    	getType/0���� getContent/0���� byteToHex/1���� endOfResultSetReached/1���� setParameter/2���� assert/2    
getFormatId/0���� readIEEE64BitDouble/0���� append/1   
	 processInfoTokenAction/3���� executeUpdate/0���� getException/6���� 
getShort/1���� send/1���� dumpDriverInfo/1���� 
endsWith/1   	 getActualScrollType/0���� getXAConnection/0���� abs/0���� isLeapYear/1���� supportsScrolling/1���� writeInt32/1    discardReplyBytes/0   	 setToBigEndian/0���� writeBytes/1    put/5���� getTransactionIsolation/0���� getStringPreceedingParameter/1���� getTransliteratorForCodePage/1    processReply/1   	 removeAllRestrictions/0���� getConnection/0���� initializeInternalParam/7   	 mapLCIDToCodePage/1���� isUserDefinedType/2���� hasLongColumns/0���� getInetAddress/0���� 	getHour/0���� processErrorToken/0���� getDatabaseProductVersion/0���� mapSortIdToCodePage/1���� useServerSideCursor/0���� uniqueIdentifierToString/1���� sortTypeInfo/2���� getBinaryStream/3���� floatValue/0���� enlist/0���� getParameter/1���� getSeconds/0    mapInstanceNameToPort/2���� compareTo/1    getAsciiStream/1���� 
toByteArray/0���� count/0   	 	setData/1   	 elementAt/1   	 send/0   
 
closeCursor/1���� getExtendedMetaData/0���� addUserParametersToRPC/0���� 
getPosition/0���� readAndDiscardBytes/1    setSQLCollation/1���� getNumRowsAffected/0���� setToLittleEndian/0   	 mapNativeErrorToSQLState/1���� 	getDate/0���� processEnvChange/1���� getServerHostname/0���� 	indexOf/1    getNameOfUnhandledDataType/1���� setPacketSize/1    add/1    readUnsignedInt8/0    	getLong/1���� registerDriver/1���� readUnsignedInt32/0���� getInputStream/1���� add/4���� count/1���� processReturnValue/0���� 
writeLength/3���� setInt/2���� readInt32/0    createTDSDTCRequest/0���� reset/3���� fill/2���� readBytes/3    
parseInt/2���� setPortNumber/1���� getColumnDescriptions/0���� cacheMaxPrecision/0���� ungetTokenType/1���� searchStringCi/2���� 
setPosition/1���� getException/4    encryptPassword/1���� getTimestamp/1���� processColInfo/0���� removeAllElements/0    write/2���� getImplConnection/0   	 getScrollType/0���� processColMetaData/0    getDouble/1���� signum/0���� writeIEEE64BitDouble/1���� 
getXidBytes/1���� getSQLCollation/0���� processOrderToken/0���� writeStreamWithLength32/2���� writeAsciiStreamWithLength32/2���� 	receive/1���� writeReaderWithLength32/2���� arraycopy/5    executeXaRpc/3���� writeIEEE32BitFloat/1���� getTokenType/0���� 
getNanos/0���� isLongColumn/0���� 	getData/0   	 charAt/1    
readInt8/0    setTransliterator/1    setNonUnicodeTransliterator/1    mapCollationToCodePage/1    getString/2���� writeInt16/1   
 getArrayOfBytes/3���� 
prepareCall/1���� clearReplyChannel/0    verifyReturnCode/2���� 
parseInt/1���� writeStringWithLength32/1���� isSimpleSelect/0���� getLocalAddress/0���� 
getBytes/2���� get/2���� addElement/1    getDay/0���� fetchAbsolute/2���� getTDSType/0���� length/0   	 getCatalog/0���� substring/2   	   � sendCursorFetch/3���� getDepacketizer/0���� rollbackTransaction/0���� cacheMaxPrecision/0���� openCursor/1���� processErrorToken/0���� setProgramName/1���� enlistConnection/1���� describeColumns/1���� getTDSType/0     completeRowProcessing/1    
unenlist/0���� 
writeLength/3���� getTransliteratorForCodePage/1���� 
closeCursor/1���� isCursorPositionValid/0���� mapODBCTypeToJDBCType/1���� setup/1���� processColMetaData/0���� 	prepare/0���� setSelectMethod/1���� 	getWSID/0���� 	setWSID/1���� getColumnAccess/0���� mapInstanceNameToPort/2���� getResultSetSQL/3���� commitTransaction/0���� isLongColumn/0     getNumRowsAffected/0���� main/1���� getNextResultType/0���� processReplyToken/2    processInfoTokenAction/3    commit/2���� executeXaRpc/1���� setFetchSize/1    supportsConvert/2���� getImplConnection/0���� getXAConnection/0���� getNumResultRows/0���� encryptPassword/1���� getLocalHostname/0���� ungetTokenType/1���� setSQLCollation/1���� setTransactionTimeout/1���� 
getXidBytes/1���� setReader/1���� processInfoToken/1���� getOutputParam/1���� encodeNetAddress/0���� implLoadProperties/2���� getNameOfUnhandledDataType/1���� send/0���� getSqlVariantValue/6���� postSetupInitialize/0���� isSimpleSelect/0���� setCatalog/1    valueOfDatabaseMetaDataParam/2���� getScrollType/0    getRow/1    setNonUnicodeTransliterator/1���� getNextRowsAffectedCount/0���� signalEndOfPacket/1���� 	getData/0     getSelectMethod/0���� getColumnInfoBeforeExecute/0���� getTranslationNeeded/1���� implGetPropertyNameValuePairs/0���� getConnection/0���� getNextResultSet/0���� 	execute/0���� getXAConnection/2���� close/0   	 	setData/1     getServerHostname/0���� createImplStatement/2���� addUserParametersToRPC/0���� getReturnedValue/6���� supportsScrolling/1���� getSQLCollation/0���� setPacketSize/1���� "setSendStringParametersAsUnicode/1���� 
getTypeInfo/0���� translateCallEscape/1���� clearReplyChannel/0���� getNameForTokenType/1���� unenlistConnection/0���� setTDSType/1     verifyReturnCode/2���� getProgramName/0���� startManualTransactionMode/0���� stopManualTransactionMode/0���� processEnvChange/1���� setMessageType/1   
 changeParameterMarkers/1���� cancel/0���� setTransactionIsolation/1���� processColInfo/0���� formatCatalogQualifier/1���� positionCursor/1    mapLCIDToCodePage/1���� fetchAbsolute/2���� discardReplyBytes/0���� useServerSideCursor/0���� createEscapeTranslator/0���� hasLongColumns/0���� mapCollationToCodePage/1����  initializeNonResultSetMetaData/1���� executeXaRpc/3���� returnOutputParams/0���� mapNativeErrorToSQLState/1���� implAddProperties/1���� sortTypeInfo/2���� 
rollback/1���� read/0���� enlist/0���� setHostProcess/1���� processOrderToken/0���� getTransactionTimeout/0���� setTDSVersion/1���� submitRequest/0    processReply/1    createImplConnection/1���� mapSortIdToCodePage/1���� write/2���� getResultType/0    
getContents/0���� setWriter/1���� getActualScrollType/0���� signalStartOfPacket/0   
 read/3���� reset/3���� getTokenType/0���� translateTimestampEscape/1���� processTabName/0���� getColumnDataForRow/2���� 	recover/1���� getTableTypes/0���� getExtendedMetaData/0���� 	getData/2���� uniqueIdentifierToString/1���� getNetAddress/0���� 	prepare/1���� getTransactionIsolation/0���� getEmptyRowInsertSyntax/0���� getPacketizer/0���� getCatalog/0    getColumn/1���� initializeUserParam/4���� getTDSVersion/0���� signalEndOfPacket/0���� initializeInternalParam/7���� open/0    setNetAddress/1���� enableCancelBasedQueryTimeout/0���� getRmIdentification/0���� getExecutingSQLDirectly/0���� recoverReturnsAllXidsAtOnce/0���� forget/1���� createImplXAResource/1���� addParameter/1���� isUserDefinedType/2���� getColumnDescriptions/0���� getHostProcess/0���� getRequiredSQLProcessing/1���� getParameterName/2���� createImplDatabaseMetaData/1���� "getSendStringParametersAsUnicode/0���� processServerInfo/0���� end/2���� createTDSDTCRequest/0���� getImplPropertyInfo/1���� start/2���� processReturnValue/0���� translateFunctionEscape/1����   	ResultSet   	 usesLocalFilePerTable���� 	tableName���� dataType���� TDS_LOGIN_REQUEST_BASE_LEN���� 	Reference    SQLKeywords���� actualScrollType���� BASEYEAR���� RefAddr���� UtilLocalMessages���� SQLServerDataSourceFactory���� BaseTimestampEscape���� supportsANSI92EntryLevelSQL���� positionedOnValidRow���� typeName    doesMaxRowSizeIncludeBlobs���� connectProps    
localTypeName���� System    #deletesAreDetectedScrollInsensitive���� type���� rowsInTable���� String    	

 isAutoIncrement���� $supportsIntegrityEnhancementFacility���� supportsPositionedDelete���� supportsMinimumSQLGrammar���� 
StringRefAddr���� 
UtilException   
 %dataDefinitionCausesTransactionCommit���� maxPrecision   	 defaultMaxFieldSize   	 allProceduresAreCallable���� sql   	 Vector   	 
USE_DB_OFF���� isSigned���� int[]    extraNameCharacters���� Reader    #TDSConnection$TransliteratorForLCID    TransliteratorForLCID    int    	
 maxCharLiteralLength����  supportsAlterTableWithDropColumn���� XAException���� BaseXid���� USER_NORMAL���� databaseName���� 
procedureTerm���� maxBinaryLiteralLength���� savedByteValue���� /supportsTransactionIsolationLevelRepeatableRead���� /supportsTransactionIsolationLevelReadUncommited���� maximumScale���� maxColumnsInGroupBy���� SQL_ANSI_V1���� depacketizer���� 	fetchSize   	 minimumScale���� BaseResultSetFilterDescriptor���� 
BaseColumn   	 commandText���� allTablesAreSelectable���� typesForTDS_07000000���� numRowsParam���� 
parameters    BASEBIAS���� SQL_ANSI92_FULL���� SQL_DFLT���� maxColumnsInSelect���� util   	
 useServerSideCursor   	 databaseMetaDataResultSet   	 !BaseImplResultSetNotificationSink���� databaseProductVersion���� Long   	 String[]    requestedScrollType���� BaseDataSourceFactory���� writer   	  storesLowerCaseQuotedIdentifiers����  storesMixedCaseQuotedIdentifiers���� #dataDefinitionIgnoredInTransactions����  storesUpperCaseQuotedIdentifiers���� "supportsMixedCaseQuotedIdentifiers���� 
BaseParameter   	 num300thSecondsSinceMidnight    typesForTDS_07010000���� 	precision    catalogTerm���� err���� supportsColumnAliasing���� BaseTableTypes���� USER_SERVER���� Types    UtilByteOrderedDataWriter   	 	exception    numReturnedParameters���� cursorHandle���� InetAddress����  supportsCatalogsInProcedureCalls���� supportsSchemasInProcedureCalls���� supportsCorrelatedSubqueries���� maxCatalogNameLength���� maxTablesInSelect���� BaseImplXAResource    nullsAreSortedAtEnd���� USER_SQLREPL���� identifierQuoteString���� transaction���� supportsMultipleResultSets���� CallableStatement���� isHidden���� Math���� BaseFunctionEscape���� $supportsOpenStatementsAcrossRollback���� !supportsOpenCursorsAcrossRollback���� socketProvider���� supportsPositionedUpdate���� noMoreDataToRead���� BaseExceptions   	 DatabaseMetaData    nullPlusNonNullIsNull���� UtilStringFunctions���� 	resultSet����  SQLServerPacketizingDataConsumer   
 maxCursorNameLength���� TDSDateTime    maxUserNameLength���� transliterators���� executingSQLDirectly���� DUMPLOAD_OFF���� nativeTypeName   	 DUMPLOAD_ON���� "SQLServerDepacketizingDataProvider    !supportsSchemasInIndexDefinitions���� "supportsCatalogsInIndexDefinitions���� 
BaseDriver���� SQLServerConnection   	 
literalSuffix���� supportsOuterJoins���� FLOAT_IEEE_754���� socketConsumer���� SQLServerImplStatement   	 supportsLikeEscapeClause���� SQLServerLicense���� reader   	 boolean    	
 quotingChar���� 
schemaTerm���� 
cursorType���� supportsNonNullableColumns���� 
tdsVersion    baseDataType   	 dataProvider���� supportsSubqueriesInQuantifieds���� BaseColumns   	 isInput���� fixedPrecScale���� supportsSubqueriesInComparisons���� 
SQL_ANSI89_L1���� %supportsSchemasInPrivilegeDefinitions���� &supportsCatalogsInPrivilegeDefinitions���� PreparedStatement���� maxFieldSize   	 sqlEx���� returnValueType    supportsExpressionsInOrderBy���� BaseSQL���� maxStatementLength���� nXids���� supportsFullOuterJoins���� IntegerZero���� 
resultType    numRowsFetchedFromResultSet    MAX_NTEXT_BYTES���� BaseData   	 nullsAreSortedAtStart���� TDSExecuteRequest   	 BaseClassUtility���� 	isServer7���� 
searchable���� supportsStoredProcedures���� 
INIT_DB_FATAL���� &SQLServerImplResultSetServerSideCursor   	 supportsCoreSQLGrammar���� this$0���� password���� supportsTableCorrelationNames���� &supportsDifferentTableCorrelationNames���� warnings   	 SQL_ANSI92_ENTRY���� supportsBatchUpdates���� Xid[]���� supportsExtendedSQLGrammar���� createParams���� sendStringParametersAsUnicode   	 Integer   	 notificationSink���� 
headerBuff���� 	UtilDebug    
JulianDays���� 
caseSensitive���� supportsANSI92IntermediateSQL���� selectMethod    ds���� 	microsoft    	

 Double    isCaseSensitive���� supportsSelectForUpdate���� BaseImplStatement   	 supportsUnionAll���� SQLException   	 supportsSubqueriesInIns���� maxColumnNameLength���� 	USE_DB_ON���� UtilTransliteratorForUCS2    hasProcReturnParam���� "supportsCatalogsInTableDefinitions���� !supportsSchemasInTableDefinitions���� dataConsumerObject���� UtilBufferedDataConsumer   
 TDSLogoutRequest���� SET_LANG_ON���� resultTypeReported���� 	paramName   	 BaseResultSetSortDescriptor���� ORDER_68000���� byte[]    supportsMixedCaseIdentifiers���� storesUpperCaseIdentifiers���� storesMixedCaseIdentifiers���� storesLowerCaseIdentifiers���� DEFAULT_PACKET_SIZE���� 
UtilByteArray    	errorCode���� UtilBufferedDataProvider���� 
connection   	 tdsType���� ResultSetMetaData���� resultTypeHasBeenRequested���� lastRowReturned���� supportsOrderByUnrelated���� cookie���� $supportsResultSetTypeScrollSensitive���� isDefinitelyWritable���� supportsGroupBy���� displaySize   	 
MAXNICSIZE���� 
TDSRequest   	 UtilPacketizingDataConsumer   
 TDSRPCParameter   	 SQL_ANSI89_IEF���� rowNumberParam���� bufferedConsumer���� databaseMetaData���� isOutput    Short    conn   	 
isReadOnly    
BigDecimal    CACHE_CONNECTION_OFF���� NullPointerException���� maxTableNameLength���� numericFunctions���� supportsConvert���� BaseParameters���� Xid���� BaseTypeInfo���� SQLServerEscapeTranslator    IOException    bufferedProvider���� io    UtilDepacketizingDataProvider���� TDSCancelRequest   	 SET_LANG_OFF���� username���� 
autoIncrement���� TDSType     numBytesInCurrentPacket���� BaseCharacterStreamWrapper���� Byte    lookaheadTokenType���� rowsInTableParam���� 
CHARSET_ASCII���� CHARSET_EBCDIC���� BaseWarnings    data     TDSLoginRequest    BaseInputStreamWrapper���� BaseCallEscape���� SQLServerDriver���� 
implStatement���� statusField���� cursorTypeParam���� numBytesReadFromCurrentPacket���� lang    	 supportsAlterTableWithAddColumn���� name���� BaseLicense���� long    supportsLimitedOuterJoins���� Socket���� BaseConnectionProperties    
Object[][]���� 
literalPrefix���� numPrecRadix���� 
BaseEscape���� 
BigInteger    supportsMultipleTransactions���� catalogSeparator���� numRowsAffected���� enlisted���� 
netAddress    SQL_TSQL���� 
isWritable���� XP_XA���� TRAN_BOUNDARY_OFF���� maxColumnsInOrderBy���� 
TDSRPCRequest   	 UtilTransliterator     programName    hostProcess    com    	

 maxIndexLength���� 
SQL_ANSI89_L2���� scale    void    	
 timeDateFunctions���� BaseEscapeTranslator    
RPCOptions    searchStringEscape���� systemFunctions���� nullsAreSortedHigh���� UtilDataProvider���� math    -supportsTransactionIsolationLevelSerializable���� net���� -supportsTransactionIsolationLevelReadCommited���� maxRows   	 	Timestamp    	maxLength���� XAConnection���� USER_REMUSER���� 
nativeColumns���� InputStream    SQLServerColumn    	 &supportsResultSetTypeScrollInsensitive���� isUCS2Stream���� numDaysSince_1_1_1900    supportsANSI92FullSQL���� 
TDSDTCRequest    isSearchable���� 
totalDataSize���� NumberFormatException���� 
TDSConnection   	 isCursorSelect���� !insertsAreDetectedScrollSensitive���� TDSCursorRequest   	 &othersDeletesAreVisibleScrollSensitive���� !updatesAreDetectedScrollSensitive���� &othersInsertsAreVisibleScrollSensitive���� &othersUpdatesAreVisibleScrollSensitive���� #ownDeletesAreVisibleScrollSensitive���� #ownInsertsAreVisibleScrollSensitive���� #ownUpdatesAreVisibleScrollSensitive���� WSID    maxSchemaNameLength���� codePage���� MAX_TEXT_BYTES���� columnDescriptions���� isClosed    INIT_DB_WARN���� 
cursorName���� nonUnicodeTransliterator    DatagramSocket���� short    
tdsRequest���� nonUnicodeCharTransliterator     concurrencyOptions���� 
XAResource���� timeout���� nullable���� 	FLOAT_VAX���� returnValue   	 !supportsSchemasInDataManipulation���� "supportsCatalogsInDataManipulation���� SQLServerImplResultSet   	 SQL_ANSI92_TRANS���� maxConnections���� 
maxStatements���� 
supportsUnion���� 
BaseTypeInfos���� 
maxRowSize���� 	footprint    	

 catalog����  lastColumnProcessedForCurrentRow���� cancelRequestSubmitted    SQLServerLocalMessages    Float    SQLServerImplConnection   	 savedStatusFieldPos���� TDSConstants   
 	
 connectionProps���� 
packetizer���� 5supportsDataDefinitionAndDataManipulationTransactions���� UtilSocketDataConsumer���� socket���� isKey���� maxColumnsInIndex���� txnConn���� sqlCollation���� 
parameterSets���� "supportsOpenStatementsAcrossCommit���� numBytesReturned���� supportsOpenCursorsAcrossCommit���� Object   	  numPacketsCreatedSinceSend���� MAX_NTEXT_CHARS���� requestedConcurrency���� resultSetConcurrency���� BaseXADataSource���� UtilSocketDataProvider���� insertsAreDetectedForwardOnly���� updatesAreDetectedForwardOnly���� SQLServerImplXAResource    maxColumnsInTable���� !deletesAreDetectedScrollSensitive���� supportsGroupByUnrelated���� cursorHandleParam���� transliterator    databaseProductName���� defaultMaxRows���� jdbcx    stringFunctions���� UtilDateAndTimeFunctions���� BaseDriverPropertyInfos���� supportsGroupByBeyondSelect���� unicodeTransliterator    xa���� BaseDataSource    implConnection   	 Boolean   	 resultSetScrollType���� processMode    FLOAT_ND5000���� SQL_ANSI92_INTER���� sqlType���� xidBytes���� SQLServerLongDataStream���� javax    StringBuffer   
	 INIT_LANG_FATAL���� SQLServerImplDatabaseMetaData    MAX_IMAGE_BYTES���� BaseImplResultSet   	 label���� mayReturnResultRows���� BaseImplConnection    
tableNames���� 
exceptions   
	 ODBC_OFF���� 	timestamp    BaseConnection    INIT_LANG_WARN���� BaseLocalMessages    	sqlserver    	

 value   	 
isCurrency���� supportsTransactions���� ODBC_ON���� base   	
 actualLength���� #insertsAreDetectedScrollInsensitive���� INTEGRATED_SECURITY_ON���� (supportsDataManipulationTransactionsOnly���� #updatesAreDetectedScrollInsensitive���� %ownUpdatesAreVisibleScrollInsensitive���� isCatalogAtStart���� unsignedAttribute���� %ownInsertsAreVisibleScrollInsensitive���� %ownDeletesAreVisibleScrollInsensitive���� 	Throwable    (othersUpdatesAreVisibleScrollInsensitive���� (othersInsertsAreVisibleScrollInsensitive���� (othersDeletesAreVisibleScrollInsensitive���� 
isNullable���� tds    	
 UtilDataConversions���� messageType   	
 	Statement���� PrintStream���� UtilByteArrayDataProvider   	 	Exception   	 deletesAreDetectedForwardOnly���� naming    jdbc    	

 TRAN_BOUNDARY_ON���� maxProcedureNameLength���� byte     supportsSubqueriesInExists���� UtilByteOrderedDataReader   	 CACHE_CONNECTION_ON���� nullsAreSortedLow���� MAXYEAR���� request   	 SQLServerDataSource    java    	 
Connection    Date���� concurrencyOptionsParam���� longColumnsCount���� 	ORDER_X86���� tdsCursorRequest���� "othersDeletesAreVisibleForwardOnly���� "othersInsertsAreVisibleForwardOnly���� "othersUpdatesAreVisibleForwardOnly���� BaseImplDatabaseMetaData   	 usesLocalFiles���� ownDeletesAreVisibleForwardOnly���� ownInsertsAreVisibleForwardOnly���� ownUpdatesAreVisibleForwardOnly���� defaultTransactionIsolation���� INTEGRATED_SECURITY_OFF���� 
procedureName    DatagramPacket����    �TDSCursorRequest/7/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;Ljava\lang\String;IIZ)V// ���� qSQLServerLongDataStream/3/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\util\UtilByteOrderedDataReader;ZJ)V// ���� uTDSDateTime/2/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\base\BaseExceptions;Ljava\sql\Timestamp;)V// ���� sSQLServerImplConnection/1/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\jdbc\sqlserver\SQLServerConnection;)V// ���� �SQLServerImplXAResource/2/!��/com.microsoft.jdbcx.sqlserver/(Lcom\microsoft\jdbcx\sqlserver\SQLServerDataSource;Lcom\microsoft\jdbc\base\BaseConnection;)V// ���� �TDSRequest/4/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;I)V// ���� 5TDSConstants/0/! /com.microsoft.jdbc.sqlserver.tds/ ���� wSQLServerPacketizingDataConsumer/1/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\util\UtilBufferedDataConsumer;)V// ���� ySQLServerImplDatabaseMetaData/1/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\jdbc\base\BaseConnectionProperties;)V// ���� mSQLServerImplResultSet/1/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\jdbc\sqlserver\tds\TDSRequest;)V// ���� ;TDSDateTime/2/!��/com.microsoft.jdbc.sqlserver.tds/(II)V// ����HTDSLoginRequest/10/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;Ljava\lang\String;Ljava\lang\String;Ljava\lang\String;Ljava\lang\String;Ljava\lang\String;Ljava\lang\String;Ljava\lang\String;)V// ���� �TDSRPCRequest/4/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;Ljava\lang\String;)V// ���� �TDSConnection/3/!��/com.microsoft.jdbc.sqlserver.tds/(Ljava\lang\String;Ljava\lang\String;Lcom\microsoft\jdbc\base\BaseExceptions;)V// ���� gTDSRPCParameter/3/!��/com.microsoft.jdbc.sqlserver.tds/(IILcom\microsoft\jdbc\base\BaseExceptions;)V// ���� �TDSCancelRequest/3/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;)V// ���� �TDSDTCRequest/3/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;)V// ���� �TDSLogoutRequest/3/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;)V// ���� qSQLServerDepacketizingDataProvider/1/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\util\UtilDataProvider;)V// ���� 4SQLServerDriver/0/! /com.microsoft.jdbc.sqlserver/ ���� >SQLServerEscapeTranslator/0/! /com.microsoft.jdbc.sqlserver/ ���� @SQLServerDataSourceFactory/0/! /com.microsoft.jdbcx.sqlserver/ ���� 9SQLServerDataSource/0/! /com.microsoft.jdbcx.sqlserver/ ���� ;SQLServerLocalMessages/0/! /com.microsoft.jdbc.sqlserver/ ���� �SQLServerImplStatement/4/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;IILcom\microsoft\jdbc\base\BaseExceptions;)V// ���� �SQLServerImplResultSetServerSideCursor/1/!��/com.microsoft.jdbc.sqlserver/(Lcom\microsoft\jdbc\sqlserver\tds\TDSCursorRequest;)V// ���� �TDSExecuteRequest/5/!��/com.microsoft.jdbc.sqlserver.tds/(Lcom\microsoft\jdbc\sqlserver\tds\TDSConnection;Lcom\microsoft\util\UtilByteOrderedDataReader;Lcom\microsoft\util\UtilByteOrderedDataWriter;Ljava\lang\String;Z)V// ���� 5SQLServerLicense/0/! /com.microsoft.jdbc.sqlserver/ ���� 8SQLServerConnection/0/! /com.microsoft.jdbc.sqlserver/ ���� 4SQLServerColumn/0/! /com.microsoft.jdbc.sqlserver/      TransliteratorForLCID/0/������   P BaseEscapeTranslator/0���� SQLServerDriver/0���� 
BaseData/1���� SQLServerConnection/0���� UtilByteOrderedDataReader/2   	 
InputStream/0���� String/3���� SQLServerImplConnection/1���� BigInteger/2���� BigDecimal/2���� String/0   	 BigInteger/1���� $SQLServerDepacketizingDataProvider/1���� BaseConnection/0���� BaseXADataSource/0���� 	Boolean/1    TDSConnection/3���� TDSDTCRequest/3���� TDSLoginRequest/10���� 
BaseData/2���� TDSCancelRequest/3���� Vector/0    Timestamp/7���� TDSCursorRequest/7���� SQLServerImplXAResource/2���� StringRefAddr/2���� BaseColumn/0���� Short/1    
BaseColumns/0���� Double/1    DatagramPacket/4���� TDSRPCParameter/3   	 BaseDriver/0���� BaseImplXAResource/0���� 	BaseXid/3���� TDSExecuteRequest/5   	 "SQLServerPacketizingDataConsumer/1���� BaseTypeInfo/0���� BaseTableTypes/0���� BaseImplResultSet/0���� UtilBufferedDataProvider/1���� UtilDepacketizingDataProvider/1���� (SQLServerImplResultSetServerSideCursor/1���� SQLServerImplResultSet/1   	 Object/0     BaseTypeInfos/0���� SQLServerColumn/0���� BaseImplConnection/0���� UtilSocketDataProvider/1���� 	Integer/1    %TDSConnection$TransliteratorForLCID/0���� TransliteratorForLCID/0���� BaseDataSourceFactory/0���� UtilByteArrayDataProvider/1   	 UtilPacketizingDataConsumer/2���� Float/1    
TDSDateTime/2    BaseExceptions/1���� SQLServerEscapeTranslator/0���� DatagramPacket/2���� 
XAException/1���� TDSRPCRequest/4   	 Long/1   	 
BaseLicense/0���� String/1   	 Byte/1    UtilBufferedDataConsumer/1���� DatagramSocket/0���� SQLException/1���� TDSRequest/4    StringBuffer/1   
	 SQLServerImplStatement/4���� UtilByteOrderedDataWriter/2���� Vector/1���� BaseImplStatement/0���� BaseLocalMessages/0���� BaseImplDatabaseMetaData/0���� UtilSocketDataConsumer/1���� Socket/2���� SQLServerImplDatabaseMetaData/1����    GTransliteratorForLCID/com.microsoft.jdbc.sqlserver.tds/TDSConnection/ ���� 1SQLServerLicense/com.microsoft.jdbc.sqlserver//! ���� 4SQLServerConnection/com.microsoft.jdbc.sqlserver//! ���� /TDSRequest/com.microsoft.jdbc.sqlserver.tds//! ���� 5TDSCancelRequest/com.microsoft.jdbc.sqlserver.tds//! ���� 0SQLServerColumn/com.microsoft.jdbc.sqlserver//!      8SQLServerImplConnection/com.microsoft.jdbc.sqlserver//! ���� 7SQLServerImplResultSet/com.microsoft.jdbc.sqlserver//! ���� 0SQLServerDriver/com.microsoft.jdbc.sqlserver//! ���� 4TDSLoginRequest/com.microsoft.jdbc.sqlserver.tds//! ���� :SQLServerEscapeTranslator/com.microsoft.jdbc.sqlserver//! ���� 5TDSCursorRequest/com.microsoft.jdbc.sqlserver.tds//! ���� 6TDSExecuteRequest/com.microsoft.jdbc.sqlserver.tds//! ���� 2TDSDTCRequest/com.microsoft.jdbc.sqlserver.tds//! ���� 7SQLServerImplStatement/com.microsoft.jdbc.sqlserver//! ���� 8SQLServerLongDataStream/com.microsoft.jdbc.sqlserver//! ���� >SQLServerImplDatabaseMetaData/com.microsoft.jdbc.sqlserver//! ���� GSQLServerImplResultSetServerSideCursor/com.microsoft.jdbc.sqlserver//! ���� 7SQLServerLocalMessages/com.microsoft.jdbc.sqlserver//! ���� ASQLServerPacketizingDataConsumer/com.microsoft.jdbc.sqlserver//! ���� 5TDSLogoutRequest/com.microsoft.jdbc.sqlserver.tds//! ���� 2TDSRPCRequest/com.microsoft.jdbc.sqlserver.tds//! ���� 9SQLServerImplXAResource/com.microsoft.jdbcx.sqlserver//! ���� <SQLServerDataSourceFactory/com.microsoft.jdbcx.sqlserver//! ���� 5SQLServerDataSource/com.microsoft.jdbcx.sqlserver//! ���� 0TDSDateTime/com.microsoft.jdbc.sqlserver.tds//! ���� 2TDSConnection/com.microsoft.jdbc.sqlserver.tds//! ���� 1TDSConstants/com.microsoft.jdbc.sqlserver.tds//! ���� CSQLServerDepacketizingDataProvider/com.microsoft.jdbc.sqlserver//! ���� 4TDSRPCParameter/com.microsoft.jdbc.sqlserver.tds//! ����    ]BaseConnection/com.microsoft.jdbc.base/SQLServerConnection///com.microsoft.jdbc.sqlserver/CC!���� eBaseImplConnection/com.microsoft.jdbc.base/SQLServerImplConnection///com.microsoft.jdbc.sqlserver/CC!���� cBaseImplResultSet/com.microsoft.jdbc.base/SQLServerImplResultSet///com.microsoft.jdbc.sqlserver/CC!���� UBaseDriver/com.microsoft.jdbc.base/SQLServerDriver///com.microsoft.jdbc.sqlserver/CC!���� iBaseEscapeTranslator/com.microsoft.jdbc.base/SQLServerEscapeTranslator///com.microsoft.jdbc.sqlserver/CC!���� cBaseImplStatement/com.microsoft.jdbc.base/SQLServerImplStatement///com.microsoft.jdbc.sqlserver/CC!���� qBaseImplDatabaseMetaData/com.microsoft.jdbc.base/SQLServerImplDatabaseMetaData///com.microsoft.jdbc.sqlserver/CC!���� cBaseLocalMessages/com.microsoft.jdbc.base/SQLServerLocalMessages///com.microsoft.jdbc.sqlserver/CC!���� bSQLServerImplResultSet/com.microsoft.jdbc.sqlserver/SQLServerImplResultSetServerSideCursor///0/CC!���� NInputStream/java.io/SQLServerLongDataStream///com.microsoft.jdbc.sqlserver/CC!���� aBaseXADataSource/com.microsoft.jdbcx.base/SQLServerDataSource///com.microsoft.jdbcx.sqlserver/CC!���� gBaseImplXAResource/com.microsoft.jdbcx.base/SQLServerImplXAResource///com.microsoft.jdbcx.sqlserver/CC!���� mBaseDataSourceFactory/com.microsoft.jdbcx.base/SQLServerDataSourceFactory///com.microsoft.jdbcx.sqlserver/CC!���� CObject/java.lang/SQLServerColumn///com.microsoft.jdbc.sqlserver/CC!     BObject/java.lang/TDSRequest///com.microsoft.jdbc.sqlserver.tds/CC!���� CObject/java.lang/TDSDateTime///com.microsoft.jdbc.sqlserver.tds/CC!���� EObject/java.lang/TDSConnection///com.microsoft.jdbc.sqlserver.tds/CC!���� DObject/java.lang/TDSConstants///com.microsoft.jdbc.sqlserver.tds/CC!���� GObject/java.lang/TDSRPCParameter///com.microsoft.jdbc.sqlserver.tds/CC!���� rUtilPacketizingDataConsumer/com.microsoft.util/SQLServerPacketizingDataConsumer///com.microsoft.jdbc.sqlserver/CC!���� vUtilDepacketizingDataProvider/com.microsoft.util/SQLServerDepacketizingDataProvider///com.microsoft.jdbc.sqlserver/CC!���� GTDSRPCRequest/com.microsoft.jdbc.sqlserver.tds/TDSCursorRequest///0/CC!���� ATDSRequest/com.microsoft.jdbc.sqlserver.tds/TDSDTCRequest///0/CC!���� ETDSRequest/com.microsoft.jdbc.sqlserver.tds/TDSExecuteRequest///0/CC!���� DTDSRequest/com.microsoft.jdbc.sqlserver.tds/TDSCancelRequest///0/CC!���� CTDSRequest/com.microsoft.jdbc.sqlserver.tds/TDSLoginRequest///0/CC!���� ZObject/java.lang/TransliteratorForLCID/TDSConnection//com.microsoft.jdbc.sqlserver.tds/CC���� DTDSRequest/com.microsoft.jdbc.sqlserver.tds/TDSLogoutRequest///0/CC!���� ATDSRequest/com.microsoft.jdbc.sqlserver.tds/TDSRPCRequest///0/CC!���� WBaseLicense/com.microsoft.jdbc.base/SQLServerLicense///com.microsoft.jdbc.sqlserver/CC!����   |         	fieldDecl   	methodRef  "� 
methodDecl  ;a ref  K� constructorDecl  ~� constructorRef  �� typeDecl  �V superRef  �