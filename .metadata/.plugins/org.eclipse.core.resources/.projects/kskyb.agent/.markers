    /kskyb.agent          \ ,org.eclipse.jdt.launching.jreContainerMarker  message �Build path specifies execution environment JavaSE-1.6. There are no JREs installed in the workspace that are strictly compatible with this environment.  severity    location 
Build path  L�{쫄 4/kskyb.agent/src/freemarker/template/SimpleHash.java            org.eclipse.jdt.core.task  sourceId JDT 	charStart  8 userEditable  charEnd  W id  � message TODO: Maybe we should log here. priority    
lineNumber   �  L�{�w ;/kskyb.agent/src/freemarker/ext/beans/OverloadedMethod.java               sourceId JDT 	charStart  � userEditable  charEnd  � id  � message TODO: make it not concurrent priority    
lineNumber   H  L�{뱃 1/kskyb.agent/src/freemarker/core/Environment.java               sourceId JDT 	charStart  Y� userEditable  charEnd  Z id  � message 0TODO : check 異�媛� 議곌굔�� ���� true�쇨�? priority    
lineNumber  $  L�{�A            sourceId JDT 	charStart  ^X userEditable  charEnd  ^v id  � message 0TODO : check 異�媛� 議곌굔�� ���� true�쇨�? priority    
lineNumber  D  L�{�A C/kskyb.agent/src/com/kskyb/magent/task/SmsLogBufferMonitorTask.java               sourceId JDT 	charStart  � userEditable  charEnd   id  � message TODO Auto-generated catch block priority    
lineNumber   �  L�{� C/kskyb.agent/src/com/kskyb/magent/task/MmsLogBufferMonitorTask.java               sourceId JDT 	charStart  9� userEditable  charEnd  : id  � message TODO Auto-generated catch block priority    
lineNumber  �  L�{�J C/kskyb.agent/src/com/kskyb/magent/struct/SMSProtocolController.java               sourceId JDT 	charStart  � userEditable  charEnd  � id  � message TODO struct -> byte array priority    
lineNumber  3  L�{�X J/kskyb.agent/src/com/kskyb/magent/manage/BufferStatusEchoCommandEntry.java               sourceId JDT 	charStart  
b userEditable  charEnd  
� id  � message [TODO : 踰��쇰�� ��踰�留� �ъ�⑺����濡� CommonUtils�� 濡�吏�怨� �④� 蹂�寃쏀�댁�쇳��. priority    
lineNumber   U  L�{�q ?/kskyb.agent/src/com/kskyb/broker/util/param/CommonRequest.java               sourceId JDT 	charStart  - userEditable  charEnd  = id  � message "TODO 由ъ���ㅻ�� ��由ы�� �댁�� priority    
lineNumber   �  L�{싹 O/kskyb.agent/src/com/kskyb/broker/util/jdbc/datasource/DataSourceStatement.java          
 org.eclipse.jdt.core.problem 	 severity    sourceId JDT 	charStart  v charEnd  � 	arguments f4:isCloseOnCompletion#   #java.sql.Statement#com.kskyb.broker.util.jdbc.datasource.DataSourceStatement id � message iThe type DataSourceStatement must implement the inherited abstract method Statement.isCloseOnCompletion() 
lineNumber    
categoryId   2  L�{�I       	    	 severity    sourceId JDT 	charStart  v charEnd  � 	arguments d4:closeOnCompletion#   #java.sql.Statement#com.kskyb.broker.util.jdbc.datasource.DataSourceStatement id � message gThe type DataSourceStatement must implement the inherited abstract method Statement.closeOnCompletion() 
lineNumber    
categoryId   2  L�{�I O/kskyb.agent/src/com/kskyb/broker/util/jdbc/datasource/DataSourceResultSet.java              	 severity    sourceId JDT 	charStart  � charEnd  � 	arguments }4:getObject#java.lang.String, java.lang.Class<T>#java.sql.ResultSet#com.kskyb.broker.util.jdbc.datasource.DataSourceResultSet id � message oThe type DataSourceResultSet must implement the inherited abstract method ResultSet.getObject(String, Class<T>) 
lineNumber    
categoryId   2  L�{�V           	 severity    sourceId JDT 	charStart  � charEnd  � 	arguments p4:getObject#int, java.lang.Class<T>#java.sql.ResultSet#com.kskyb.broker.util.jdbc.datasource.DataSourceResultSet id � message lThe type DataSourceResultSet must implement the inherited abstract method ResultSet.getObject(int, Class<T>) 
lineNumber    
categoryId   2  L�{�V P/kskyb.agent/src/com/kskyb/broker/util/jdbc/datasource/DataSourceConnection.java          
    	 severity    sourceId JDT 	charStart   charEnd  ( 	arguments k4:setSchema#java.lang.String#java.sql.Connection#com.kskyb.broker.util.jdbc.datasource.DataSourceConnection id � message gThe type DataSourceConnection must implement the inherited abstract method Connection.setSchema(String) 
lineNumber    
categoryId   2  L�{�g           	 severity    sourceId JDT 	charStart   charEnd  ( 	arguments f4:getNetworkTimeout#   #java.sql.Connection#com.kskyb.broker.util.jdbc.datasource.DataSourceConnection id � message iThe type DataSourceConnection must implement the inherited abstract method Connection.getNetworkTimeout() 
lineNumber    
categoryId   2  L�{�g           	 severity    sourceId JDT 	charStart   charEnd  ( 	arguments ^4:getSchema#   #java.sql.Connection#com.kskyb.broker.util.jdbc.datasource.DataSourceConnection id � message aThe type DataSourceConnection must implement the inherited abstract method Connection.getSchema() 
lineNumber    
categoryId   2  L�{�h           	 severity    sourceId JDT 	charStart   charEnd  ( 	arguments t4:abort#java.util.concurrent.Executor#java.sql.Connection#com.kskyb.broker.util.jdbc.datasource.DataSourceConnection id � message eThe type DataSourceConnection must implement the inherited abstract method Connection.abort(Executor) 
lineNumber    
categoryId   2  L�{�h           	 severity    sourceId JDT 	charStart   charEnd  ( 	arguments �4:setNetworkTimeout#java.util.concurrent.Executor, int#java.sql.Connection#com.kskyb.broker.util.jdbc.datasource.DataSourceConnection id � message vThe type DataSourceConnection must implement the inherited abstract method Connection.setNetworkTimeout(Executor, int) 
lineNumber    
categoryId   2  L�{�h W/kskyb.agent/src/com/kskyb/broker/util/jdbc/datasource/DataSourceCallableStatement.java              	 severity    sourceId JDT 	charStart  + charEnd  F 	arguments �4:getObject#java.lang.String, java.lang.Class<T>#java.sql.CallableStatement#com.kskyb.broker.util.jdbc.datasource.DataSourceCallableStatement id � message The type DataSourceCallableStatement must implement the inherited abstract method CallableStatement.getObject(String, Class<T>) 
lineNumber    
categoryId   2  L�{�q           	 severity    sourceId JDT 	charStart  + charEnd  F 	arguments �4:getObject#int, java.lang.Class<T>#java.sql.CallableStatement#com.kskyb.broker.util.jdbc.datasource.DataSourceCallableStatement id � message |The type DataSourceCallableStatement must implement the inherited abstract method CallableStatement.getObject(int, Class<T>) 
lineNumber    
categoryId   2  L�{�r N/kskyb.agent/src/com/kskyb/broker/util/jdbc/DeligatePreparedStatementInfo.java              	 severity    sourceId JDT 	charStart  "� charEnd  "� 	arguments 1:file id  w message %Resource leak: 'file' is never closed 
lineNumber  > 
categoryId   Z  L�{�            sourceId JDT 	charStart   userEditable  charEnd  < id  � message [TODO: 留ㅽ�� �몃�깆�ㅻ�� 蹂�寃쏀�� �� ����濡� ��寃쎈���? �뱀�� setter 湲곕�� 異�媛� priority    
lineNumber   '  L�{� N/kskyb.agent/src/com/kskyb/broker/util/convertor/ParameterFunctionContext.java               sourceId JDT 	charStart  2 userEditable  charEnd  P id  � message 2TODO : test case 留��ㅼ�댁�� ���ㅽ�� �댁�쇳��. priority    
lineNumber     L�{� 7/kskyb.agent/src/com/kskyb/broker/util/cache/Cache.java               sourceId JDT 	charStart  � userEditable  charEnd  � id  � message )TODO ���μ�� ���ш� 諛������ㅻ㈃? priority    
lineNumber   �  L�{쑴 A/kskyb.agent/src/com/kskyb/broker/util/buffer/ArrayBaseStack.java               sourceId JDT 	charStart  I userEditable  charEnd  u id  � message fTODO: �������μ�� 寃쎌�� 鍮��댁���� 踰��쇰�� ���뱁�� ������ 以��ъ＜�� 濡�吏��� ����. priority    
lineNumber   7  L�{씀 A/kskyb.agent/src/com/kskyb/broker/util/buffer/ArrayBaseQueue.java               sourceId JDT 	charStart  & userEditable  charEnd  R id  � message fTODO: �������μ�� 寃쎌�� 鍮��댁���� 踰��쇰�� ���뱁�� ������ 以��ъ＜�� 濡�吏��� ����. priority    
lineNumber   9  L�{씁 5/kskyb.agent/src/com/kskyb/broker/util/Utilities.java               sourceId JDT 	charStart  j userEditable  charEnd  � id  � message ;TODO : primitive type�� ���� 蹂����� �ｌ�댁�� ����. priority    
lineNumber   .  L�{씬 A/kskyb.agent/src/com/kskyb/broker/util/SimpleStringTokenizer.java               sourceId JDT 	charStart  � userEditable  charEnd  , id  � message 1TODO : test for performance use recycle or direct priority    
lineNumber     L�{앤 ?/kskyb.agent/src/com/kskyb/broker/util/MapDelegateSelector.java               sourceId JDT 	charStart   � userEditable  charEnd  	 id  � message STODO:selector���� 蹂�寃쎌�� �쇱�대�� 遺�遺��� ��蹂몄�� 諛�����吏� ����<br> priority    
lineNumber     L�{얏 A/kskyb.agent/src/com/kskyb/broker/util/HashedObjectContainer.java               sourceId JDT 	charStart   � userEditable  charEnd  5 id  � message xTODO : 吏���移�寃� 留��� �몄�ㅽ�댁�ㅺ� ���깅���ㅻ㈃ �ㅻ���� �몄�ㅽ�댁�� ��嫄� 猷⑦�댁�� �ㅼ�닿��쇳��. <br> priority    
lineNumber     L�{엊 4/kskyb.agent/src/com/kskyb/broker/util/DateUtil.java               sourceId JDT 	charStart  /� userEditable  charEnd  /� id  � message TODO : range check priority    
lineNumber  W  L�{� ;/kskyb.agent/src/com/kskyb/broker/util/CommonConfigure.java               sourceId JDT 	charStart  | userEditable  charEnd  � id  � message mTODO : default濡� 寃������� �댁�⑹�� ���ν���� service 紐⑤���곗�� ���� 媛��λ��濡� �댁�쇳��.<br> priority    
lineNumber     L�{� A/kskyb.agent/src/com/kskyb/broker/util/CharSequenceTokenizer.java                sourceId JDT 	charStart   userEditable  charEnd   id  � message 6TODO : �ш린源�吏� �ㅻ㈃ ���� 留���嫄대�� ����? priority    
lineNumber   �  L�{�       !     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message ZTODO : �ш린源�吏� �ㅻ㈃ ���� 留���嫄대�� ���щ�� 諛�����耳��� ���� 寃��멸�? priority    
lineNumber   �  L�{� D/kskyb.agent/src/com/kskyb/broker/service/scheduler/PeriodTimer.java          "     sourceId JDT 	charStart  J userEditable  charEnd  z id  � message jTODO: 媛��� 媛�源��� �ㅽ�� ��媛��� 怨��고���� long�쇰� 諛������� 濡�吏��� �ㅼ�닿�硫� 醫��� priority    
lineNumber   �  L�{옘 E/kskyb.agent/src/com/kskyb/broker/service/scheduler/AbstractTask.java          #     sourceId JDT 	charStart  U userEditable  charEnd  w id  � message DTODO : ���� task ������ 湲곗���� 諛�������濡� 濡�吏� 異�媛� priority    
lineNumber   u  L�{옥 R/kskyb.agent/src/com/kskyb/broker/service/pool/thread/BridgeThreadPoolService.java          $     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message ?TODO : 由ъ���ㅺ� �� ��由� ������吏� ��寃��댁�쇳����. priority    
lineNumber   0  L�{옳 L/kskyb.agent/src/com/kskyb/broker/service/pool/db/DataSourcePoolService.java          %     sourceId JDT 	charStart  � userEditable  charEnd   id  � message ;TODO : �����댁�몄�� ���������� ������ ������. priority    
lineNumber   �  L�{왁 U/kskyb.agent/src/com/kskyb/broker/service/net/transport/socket/SocketServiceInfo.java          &     sourceId JDT 	charStart  � userEditable  charEnd   id  � message 5TODO : serive瑜� ��吏����� 濡�吏��� �ｌ�댁�쇳��. priority    
lineNumber     L�{왓 _/kskyb.agent/src/com/kskyb/broker/service/net/nio/transport/socket/NonBlockedSocketSession.java          '    	 severity    sourceId JDT 	charStart  � charEnd  � 	arguments 1:in id  x message 2Resource leak: 'in' is not closed at this location 
lineNumber   � 
categoryId   Z  L�{욋 a/kskyb.agent/src/com/kskyb/broker/service/net/nio/transport/socket/NonBlockedSocketProcessor.java          (     sourceId JDT 	charStart  * userEditable  charEnd  ` id  � message `TODO : test case瑜� 留��ㅼ�댁�� �� 遺�遺����� sessions媛� 0 �몄�瑜� ���명�대��쇳����. priority    
lineNumber   �  L�{욕       )     sourceId JDT 	charStart  +9 userEditable  charEnd  +n id  � message aFIXME : acceptor service媛� �������쇰� 醫�猷�����吏� �щ��� ���� 寃�利��� ������. priority    
lineNumber  w  L�{욕 O/kskyb.agent/src/com/kskyb/broker/service/net/filter/impl/GzipCompressorV1.java          *     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message FIXME : remove priority    
lineNumber   2  L�{� O/kskyb.agent/src/com/kskyb/broker/service/net/core/impl/DummySessionConfig.java          +     sourceId JDT 	charStart  � userEditable  charEnd    id  � message TODO Auto-generated method stub priority    
lineNumber     L�{� I/kskyb.agent/src/com/kskyb/broker/service/net/core/impl/DummySession.java          ,     sourceId JDT 	charStart  c userEditable  charEnd  � id  � message TODO Auto-generated method stub priority    
lineNumber   ,  L�{� E/kskyb.agent/src/com/kskyb/broker/service/net/core/IoSessionImpl.java          -     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message BTODO : closing & closed�� ���� ��李⑥�� ���댁�� 怨�誘쇳���� priority    
lineNumber     L�{�       .     sourceId JDT 	charStart  - userEditable  charEnd  -D id  � message vTODO : 踰��쇱���� ��肄��⑹�� ��猷��� 媛�( null )�� ���ㅻ㈃ 臾댄�� 猷⑦��瑜� ���� ���� 媛��μ�깆�� ���� priority    
lineNumber  �  L�{� \/kskyb.agent/src/com/kskyb/broker/service/net/core/IoSessionEventListenerSupportAdapter.java          /     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message ETODO : name �� unique �댁�쇳�⑥�� 蹂댁����� 猷⑦�댁�� ������. priority    
lineNumber   �  L�{� U/kskyb.agent/src/com/kskyb/broker/service/net/core/IoSessionEventListenerAdapter.java          0     sourceId JDT 	charStart  y userEditable  charEnd  � id  � message ETODO : name �� unique �댁�쇳�⑥�� 蹂댁����� 猷⑦�댁�� ������. priority    
lineNumber   o  L�{� D/kskyb.agent/src/com/kskyb/broker/service/net/codec/ObjectCodec.java          1     sourceId JDT 	charStart   userEditable  charEnd  ! id  � message TODO Auto-generated method stub priority    
lineNumber   l  L�{�- W/kskyb.agent/src/com/kskyb/broker/service/net/bio/transport/socket/SocketIoSession.java          2     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message CTODO : �곌껐�� ���� ����嫄곗�� ���� 寃�利��� ��������. priority    
lineNumber   �  L�{�/ I/kskyb.agent/src/com/kskyb/broker/service/net/MessageDelegateService.java          3     sourceId JDT 	charStart  	x userEditable  charEnd  	� id  � message $TODO : choice null messag can broke? priority    
lineNumber   e  L�{�B S/kskyb.agent/src/com/kskyb/broker/service/messaging/MessageArchiveChildService.java          4     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message ^TODO : �대깽�몃�� ���ы��怨� ���듭�� ���� 寃쎌�� sub interrupt method瑜� �몄�����. priority    
lineNumber   Z  L�{�E J/kskyb.agent/src/com/kskyb/broker/service/jmx/thread/JMXThreadSummary.java          5     sourceId JDT 	charStart   userEditable  charEnd  6 id  � message 9TODO : id array recycle �� 怨��ㅽ�댁�� 蹂�寃쏀�댁�쇳��. priority    
lineNumber   
  L�{�P A/kskyb.agent/src/com/kskyb/broker/service/jmx/client/JMXMain.java          6    	 severity    sourceId JDT 	charStart  � charEnd  � 	arguments 1:entry id  w message &Resource leak: 'entry' is never closed 
lineNumber   / 
categoryId   Z  L�{�W Q/kskyb.agent/src/com/kskyb/broker/service/context/comm/ContextMessageHandler.java          7     sourceId JDT 	charStart  � userEditable  charEnd   id  � message 2TODO : choice echo execute success message or not. priority    
lineNumber   �  L�{�{ H/kskyb.agent/src/com/kskyb/broker/service/bpel/param/ParamConstants.java          8     sourceId JDT 	charStart  .� userEditable  charEnd  .� id  � message <TODO : tree���� 異�媛��댁�쇳�� �몃��瑜� 李얠���쇳����. priority    
lineNumber  t  L�{�       9     sourceId JDT 	charStart  <� userEditable  charEnd  <� id  � message ZTODO : biz id 媛��몄�ㅻ��嫄� hierachy ��怨� �щ�쇨��� root���� 媛��몄�ㅻ㈃ ����. priority    
lineNumber  �  L�{� L/kskyb.agent/src/com/kskyb/broker/service/bpel/connector/MBeanConnector.java          :     sourceId JDT 	charStart  +N userEditable  charEnd  +l id  � message DFIXME : 由щ��⑺��怨� 寃곌낵瑜� 由ы�댄����濡� �����댁�쇳���� priority    
lineNumber  �  L�{�       ;     sourceId JDT 	charStart  _� userEditable  charEnd  `( id  � message eTODO : ����瑜� ���명�댁�� flag瑜� 媛�吏�怨� 吏������⑹�� 蹂댁�쇱�� ����濡� ��硫� 醫�寃���. priority    
lineNumber  Q  L�{� 9/kskyb.agent/src/com/kskyb/broker/model/ModelWrapper.java          <     sourceId JDT 	charStart  Y userEditable  charEnd  � id  � message ZTODO : 媛� Map / List�� 蹂����� ���댁���� 諛������� �⑦�댁�� 留��ㅼ�댁�쇳����. priority    
lineNumber   �  L�{입 @/kskyb.agent/src/com/kskyb/broker/model/ModelConstraintUtil.java          =     sourceId JDT 	charStart  x userEditable  charEnd  � id  � message hTODO : 蹂듭�ъ�� ���몃���� attr�� 怨⑤�쇰�대�� 怨쇱���� 蹂듭�≫��湲� ��臾몄�� ������ ������. priority    
lineNumber   �  L�{잘 2/kskyb.agent/src/com/kskyb/broker/model/Model.java          >     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message FTODO : 紐⑤�� hierachy ���� 以�泥⑸�� 紐⑤�몄�� �ㅼ�댁���ㅻ㈃?<br> priority    
lineNumber  -  L�{�       ?     sourceId JDT 	charStart  E� userEditable  charEnd  FE id  � message pTODO : property �� type�� ��寃����� xml�� 寃쎌�곗���� CDATA section node�� �대��� 蹂�寃쏀�댁�쇳����. priority    
lineNumber  �  L�{� ?/kskyb.agent/src/com/kskyb/broker/model/AttributeFieldType.java          @     sourceId JDT 	charStart   � userEditable  charEnd   � id  � message )TODO : ����瑜� hash濡� �����댁�쇳��. priority    
lineNumber     L�{�# 9/kskyb.agent/src/com/kskyb/broker/mime/MultiMimePart.java          A     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message =TODO Content-ID 瑜� �ы�⑦�� 泥⑤�媛� 議댁�ы�� 寃쎌�� <br> priority    
lineNumber   (  L�{�( @/kskyb.agent/src/com/kskyb/broker/logging/log4j/Log4jLogger.java          B     sourceId JDT 	charStart  K userEditable  charEnd  � id  � message ZTODO : inner logger entry瑜� 留��ㅼ�댁�� cache��怨� ���� 諛⑸��쇰� �����댁�쇳��. priority    
lineNumber   ~  L�{�7 ;/kskyb.agent/src/com/kskyb/broker/lang/ObjectFinalizer.java          C     sourceId JDT 	charStart  � userEditable  charEnd  $ id  � message _TODO : FinalizeMethodFinder�� DestroyMethod annotation�� �쎈�� ������ 異�媛��댁�쇳����. priority    
lineNumber     L�{�`       D     sourceId JDT 	charStart  H userEditable  charEnd  � id  � message iTODO : �ш린��李얠���� class & method �� service���� 紐⑤���곕� 媛��ν����濡� export �댁�쇳��. priority    
lineNumber   �  L�{�` K/kskyb.agent/src/com/kskyb/broker/kernel/serivce/ShutdownBridgeService.java          E     sourceId JDT 	charStart   userEditable  charEnd  / id  � message 6TODO : JMX �몄�濡� 二쎌���� ����濡� 諛�轅��쇳��. priority    
lineNumber     L�{� </kskyb.agent/src/com/kskyb/broker/kernel/ContextService.java          F     sourceId JDT 	charStart  
Z userEditable  charEnd  
� id  � message XTODO : hard coding�� ���� ��寃쎌������ loading�� 媛��ν����濡� �����댁�쇳��. priority    
lineNumber   i  L�{좆       G     sourceId JDT 	charStart  + userEditable  charEnd  I id  � message BTODO : 以�吏����� ��媛� ��猷��� 二쇱���� ��嫄고�댁�쇳����. priority    
lineNumber   �  L�{좆 J/kskyb.agent/src/com/kskyb/broker/kernel/AbstractAsyncListenerAdapter.java          H     sourceId JDT 	charStart  ` userEditable  charEnd  � id  � message �TODO : ������ process瑜� �몄����� listener���� 濡�吏�泥�由щ�� ��紐삵���� 臾댄��猷⑦��瑜� ��嫄곕�� ���듭�� �ㅼ� ���� 寃쎌�곗�� ��鍮��댁�쇳��.<br> priority    
lineNumber   �  L�{좔 ?/kskyb.agent/src/com/kskyb/broker/jmx/StandardMBeanAdapter.java          I     sourceId JDT 	charStart  I userEditable  charEnd  q id  � message \TODO : 硫����� 媛��몄�ㅻ�� 援ъ“瑜� 珥�湲곗�� �������� 諛⑹���쇰� �����댁�쇳��. priority    
lineNumber     L�{죙 ;/kskyb.agent/src/com/kskyb/broker/jmx/MBeanInfoContext.java          J     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message TODO : generate priority    
lineNumber   ,  L�{죤 ?/kskyb.agent/src/com/kskyb/broker/io/serialize/SheetObject.java          K     sourceId JDT 	charStart  Tt userEditable  charEnd  T� id  � message YTODO : �ш린�� ���ш� 諛��������쇰�� ���ъ�由ш� 留ㅻ���쎄� ���댁�� ��. priority    
lineNumber  Y  L�{즌 </kskyb.agent/src/com/kskyb/broker/io/RandomAccessStream.java          L     sourceId JDT 	charStart  � userEditable  charEnd    id  � message TODO Auto-generated catch block priority    
lineNumber  �  L�{�       M     sourceId JDT 	charStart  7r userEditable  charEnd  7� id  � message JTODO : flag瑜� �듭�쇳���� 議곌굔臾몄�� ����濡� 以��대��濡� �댁�쇳��. priority    
lineNumber  �  L�{� 0/kskyb.agent/src/com/kskyb/broker/io/IOUtil.java          N    	 severity    sourceId JDT 	charStart  JQ charEnd  Jn 	arguments 1:out id  x message 3Resource leak: 'out' is not closed at this location 
lineNumber  x 
categoryId   Z  L�{�'       O    	 severity    sourceId JDT 	charStart  JQ charEnd  Jn 	arguments 1:out id  x message 3Resource leak: 'out' is not closed at this location 
lineNumber  x 
categoryId   Z  L�{�' B/kskyb.agent/src/com/kskyb/broker/io/GzipCompressOutputStream.java          P     sourceId JDT 	charStart   � userEditable  charEnd   id  � message .FIXME : static Delegate class濡� 諛�轅��쇳��. priority    
lineNumber   
  L�{�*       Q     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message FIXME : remove priority    
lineNumber     L�{�*       R     sourceId JDT 	charStart  p userEditable  charEnd  � id  � message TODO : user recycle array priority    
lineNumber   D  L�{�* A/kskyb.agent/src/com/kskyb/broker/io/GzipCompressInputStream.java          S     sourceId JDT 	charStart  Q userEditable  charEnd  j id  � message TODO : user array recycle priority    
lineNumber   ?  L�{�- D/kskyb.agent/src/com/kskyb/broker/io/BufferedRandomAccessStream.java          U    	 severity    sourceId JDT 	charStart  � charEnd  � 	arguments 1:in id  x message 2Resource leak: 'in' is not closed at this location 
lineNumber  & 
categoryId   Z  L�{�:       T    	 severity    sourceId JDT 	charStart  � charEnd  � 	arguments 1:in id  x message 2Resource leak: 'in' is not closed at this location 
lineNumber  & 
categoryId   Z  L�{�: W/kskyb.agent/src/com/kskyb/broker/common/digest/xml/resource/XMLInputSourceContext.java          V    	 severity    sourceId JDT 	charStart  n charEnd  � 	arguments 1:<unassigned Closeable value> id  w message =Resource leak: '<unassigned Closeable value>' is never closed 
lineNumber   = 
categoryId   Z  L�{�c X/kskyb.agent/src/com/kskyb/broker/common/digest/xml/relation/RelationFieldEntryImpl.java          W     sourceId JDT 	charStart  3/ userEditable  charEnd  3X id  � message ETODO : key object�� codec�� �������� ���쇰�� ��鍮��댁�쇳��. priority    
lineNumber  �  L�{�q       X     sourceId JDT 	charStart  8� userEditable  charEnd  9# id  � message FTODO : �쇰� object�� 寃쎌�� �몄�ㅽ�댁�� 移댄�쇰�� 異�媛��댁�쇳��. priority    
lineNumber  �  L�{�q ]/kskyb.agent/src/com/kskyb/broker/common/digest/xml/relation/RelationFieldEntryAttribute.java          Y     sourceId JDT 	charStart   userEditable  charEnd  A id  � message RTODO : test case ���깊�댁�� 媛� ���ш����� ��������吏� 泥댄�ы�댁�쇳��. priority    
lineNumber   9  L�{�y O/kskyb.agent/src/com/kskyb/broker/common/digest/xml/XMLSerializerDelegator.java          Z     sourceId JDT 	charStart  h userEditable  charEnd  � id  � message ]TODO : �щ�ш�瑜� �쎌�댁�� ����留� instance濡� 留��ㅻ㈃ �ㅽ�댁�� �ъ�媛� ����.<br> priority    
lineNumber   �  L�{� 9/kskyb.agent/src/com/kskyb/broker/async/ExecutorPool.java          [     sourceId JDT 	charStart  � userEditable  charEnd  � id  � message TODO perhaps force stops priority    
lineNumber  <  L�{짭