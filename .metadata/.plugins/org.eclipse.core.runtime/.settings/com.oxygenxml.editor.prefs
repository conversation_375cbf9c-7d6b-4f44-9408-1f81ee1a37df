eclipse.preferences.version=1
oXygen_Options_Tag=<?xml version\="1.0" encoding\="UTF-8"?>\n<serialized version\="16.1" xml\:space\="preserve">\n\t<map>\n\t\t<entry>\n\t\t\t<String>dita.map.tree.states.info</String>\n\t\t\t<treeStateInfo-array/>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>document.types</String>\n\t\t\t<documentTypeDescriptor-array/>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>document.types.order</String>\n\t\t\t<documentTypeEntry-array>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/ant/ant.framework/ANT</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/dita/dita.framework/DITA</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/dita/ditamap.framework/DITA Map</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/dita/ditaval.framework/DITAVAL</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/daisy/daisy.framework/Daisy</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/docbook/docbook4.framework/DocBook 4</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/docbook/docbook5.framework/DocBook 5</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/docbook/targetset.framework/Docbook Targetset</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/ead/ead.framework/EAD</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/fo/fo.framework/FO</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/kml/kml.framework/KML</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/mathml/mathml.framework/MathML</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/maven/maven.framework/Maven Project</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/maven/maven.framework/Maven Settings</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/ncx/ncx.framework/NCX</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/nvdl/nvdl.framework/NVDL</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xml/xmlcatalog.framework/OASIS XML Catalog</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/ocf/ocf.framework/OCF</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/odf/odf.framework/ODF 1.2</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/ooxml/ooxml.framework/OOXML</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/opf/opf2.0.framework/OPF 2.0</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/opf/opf3.0.framework/OPF 3.0</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/relaxng/relaxng.framework/Relax NG</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/svg/svg.framework/SVG</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xslt/saxon.framework/Saxon</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/schematron/schematron.framework/Schematron</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/schematron/schematron15.framework/Schematron 1.5 (deprecated)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/stratml/stratml1.framework/StratML Part 1 (Strategic Plan)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/stratml/stratml2.framework/StratML Part 2 (Perfomance Plans and Reports)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/tei/teip5odd.framework/TEI ODD</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/tei/teip4.framework/TEI P4 (deprecated)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/tei/teip5.framework/TEI P5</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/wsdl/wsdl.framework/WSDL</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/widgets/widgets.framework/Widgets</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xhtml/xhtml.framework/XHTML</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xmlschema/xmlschema.framework/XML Schema</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xmlspec/xmlspec.framework/XMLSpec</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xproc/xproc.framework/XProc</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xslt/xslt.framework/XSLT</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/xspec/xspec.framework/XSpec</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/samples/application_form.framework/Application Form (Sample)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/samples/form-controls.framework/Form Controls (Sample)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/samples/personal.framework/Personal (Sample)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t\t<documentTypeEntry>\n\t\t\t\t\t<field name\="documentTypeID">\n\t\t\t\t\t\t<String>6/samples/travel-guide.framework/Travel Guide (Sample)</String>\n\t\t\t\t\t</field>\n\t\t\t\t\t<field name\="enable">\n\t\t\t\t\t\t<Boolean>true</Boolean>\n\t\t\t\t\t</field>\n\t\t\t\t</documentTypeEntry>\n\t\t\t</documentTypeEntry-array>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>ec.break.conditions</String>\n\t\t\t<String-array/>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>eclipse.master.files</String>\n\t\t\t<masterFilesStates-array/>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>editor.states.info</String>\n\t\t\t<editorStatesInfo-array/>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>first.run.v16.1</String>\n\t\t\t<Boolean>false</Boolean>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>http.proxy.system</String>\n\t\t\t<Boolean>false</Boolean>\n\t\t</entry>\n\t\t<entry>\n\t\t\t<String>validation.scenarios</String>\n\t\t\t<validationScenario-array/>\n\t\t</entry>\n\t</map>\n</serialized>
