<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_yXMAoYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_yXMAooAlEeS_EpJ0MUkvAA" bindingContexts="_yXMApIAlEeS_EpJ0MUkvAA">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_yXMAooAlEeS_EpJ0MUkvAA" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_yk-lAoAlEeS_EpJ0MUkvAA" label="%trimmedwindow.label.eclipseSDK" x="326" y="148" width="1221" height="812">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1428655159590"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_yk-lAoAlEeS_EpJ0MUkvAA" selectedElement="_yk_MEIAlEeS_EpJ0MUkvAA" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_yk_MEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_y5Qy8IAlEeS_EpJ0MUkvAA">
        <children xsi:type="advanced:Perspective" xmi:id="_y5Qy8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.j2ee.J2EEPerspective" selectedElement="_y5Qy8YAlEeS_EpJ0MUkvAA" label="Java EE" iconURI="platform:/plugin/org.eclipse.jst.j2ee.ui/icons/full/cview16/j2ee_perspective.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.jst.j2ee.J2eeMainActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.wst.server.ui.ServersView</tags>
          <tags>persp.viewSC:org.eclipse.datatools.connectivity.DataSourceExplorerNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.BookmarkView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.wst.common.snippets.internal.ui.SnippetsView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.AllMarkersView</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.jpa.ui.wizard.newJpaProject</tags>
          <tags>persp.perspSC:org.eclipse.jpt.ui.jpaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.web.ui.webDevPerspective</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.EarProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.project.facet.WebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.project.facet.EjbProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.jca.ui.internal.wizard.ConnectorProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.appclient.AppClientProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.web.ui.internal.wizards.SimpleWebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newJpaProject</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.internal.wizard.AddServletWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddSessionBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddMessageDrivenBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newEntity</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ws.creation.ui.wizard.serverwizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.actionSet:org.eclipse.wst.server.ui.internal.webbrowser.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.actionSet:org.eclipse.wst.ws.explorer.explorer</tags>
          <tags>persp.actionSet:com.oxygenxml.editor.urlsupport.actionset.openURL</tags>
          <tags>persp.actionSet:com.oxygenxml.report.actionset.reportProblem</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_y5Qy8YAlEeS_EpJ0MUkvAA" selectedElement="_y5Qy8oAlEeS_EpJ0MUkvAA" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_y5Qy8oAlEeS_EpJ0MUkvAA" elementId="topLeft" containerData="2500" selectedElement="_y5Qy9IAlEeS_EpJ0MUkvAA">
              <tags>newtablook</tags>
              <tags>active</tags>
              <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy84AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_y3aY0IAlEeS_EpJ0MUkvAA"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy9IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ResourceNavigator" ref="_y3a_4IAlEeS_EpJ0MUkvAA"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_y3a_4YAlEeS_EpJ0MUkvAA"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy9oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.PackagesView" toBeRendered="false" ref="_y5KsUIAlEeS_EpJ0MUkvAA"/>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_y5Qy94AlEeS_EpJ0MUkvAA" containerData="7500" selectedElement="_y5Qy_YAlEeS_EpJ0MUkvAA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_y5Qy-IAlEeS_EpJ0MUkvAA" containerData="7000" selectedElement="_y5Qy-oAlEeS_EpJ0MUkvAA" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy-YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editorss" containerData="7000" ref="_y3TrIIAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="basic:PartStack" xmi:id="_y5Qy-oAlEeS_EpJ0MUkvAA" elementId="topRight" containerData="3000" selectedElement="_y5Qy-4AlEeS_EpJ0MUkvAA">
                  <tags>newtablook</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy-4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ContentOutline" ref="_y5NIlIAlEeS_EpJ0MUkvAA"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy_IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_y5NvoIAlEeS_EpJ0MUkvAA"/>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_y5Qy_YAlEeS_EpJ0MUkvAA" elementId="bottomRight" containerData="3000" selectedElement="_y5QzAIAlEeS_EpJ0MUkvAA">
                <tags>newtablook</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy_oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.AllMarkersView" ref="_y5LTYIAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5Qy_4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.PropertySheet" ref="_y5LTYYAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.ServersView" ref="_y5LTYoAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" ref="_y5L6cIAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzAoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" ref="_y5MhgIAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzA4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ProblemView" toBeRendered="false" ref="_y5MhgYAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzBIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_y5MhgoAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzBYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_y5NIkIAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzBoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_y5NIkYAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzB4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_y5NIkoAlEeS_EpJ0MUkvAA"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_y5QzCIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_y5NIk4AlEeS_EpJ0MUkvAA"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_yk_MEYAlEeS_EpJ0MUkvAA" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_yk_MEoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_yk998IAlEeS_EpJ0MUkvAA"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_yk_ME4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_yk-lAIAlEeS_EpJ0MUkvAA"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_yk_MFIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_yk-lAYAlEeS_EpJ0MUkvAA"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_yk998IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yk-lAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zTEaEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zTEaEYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_yk-lAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_y3TrIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_y3TrIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.primaryDataStack">
        <tags>newtablook</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y3aY0IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_y-GPoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_y-G2sIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigator.ProjectExplorer" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y3a_4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view LINK_NAVIGATOR_TO_EDITOR=&quot;0&quot; sorter=&quot;1&quot;>&#xD;&#xA;&lt;filters>&#xD;&#xA;&lt;filter element=&quot;*.class&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;filter element=&quot;RemoteSystemsConnections&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;filter element=&quot;RemoteSystemsTempFiles&quot; isEnabled=&quot;true&quot;/>&#xD;&#xA;&lt;filter element=&quot;.*&quot; isEnabled=&quot;false&quot;/>&#xD;&#xA;&lt;/filters>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;element path=&quot;/kskyb.agent&quot;/>&#xD;&#xA;&lt;element path=&quot;/kskyb.agent/src&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;selection>&#xD;&#xA;&lt;element path=&quot;/kskyb.agent&quot;/>&#xD;&#xA;&lt;/selection>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_FGVAUN9dEeSKbvIcVz4E9w" elementId="org.eclipse.ui.views.ResourceNavigator">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_FGVAUd9dEeSKbvIcVz4E9w" elementId="org.eclipse.ui.views.ResourceNavigator"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y3a_4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5KsUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.PackagesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java Browsing</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5LTYIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.AllMarkersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.allSeverityField&quot; categoryGroup=&quot;org.eclipse.ui.ide.type&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.allMarkersGenerator&quot; partName=&quot;Markers&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.allSeverityField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.allSeverityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zHc_8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.AllMarkersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zHc_8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.AllMarkersView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5LTYYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5LTYoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
      <menus xmi:id="_UjkKYIAmEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.ServersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_UjkxcIAmEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.ServersView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5L6cIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Data Management</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5MhgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5MhgYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5MhgoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5NIkIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5NIkYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5NIkoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5NIk4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5NIlIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_zBzD4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_zBzD4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_y5NvoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <trimBars xmi:id="_ylDdgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_ywl_IIAlEeS_EpJ0MUkvAA" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ywl_IYAlEeS_EpJ0MUkvAA" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ywmmMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_r-_vgN9cEeSKbvIcVz4E9w" elementId="print" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" enabled="false" command="_yXsW-YAlEeS_EpJ0MUkvAA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ywnNQIAlEeS_EpJ0MUkvAA" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ywnNQYAlEeS_EpJ0MUkvAA" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_y77scIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_y6PqYIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_y7GmAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_y6apgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_y7yigIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.ws.explorer.explorer">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ywnNQoAlEeS_EpJ0MUkvAA" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ywnNQ4AlEeS_EpJ0MUkvAA" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ywnNRIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_r_Cy0d9cEeSKbvIcVz4E9w" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_yXrv94AlEeS_EpJ0MUkvAA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ywnNRYAlEeS_EpJ0MUkvAA" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ywnNRoAlEeS_EpJ0MUkvAA" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ywn0UIAlEeS_EpJ0MUkvAA" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_ywn0UYAlEeS_EpJ0MUkvAA" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_ywn0UoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_y1POkIAlEeS_EpJ0MUkvAA" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_y1S48IAlEeS_EpJ0MUkvAA" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_y1aNsIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_y1aNsYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_y1lz4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_y2LpwIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_y2X3AIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.trim.vertical1" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_zVh4MIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_y2YeEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <bindingTables xmi:id="_yXMAo4AlEeS_EpJ0MUkvAA" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_yXMApIAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYRlxoAlEeS_EpJ0MUkvAA" keySequence="CTRL+A" command="_yXpT5IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRly4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SPACE" command="_yXrwGYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRl0YAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+SPACE" command="_yXosloAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz5IAlEeS_EpJ0MUkvAA" keySequence="CTRL+C" command="_yXqhxYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz54AlEeS_EpJ0MUkvAA" keySequence="ALT+PAGE_UP" command="_yXp65YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz6YAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+D" command="_yXs-CoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCA4AlEeS_EpJ0MUkvAA" keySequence="SHIFT+INSERT" command="_yXmQfoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3PIAlEeS_EpJ0MUkvAA" keySequence="ALT+PAGE_DOWN" command="_yXrJFYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeQYAlEeS_EpJ0MUkvAA" keySequence="CTRL+Y" command="_yXp61YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeUoAlEeS_EpJ0MUkvAA" keySequence="CTRL+Z" command="_yXosuYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsbIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+I" command="_yXneroAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsboAlEeS_EpJ0MUkvAA" keySequence="CTRL+INSERT" command="_yXqhxYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXscoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+F2" command="_yXrI-IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTdYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+F1" command="_yXneiIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTeoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+L" command="_yXsXNYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhkoAlEeS_EpJ0MUkvAA" keySequence="CTRL+F10" command="_yXm3c4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhmIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+F3" command="_yXsXA4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhnYAlEeS_EpJ0MUkvAA" keySequence="CTRL+X" command="_yXoswYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIs4AlEeS_EpJ0MUkvAA" keySequence="CTRL+1" command="_yXoFm4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaItYAlEeS_EpJ0MUkvAA" keySequence="CTRL+V" command="_yXmQfoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWyYAlEeS_EpJ0MUkvAA" keySequence="SHIFT+DEL" command="_yXoswYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYMtQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_yX2H-YAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPJgIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+C" command="_yXqh24AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQXpIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_yXm3hYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQXp4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_yXnehoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-sYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+B" command="_yXs-SYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-tIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_UP" command="_yXp64oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlzYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_UP" command="_yXp6uYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz74AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F" command="_yXsXQ4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpFYAlEeS_EpJ0MUkvAA" keySequence="CTRL+T" command="_yXqh5YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpHoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+M" command="_yXoFpYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQJIAlEeS_EpJ0MUkvAA" keySequence="CTRL+F3" command="_yXs-IIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQJ4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+P" command="_yXrJJoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQK4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+U" command="_yXrv9oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFVIAlEeS_EpJ0MUkvAA" keySequence="CTRL+O" command="_yXpT5YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsb4AlEeS_EpJ0MUkvAA" keySequence="CTRL+I" command="_yXos04AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6hoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+/" command="_yXp6zYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6mIAlEeS_EpJ0MUkvAA" keySequence="CTRL+7" command="_yXqh24AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavt4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+O" command="_yXostIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavvIAlEeS_EpJ0MUkvAA" keySequence="CTRL+/" command="_yXqh24AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavw4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+\" command="_yXm3i4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbW0IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_yXos3IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbW04AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_yXpT54AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb91IAlEeS_EpJ0MUkvAA" keySequence="CTRL+2 F" command="_yXs-DYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb92IAlEeS_EpJ0MUkvAA" keySequence="CTRL+2 L" command="_yXm3foAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYck6IAlEeS_EpJ0MUkvAA" keySequence="CTRL+2 R" command="_yXrwCYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYck8oAlEeS_EpJ0MUkvAA" keySequence="CTRL+2 M" command="_yXpTpIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYPJgYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_yX2II4AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPJgoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+C" command="_yXqh24AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6mYAlEeS_EpJ0MUkvAA" keySequence="CTRL+7" command="_yXqh24AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavvYAlEeS_EpJ0MUkvAA" keySequence="CTRL+/" command="_yXqh24AlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYPJg4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_yX2H8IAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPJhIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+C" command="_yXrv_IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYPwlYAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_RIGHT" command="_yXoFo4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCAoAlEeS_EpJ0MUkvAA" keySequence="SHIFT+INSERT" command="_yXpTs4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeQIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+V" command="_yXpTs4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsbYAlEeS_EpJ0MUkvAA" keySequence="CTRL+INSERT" command="_yXrv_IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWwYAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_UP" command="_yXrwFIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYPJhYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" bindingContext="_yX2H84AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPwkIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+C" command="_yXqiAoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQXpYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_yXrJA4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQXqoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_yXrv9YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-tYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_UP" command="_yXs-YoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlz4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_UP" command="_yXmQiYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz6IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+E" command="_yXp6-4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz8oAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F" command="_yXm3gIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpFoAlEeS_EpJ0MUkvAA" keySequence="CTRL+T" command="_yXqiG4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQIoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+M" command="_yXm3bIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQJYAlEeS_EpJ0MUkvAA" keySequence="CTRL+F3" command="_yXqhyoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQKYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+P" command="_yXrI44AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQLoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+U" command="_yXosvoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeQ4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+T" command="_yXp6w4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFWYAlEeS_EpJ0MUkvAA" keySequence="CTRL+O" command="_yXmQaYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXscIAlEeS_EpJ0MUkvAA" keySequence="CTRL+I" command="_yXpTz4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6h4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+/" command="_yXp6wIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhkIAlEeS_EpJ0MUkvAA" keySequence="CTRL+7" command="_yXqiAoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavuIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+O" command="_yXpTq4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavvoAlEeS_EpJ0MUkvAA" keySequence="CTRL+/" command="_yXqiAoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavxIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+\" command="_yXneeYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbW0YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_yXsXRYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbW1IAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_yXnemIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb91YAlEeS_EpJ0MUkvAA" keySequence="CTRL+2 F" command="_yXpTtYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb92YAlEeS_EpJ0MUkvAA" keySequence="CTRL+2 L" command="_yXs-IYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYck6YAlEeS_EpJ0MUkvAA" keySequence="CTRL+2 R" command="_yXoFqIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYPwkYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_yX2H-4AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPwkoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+C" command="_yXsXQYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQXpoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_yXrI04AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-sIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_yXrv4YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-toAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_UP" command="_yXnep4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-uIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+A" command="_yXs-WoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRl0IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_UP" command="_yXrJG4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz84AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F" command="_yXs-NYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQKoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+P" command="_yXqh54AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeTYAlEeS_EpJ0MUkvAA" keySequence="F3" command="_yXqh5IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFWoAlEeS_EpJ0MUkvAA" keySequence="CTRL+O" command="_yXqh9IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXscYAlEeS_EpJ0MUkvAA" keySequence="CTRL+I" command="_yXsW_IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6iIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+/" command="_yXqh14AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavxYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+\" command="_yXrI94AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavxoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+>" command="_yXrwNIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbW0oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_yXm3aIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbW1YAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_yXp6wYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYPwk4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.contexts.window" bindingContext="_yXMApYAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPwlIAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_yXqiA4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYPwloAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_RIGHT" command="_yXos1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQXoIAlEeS_EpJ0MUkvAA" keySequence="CTRL+B" command="_yXmQhoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-t4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+A" command="_yXrJE4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQ-uYAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_yXqiGYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlwIAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+SHIFT+A" command="_yXrJI4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlwYAlEeS_EpJ0MUkvAA" keySequence="ALT+C" command="_yXosp4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlxIAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_LEFT" command="_yXm3d4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM0IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D O" command="_yXqhy4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM0YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D J" command="_yXrv8YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM0oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D Q" command="_yXp6uoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM04AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D R" command="_yXp61oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM1IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D A" command="_yXrwM4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM1YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D T" command="_yXmQUYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM1oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D P" command="_yXsW9IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM14AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D X" command="_yXp6yYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM2IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+D E" command="_yXs-SoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM3IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+R" command="_yXp62oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM5IAlEeS_EpJ0MUkvAA" keySequence="CTRL+#" command="_yXm3dIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz7YAlEeS_EpJ0MUkvAA" keySequence="CTRL+E" command="_yXossIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz9YAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+E" command="_yXnekYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz9oAlEeS_EpJ0MUkvAA" keySequence="CTRL+F" command="_yXm3lYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa8YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+F" command="_yXsXDoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa84AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+G" command="_yXrI14AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa94AlEeS_EpJ0MUkvAA" keySequence="CTRL+G" command="_yXlpR4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa-YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+H" command="_yXosqYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa-oAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+G" command="_yXrI9YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa_4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+G" command="_yXs-NIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCAIAlEeS_EpJ0MUkvAA" keySequence="CTRL+H" command="_yXrwGIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCBIAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+H" command="_yXm3aoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCBoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+I" command="_yXm3dYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCCoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+H" command="_yXp7AYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCDoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F6" command="_yXrI64AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpEIAlEeS_EpJ0MUkvAA" keySequence="SHIFT+F2" command="_yXrI0IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpEoAlEeS_EpJ0MUkvAA" keySequence="CTRL+F9" command="_yXnelYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpGYAlEeS_EpJ0MUkvAA" keySequence="F12" command="_yXrwHIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpGoAlEeS_EpJ0MUkvAA" keySequence="ALT+F7" command="_yXqh7oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpG4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_yXosnIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpHYAlEeS_EpJ0MUkvAA" keySequence="ALT+-" command="_yXqh2IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQL4AlEeS_EpJ0MUkvAA" keySequence="CTRL+." command="_yXs-IoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQMIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+N" command="_yXosuoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3MIAlEeS_EpJ0MUkvAA" keySequence="CTRL+M" command="_yXrwFIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3MYAlEeS_EpJ0MUkvAA" keySequence="CTRL+P" command="_yXsW-YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3MoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Z" command="_yXp6_IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3NIAlEeS_EpJ0MUkvAA" keySequence="CTRL+," command="_yXmQg4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3NYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+T" command="_yXqh6IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3PYAlEeS_EpJ0MUkvAA" keySequence="ALT+X" command="_yXp6-oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeQoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+T" command="_yXoswIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeRYAlEeS_EpJ0MUkvAA" keySequence="F5" command="_yXpToYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeSIAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+SHIFT+M" command="_yXs-AIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeS4AlEeS_EpJ0MUkvAA" keySequence="F3" command="_yXoFloAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeToAlEeS_EpJ0MUkvAA" keySequence="ALT+V" command="_yXpT6YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeU4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F8" command="_yXosmYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFUIAlEeS_EpJ0MUkvAA" keySequence="CTRL+F6" command="_yXnedIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFUoAlEeS_EpJ0MUkvAA" keySequence="CTRL+{" command="_yXosmoAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYXFU4AlEeS_EpJ0MUkvAA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_yYXsY4AlEeS_EpJ0MUkvAA" keySequence="ALT+CR" command="_yXrwBoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsZIAlEeS_EpJ0MUkvAA" keySequence="CTRL+F7" command="_yXqhxoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsaIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F9" command="_yXpT2IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsaYAlEeS_EpJ0MUkvAA" keySequence="F2" command="_yXmQhYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsa4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+F7" command="_yXrJJ4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsc4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_yXrI4IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTc4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+J" command="_yXosrYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTdoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+S" command="_yXp69YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTeYAlEeS_EpJ0MUkvAA" keySequence="CTRL+BREAK" command="_yXm3hIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTe4AlEeS_EpJ0MUkvAA" keySequence="CTRL+F11" command="_yXsXIoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTfIAlEeS_EpJ0MUkvAA" keySequence="ALT+F5" command="_yXpT24AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTfoAlEeS_EpJ0MUkvAA" keySequence="CTRL+F8" command="_yXoFnYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYThYAlEeS_EpJ0MUkvAA" keySequence="CTRL+W" command="_yXp634AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTiIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+O" command="_yXs-BYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6iYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+U" command="_yXnel4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6jIAlEeS_EpJ0MUkvAA" keySequence="SHIFT+F5" command="_yXp6tIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6jYAlEeS_EpJ0MUkvAA" keySequence="F4" command="_yXm3YYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6kIAlEeS_EpJ0MUkvAA" keySequence="CTRL+-" command="_yXsXN4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6koAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+P" command="_yXrI_IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6k4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F7" command="_yXs-AYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6lIAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+SHIFT+F12" command="_yXs-EYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6loAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+S" command="_yXrI4YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhlIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+M" command="_yXs-D4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhloAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+W" command="_yXosv4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhmYAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+P" command="_yXnec4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhnIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F4" command="_yXosv4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhnoAlEeS_EpJ0MUkvAA" keySequence="CTRL+3" command="_yXoFo4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhn4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+R" command="_yXs-X4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhoIAlEeS_EpJ0MUkvAA" keySequence="DEL" command="_yXnee4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZho4AlEeS_EpJ0MUkvAA" keySequence="F11" command="_yXsXVIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIo4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+W" command="_yXs-HYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIqIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F12" command="_yXmQYYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIqoAlEeS_EpJ0MUkvAA" keySequence="CTRL+N" command="_yXs-OYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIrIAlEeS_EpJ0MUkvAA" keySequence="CTRL+Q" command="_yXsXCYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIr4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+L" command="_yXoFmIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIsYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+N" command="_yXqh2YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIsoAlEeS_EpJ0MUkvAA" keySequence="CTRL+F4" command="_yXp634AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavsIAlEeS_EpJ0MUkvAA" keySequence="CTRL+U" command="_yXp6zIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavsYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+V" command="_yXpTzYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavtIAlEeS_EpJ0MUkvAA" keySequence="CTRL+F12" command="_yXnehIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavuoAlEeS_EpJ0MUkvAA" keySequence="CTRL+S" command="_yXpTwoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWw4AlEeS_EpJ0MUkvAA" keySequence="CTRL+_" command="_yXosmoAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYbWxIAlEeS_EpJ0MUkvAA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_yYbWxYAlEeS_EpJ0MUkvAA" keySequence="CTRL+DEL" command="_yXsXPIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWx4AlEeS_EpJ0MUkvAA" keySequence="CTRL+=" command="_yXos2IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWyIAlEeS_EpJ0MUkvAA" keySequence="SHIFT+DEL" command="_yXosk4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWyoAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_yXs-QYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWy4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+B" command="_yXnegoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWzIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+C" command="_yXrwH4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb90IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q Y" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYb90YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_yYb90oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X Q" command="_yXneq4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb904AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X A" command="_yXmQgoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb91oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q X" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYb914AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_yYb92oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q H" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYb924AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_yYb93IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q P" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYb93YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_yYb934AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X X" command="_yXqh3YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb94IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q K" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYb94YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_yYb94oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X T" command="_yXpT4YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb944AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X E" command="_yXrI9IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb95IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X M" command="_yXpT6oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb95YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q L" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYb95oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_yYb954AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q D" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYb96IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_yYck4YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X J" command="_yXrJGYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYck4oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q C" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYck44AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_yYck5IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q O" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYck5YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_yYck5oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q Z" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYck54AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_yYck6oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q B" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYck64AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_yYck7IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q V" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYck7YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_yYck7oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q J" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYck74AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_yYck8YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X P" command="_yXs-F4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYck84AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X O" command="_yXrI0YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYdL8IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q T" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYdL8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_yYdL84AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+X R" command="_yXp604AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYdL9IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q S" command="_yXrJBYAlEeS_EpJ0MUkvAA">
      <parameters xmi:id="_yYdL9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_yYdL9oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q Q" command="_yXrJBYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_ru2asd9cEeSKbvIcVz4E9w" keySequence="ALT+CTRL+D" command="_ruYgp99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru3o0N9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+A" command="_ruZHxd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru-9kN9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+CR" command="_ruZHyt9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru_ko99cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+O" command="_ruYg699cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru_kqN9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+DEL" command="_ruYg-d9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAyx99cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+V C" command="_ruZHut9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ1d9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+V R" command="_ruYgst9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yYPwl4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" bindingContext="_yX2H94AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPwmIAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+D" command="_yXosrIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlwoAlEeS_EpJ0MUkvAA" keySequence="ALT+C" command="_yXoFroAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCDIAlEeS_EpJ0MUkvAA" keySequence="ALT+S" command="_yXpT7IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQJoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+P" command="_yXrv7oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3PoAlEeS_EpJ0MUkvAA" keySequence="ALT+X" command="_yXs-TIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeR4AlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+X" command="_yXp62YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavuYAlEeS_EpJ0MUkvAA" keySequence="ALT+Q" command="_yXsW8YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavu4AlEeS_EpJ0MUkvAA" keySequence="CTRL+/" command="_yXmQW4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavwYAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+R" command="_yXsXIYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYPwmYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.serverViewScope" bindingContext="_yX2IEYAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPwmoAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+D" command="_yXrJFIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQMoAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+S" command="_yXoFj4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhmoAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+P" command="_yXrJLIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavwoAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+R" command="_yXs-JYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYPwm4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_yX2IGIAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYPwnIAlEeS_EpJ0MUkvAA" keySequence="ALT+D" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlw4AlEeS_EpJ0MUkvAA" keySequence="ALT+C" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlyIAlEeS_EpJ0MUkvAA" keySequence="ALT+B" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM2YAlEeS_EpJ0MUkvAA" keySequence="ALT+W" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz5YAlEeS_EpJ0MUkvAA" keySequence="ALT+E" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa8IAlEeS_EpJ0MUkvAA" keySequence="ALT+H" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa8oAlEeS_EpJ0MUkvAA" keySequence="ALT+G" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCDYAlEeS_EpJ0MUkvAA" keySequence="ALT+S" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3OIAlEeS_EpJ0MUkvAA" keySequence="ALT+N" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3OYAlEeS_EpJ0MUkvAA" keySequence="ALT+F" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeSYAlEeS_EpJ0MUkvAA" keySequence="ALT+P" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeT4AlEeS_EpJ0MUkvAA" keySequence="ALT+V" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFUYAlEeS_EpJ0MUkvAA" keySequence="ALT+L" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhm4AlEeS_EpJ0MUkvAA" keySequence="ALT+Y" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIoIAlEeS_EpJ0MUkvAA" keySequence="ALT+R" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIpIAlEeS_EpJ0MUkvAA" keySequence="ALT+T" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavx4AlEeS_EpJ0MUkvAA" keySequence="ALT+A" command="_yXqh1YAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYQXoYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.textEditorScope" bindingContext="_yX2H8oAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYQXooAlEeS_EpJ0MUkvAA" keySequence="CTRL+ARROW_DOWN" command="_yXs-WYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYQXo4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_yXpTzoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlxYAlEeS_EpJ0MUkvAA" keySequence="CTRL+ARROW_UP" command="_yXnehYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlx4AlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+ARROW_UP" command="_yXs-HoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlyoAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_DOWN" command="_yXrJIIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlzIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+DEL" command="_yXrwIIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRl0oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+A" command="_yXqh5oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz5oAlEeS_EpJ0MUkvAA" keySequence="CTRL+ARROW_LEFT" command="_yXqhwYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz7IAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_yXnen4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz9IAlEeS_EpJ0MUkvAA" keySequence="SHIFT+HOME" command="_yXpTqYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa9IAlEeS_EpJ0MUkvAA" keySequence="CTRL+HOME" command="_yXmQfYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa-4AlEeS_EpJ0MUkvAA" keySequence="SHIFT+END" command="_yXpTy4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa_IAlEeS_EpJ0MUkvAA" keySequence="CTRL+BS" command="_yXlpRoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa_YAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+J" command="_yXoFlIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa_oAlEeS_EpJ0MUkvAA" keySequence="CTRL+END" command="_yXrJJYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpF4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+CR" command="_yXsXAoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpGIAlEeS_EpJ0MUkvAA" keySequence="CTRL+NUMPAD_MULTIPLY" command="_yXrv5IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpHIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_yXqiCYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQI4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+K" command="_yXnegYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3N4AlEeS_EpJ0MUkvAA" keySequence="HOME" command="_yXsXRoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3OoAlEeS_EpJ0MUkvAA" keySequence="CTRL+D" command="_yXm3iYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3O4AlEeS_EpJ0MUkvAA" keySequence="CTRL+ARROW_RIGHT" command="_yXoFhIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsaoAlEeS_EpJ0MUkvAA" keySequence="F2" command="_yXoFpIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTcIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_yXrv64AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTcYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+J" command="_yXneqYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTcoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+INSERT" command="_yXnemYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTg4AlEeS_EpJ0MUkvAA" keySequence="ALT+/" command="_yXsXSoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6g4AlEeS_EpJ0MUkvAA" keySequence="CTRL+K" command="_yXrJD4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6hIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+X" command="_yXqhzoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6hYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+Y" command="_yXpTu4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6i4AlEeS_EpJ0MUkvAA" keySequence="CTRL+NUMPAD_DIVIDE" command="_yXneioAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6lYAlEeS_EpJ0MUkvAA" keySequence="CTRL+J" command="_yXm3eoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhk4AlEeS_EpJ0MUkvAA" keySequence="CTRL+F10" command="_yXsW_oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhl4AlEeS_EpJ0MUkvAA" keySequence="SHIFT+CR" command="_yXsXRIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhoYAlEeS_EpJ0MUkvAA" keySequence="END" command="_yXsXHIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIq4AlEeS_EpJ0MUkvAA" keySequence="CTRL+NUMPAD_SUBTRACT" command="_yXsXAYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIroAlEeS_EpJ0MUkvAA" keySequence="INSERT" command="_yXqiB4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavtYAlEeS_EpJ0MUkvAA" keySequence="CTRL+NUMPAD_ADD" command="_yXs-DoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavv4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+Q" command="_yXoFh4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavwIAlEeS_EpJ0MUkvAA" keySequence="CTRL+L" command="_yXrwMIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWwoAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_UP" command="_yXs-RYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWxoAlEeS_EpJ0MUkvAA" keySequence="CTRL+DEL" command="_yXossoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbW1oAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+ARROW_DOWN" command="_yXp6sIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYQXqIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_yX2IHIAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYQXqYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_yXpTyIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlyYAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_DOWN" command="_yXosq4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYRlzoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+ARROW_UP" command="_yXrwDIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSM34AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+R" command="_yXpTzIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCAYAlEeS_EpJ0MUkvAA" keySequence="SHIFT+INSERT" command="_yXnedoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCB4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+I" command="_yXpTwYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQLIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+U" command="_yXrJMYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYThoAlEeS_EpJ0MUkvAA" keySequence="CTRL+CR" command="_yXospYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6joAlEeS_EpJ0MUkvAA" keySequence="F4" command="_yXm3cIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIrYAlEeS_EpJ0MUkvAA" keySequence="INSERT" command="_yXpTuIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWwIAlEeS_EpJ0MUkvAA" keySequence="ALT+ARROW_UP" command="_yXrJAoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWzYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+C" command="_yXp684AlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYQ-soAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_yX2H-IAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYQ-s4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+B" command="_yXs-SYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSM2oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_yX2IBYAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSM24AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+R" command="_yXm3YIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYSz7oAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F" command="_yXsXQ4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCEYAlEeS_EpJ0MUkvAA" keySequence="SHIFT+F2" command="_yXqh-YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeSoAlEeS_EpJ0MUkvAA" keySequence="F3" command="_yXmQVYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavtoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+O" command="_yXlpT4AlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSM3YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" bindingContext="_yX2ICoAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSM3oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+R" command="_yXs-FYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQIIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+M" command="_yXoFtYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavsoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+V" command="_yXpT5oAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSM4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_yX2IHYAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSM4YAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+R" command="_yXpTzIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCCIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+I" command="_yXpTwYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQIYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+M" command="_yXmQaIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQLYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+U" command="_yXrJMYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFVYAlEeS_EpJ0MUkvAA" keySequence="CTRL+O" command="_yXs-GYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTd4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+S" command="_yXosxoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWzoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+C" command="_yXp684AlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSM4oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" bindingContext="_yX2IIIAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSM44AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+R" command="_yXrI5oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTa-IAlEeS_EpJ0MUkvAA" keySequence="CTRL+G" command="_yXrJEIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYTbAIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+G" command="_yXqh64AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCBYAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+H" command="_yXrwDYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCCYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+I" command="_yXqh9YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUCC4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+H" command="_yXs-XIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYUpEYAlEeS_EpJ0MUkvAA" keySequence="SHIFT+F2" command="_yXrwJ4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3M4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Z" command="_yXpT2oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYV3NoAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+T" command="_yXnegIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeTIAlEeS_EpJ0MUkvAA" keySequence="F3" command="_yXrJK4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTdIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+J" command="_yXqiGoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTeIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+S" command="_yXrI3IAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6goAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+O" command="_yXm3ioAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6ioAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+U" command="_yXoFg4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6j4AlEeS_EpJ0MUkvAA" keySequence="F4" command="_yXp6s4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhlYAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+M" command="_yXsXHoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIsIAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+L" command="_yXp6woAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYavs4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+V" command="_yXoFhoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYbWz4AlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+C" command="_yXoFtoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYb93oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q P" command="_yXrI74AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYck4IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q D" command="_yXrJIYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYck8IAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q J" command="_yXsXM4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYdL8oAlEeS_EpJ0MUkvAA" keySequence="ALT+SHIFT+Q T" command="_yXqiFoAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSz4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_yX2IJIAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSz4YAlEeS_EpJ0MUkvAA" keySequence="CTRL+C" command="_yXoFsYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaItIAlEeS_EpJ0MUkvAA" keySequence="CTRL+V" command="_yXqhzIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSz4oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_yX2IF4AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSz44AlEeS_EpJ0MUkvAA" keySequence="CTRL+C" command="_yXnedYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSz6oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.core.runtime.xml" bindingContext="_yX2H_4AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSz64AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+D" command="_yXp6y4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQKIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+P" command="_yXrwHYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYSz8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_yX2ID4AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYSz8YAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F" command="_yXm3g4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXFWIAlEeS_EpJ0MUkvAA" keySequence="CTRL+O" command="_yXnecIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYTa9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_yX2IE4AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYTa9oAlEeS_EpJ0MUkvAA" keySequence="CTRL+G" command="_yXrwE4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsYIAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+." command="_yXrwEoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6l4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+," command="_yXsXDYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYUCD4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.debugging" bindingContext="_yX2IEoAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYUCEIAlEeS_EpJ0MUkvAA" keySequence="CTRL+R" command="_yXqhyYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYVQMYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+3" command="_yXm3YoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeRIAlEeS_EpJ0MUkvAA" keySequence="F5" command="_yXm3ZoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYWeRoAlEeS_EpJ0MUkvAA" keySequence="CTRL+F2" command="_yXrwI4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTfYAlEeS_EpJ0MUkvAA" keySequence="F8" command="_yXqiAYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTf4AlEeS_EpJ0MUkvAA" keySequence="CTRL+F5" command="_yXsXUIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYY6kYAlEeS_EpJ0MUkvAA" keySequence="F6" command="_yXpT0oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaItoAlEeS_EpJ0MUkvAA" keySequence="F7" command="_yXs-KIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYUpE4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_yX2H9oAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYUpFIAlEeS_EpJ0MUkvAA" keySequence="CTRL+T" command="_yXoFqYAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYThIAlEeS_EpJ0MUkvAA" keySequence="CTRL+W" command="_yXqiEIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhkYAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+M" command="_yXpT3oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhpIAlEeS_EpJ0MUkvAA" keySequence="ALT+CTRL+N" command="_yXs-EoAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIqYAlEeS_EpJ0MUkvAA" keySequence="CTRL+N" command="_yXrI8YAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYWeUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.console" bindingContext="_yX2IDYAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYWeUYAlEeS_EpJ0MUkvAA" keySequence="CTRL+Z" command="_yXs-G4AlEeS_EpJ0MUkvAA">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_yYXFVoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_yX2IH4AlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYXFV4AlEeS_EpJ0MUkvAA" keySequence="CTRL+O" command="_yXmQd4AlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYXsYYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_yX2H8YAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYXsYoAlEeS_EpJ0MUkvAA" keySequence="ALT+CR" command="_yXp7AIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTh4AlEeS_EpJ0MUkvAA" keySequence="CTRL+CR" command="_yXm3cYAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYXsZYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.pagedesigner.editorContext" bindingContext="_yX2ICIAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYXsZoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F11" command="_yXsXU4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYXsZ4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F9" command="_yXpT0YAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYYTgoAlEeS_EpJ0MUkvAA" keySequence="CTRL+F5" command="_yXqh7oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYZhooAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F10" command="_yXs-HIAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIpoAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F5" command="_yXrJJ4AlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIp4AlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F12" command="_yXs-LIAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYYTgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" bindingContext="_yX2IJYAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYYTgYAlEeS_EpJ0MUkvAA" keySequence="CTRL+F5" command="_yXqh7oAlEeS_EpJ0MUkvAA"/>
    <bindings xmi:id="_yYaIpYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+F5" command="_yXrJJ4AlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYY6gIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_yX2IIYAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYY6gYAlEeS_EpJ0MUkvAA" keySequence="CTRL+SHIFT+O" command="_yXmQd4AlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_yYaIoYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_yX2IHoAlEeS_EpJ0MUkvAA">
    <bindings xmi:id="_yYaIooAlEeS_EpJ0MUkvAA" keySequence="F1" command="_yXmQUoAlEeS_EpJ0MUkvAA"/>
  </bindingTables>
  <bindingTables xmi:id="_y3USMYAlEeS_EpJ0MUkvAA" bindingContext="_y3USMIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3USM4AlEeS_EpJ0MUkvAA" bindingContext="_y3USMoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3U5QYAlEeS_EpJ0MUkvAA" bindingContext="_y3U5QIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3U5Q4AlEeS_EpJ0MUkvAA" bindingContext="_y3U5QoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3U5RYAlEeS_EpJ0MUkvAA" bindingContext="_y3U5RIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3U5R4AlEeS_EpJ0MUkvAA" bindingContext="_y3U5RoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3U5SYAlEeS_EpJ0MUkvAA" bindingContext="_y3U5SIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3U5S4AlEeS_EpJ0MUkvAA" bindingContext="_y3U5SoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3U5TYAlEeS_EpJ0MUkvAA" bindingContext="_y3U5TIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3VgUYAlEeS_EpJ0MUkvAA" bindingContext="_y3VgUIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3VgU4AlEeS_EpJ0MUkvAA" bindingContext="_y3VgUoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3VgVYAlEeS_EpJ0MUkvAA" bindingContext="_y3VgVIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3VgV4AlEeS_EpJ0MUkvAA" bindingContext="_y3VgVoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3VgWYAlEeS_EpJ0MUkvAA" bindingContext="_y3VgWIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3VgW4AlEeS_EpJ0MUkvAA" bindingContext="_y3VgWoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WHYYAlEeS_EpJ0MUkvAA" bindingContext="_y3WHYIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WHY4AlEeS_EpJ0MUkvAA" bindingContext="_y3WHYoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WHZYAlEeS_EpJ0MUkvAA" bindingContext="_y3WHZIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WHZ4AlEeS_EpJ0MUkvAA" bindingContext="_y3WHZoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WHaYAlEeS_EpJ0MUkvAA" bindingContext="_y3WHaIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WHa4AlEeS_EpJ0MUkvAA" bindingContext="_y3WHaoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WHbYAlEeS_EpJ0MUkvAA" bindingContext="_y3WHbIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WucYAlEeS_EpJ0MUkvAA" bindingContext="_y3WucIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3Wuc4AlEeS_EpJ0MUkvAA" bindingContext="_y3WucoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WudYAlEeS_EpJ0MUkvAA" bindingContext="_y3WudIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3Wud4AlEeS_EpJ0MUkvAA" bindingContext="_y3WudoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WueYAlEeS_EpJ0MUkvAA" bindingContext="_y3WueIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3Wue4AlEeS_EpJ0MUkvAA" bindingContext="_y3WueoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3WufYAlEeS_EpJ0MUkvAA" bindingContext="_y3WufIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3XVgYAlEeS_EpJ0MUkvAA" bindingContext="_y3XVgIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3XVg4AlEeS_EpJ0MUkvAA" bindingContext="_y3XVgoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3XVhYAlEeS_EpJ0MUkvAA" bindingContext="_y3XVhIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3XVh4AlEeS_EpJ0MUkvAA" bindingContext="_y3XVhoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3XViYAlEeS_EpJ0MUkvAA" bindingContext="_y3XViIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3XVi4AlEeS_EpJ0MUkvAA" bindingContext="_y3XVioAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3XVjYAlEeS_EpJ0MUkvAA" bindingContext="_y3XVjIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3X8kYAlEeS_EpJ0MUkvAA" bindingContext="_y3X8kIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3X8k4AlEeS_EpJ0MUkvAA" bindingContext="_y3X8koAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3X8lYAlEeS_EpJ0MUkvAA" bindingContext="_y3X8lIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3X8l4AlEeS_EpJ0MUkvAA" bindingContext="_y3X8loAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3X8mYAlEeS_EpJ0MUkvAA" bindingContext="_y3X8mIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3X8m4AlEeS_EpJ0MUkvAA" bindingContext="_y3X8moAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3YjoYAlEeS_EpJ0MUkvAA" bindingContext="_y3YjoIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3Yjo4AlEeS_EpJ0MUkvAA" bindingContext="_y3YjooAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3YjpYAlEeS_EpJ0MUkvAA" bindingContext="_y3YjpIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3Yjp4AlEeS_EpJ0MUkvAA" bindingContext="_y3YjpoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3YjqYAlEeS_EpJ0MUkvAA" bindingContext="_y3YjqIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3Yjq4AlEeS_EpJ0MUkvAA" bindingContext="_y3YjqoAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3YjrYAlEeS_EpJ0MUkvAA" bindingContext="_y3YjrIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_y3ZKsYAlEeS_EpJ0MUkvAA" bindingContext="_y3ZKsIAlEeS_EpJ0MUkvAA"/>
  <bindingTables xmi:id="_ru1MkN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.wsdlEditorScope" bindingContext="_rua8599cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru2asN9cEeSKbvIcVz4E9w" keySequence="ALT+CTRL+D" command="_ruYgwd9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAyxt9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S R" command="_ruX5m99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ1N9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S O" command="_ruYgyd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvCA4t9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S D" command="_ruZHtd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru3o0d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xmlCommonEditorScope" bindingContext="_rua85d9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru3o0t9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+A" command="_ruX5mt9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru4P4N9cEeSKbvIcVz4E9w" keySequence="CTRL+NUMPAD_SUBTRACT" command="_ruYg1t9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru4P4d9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+E" command="_ruYgqd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru4P4t9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+F" command="_ruYgpt9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru428N9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+F" command="_ruX5od9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru429d9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+I" command="_ruZH0N9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru5eAN9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+G" command="_ruYg6d9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru6FEN9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+/" command="_ruZHt99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru6FEd9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+," command="_ruX5kt9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru6FEt9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+R" command="_ruZHxN9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru6FE99cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+CR" command="_ruYg-N9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru6sIN9cEeSKbvIcVz4E9w" keySequence="CTRL+NUMPAD_DIVIDE" command="_ruYgt99cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru6sJd9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+P" command="_ruX5kd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru76QN9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+Y" command="_ruZHz99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru8hUt9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+I" command="_ruZHtt9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9IYN9cEeSKbvIcVz4E9w" keySequence="CTRL+I" command="_ruYg799cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9vcd9cEeSKbvIcVz4E9w" keySequence="CTRL+[" command="_ruZHzN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9vc99cEeSKbvIcVz4E9w" keySequence="CTRL+NUMPAD_MULTIPLY" command="_ruX5lt9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru-WgN9cEeSKbvIcVz4E9w" keySequence="CTRL+NUMPAD_ADD" command="_ruX5nN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru-9kd9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+CR" command="_ruYgtN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru_kod9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+X" command="_ruZHst9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru_kpN9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+\" command="_ruYg599cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvALsN9cEeSKbvIcVz4E9w" keySequence="CTRL+]" command="_ruYgr99cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvALsd9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+B B" command="_ruYgq99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvALst9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+B R" command="_ruX5kN9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvALs99cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+B T" command="_ruYgzd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvALtN9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+C" command="_ruYg1N9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ1t9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+V W" command="_ruZH0d9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvCA5t9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+V E" command="_ruYgu99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru3o099cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.allEditorsScope" bindingContext="_rua84t9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru3o1N9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+SPACE" command="_ruZHuN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru42-99cEeSKbvIcVz4E9w" keySequence="SHIFT+TAB" command="_ruYg3d9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru7TMN9cEeSKbvIcVz4E9w" keySequence="F3" command="_ruZHwd9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru76Qd9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+Q" command="_ruX5l99cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9IYd9cEeSKbvIcVz4E9w" keySequence="TAB" command="_ruYg7d9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9vct9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+," command="_ruYgwt9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru_kot9cEeSKbvIcVz4E9w" keySequence="SHIFT+F3" command="_ruYgzt9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAywN9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+V X" command="_ruYg5t9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvCA5d9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+V V" command="_ruZHvt9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru3o1d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xmlEditorScope" bindingContext="_rubj9N9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru3o1t9cEeSKbvIcVz4E9w" keySequence="SHIFT+F4" command="_ruZH0t9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9IYt9cEeSKbvIcVz4E9w" keySequence="F4" command="_ruYgot9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAyxd9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S R" command="_ruX5m99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ099cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S O" command="_ruYgyd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvCA4d9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S D" command="_ruZHtd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru3o199cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xslEditorScope" bindingContext="_rua85t9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru3o2N9cEeSKbvIcVz4E9w" keySequence="SHIFT+F4" command="_ruZH0t9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru429t9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+H" command="_ruX5lN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9IY99cEeSKbvIcVz4E9w" keySequence="F4" command="_ruYgot9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAywd9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S R" command="_ruX5m99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvAyyN9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S O" command="_ruYgyd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ2d9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S D" command="_ruZHtd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru3o2d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsdEditorScope" bindingContext="_rubj8t9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru3o2t9cEeSKbvIcVz4E9w" keySequence="SHIFT+F4" command="_ruZH0t9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru42999cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+H" command="_ruX5lN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9IZN9cEeSKbvIcVz4E9w" keySequence="F4" command="_ruYgot9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAywt9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S R" command="_ruX5m99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ0N9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S O" command="_ruYgyd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ2t9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S D" command="_ruZHtd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru3o299cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.rngEditorScope" bindingContext="_rubj8d9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru3o3N9cEeSKbvIcVz4E9w" keySequence="SHIFT+F4" command="_ruZH0t9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru42-N9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+H" command="_ruX5lN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru9vcN9cEeSKbvIcVz4E9w" keySequence="F4" command="_ruYgot9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAyw99cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S R" command="_ruX5m99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ0d9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S O" command="_ruYgyd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ299cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S D" command="_ruZHtd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru428d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.rncEditorScope" bindingContext="_rua84N9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru428t9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+F" command="_ruX5od9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru_kpd9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+\" command="_ruYg599cEeSKbvIcVz4E9w"/>
  </bindingTables>
  <bindingTables xmi:id="_ru42899cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.cssEditorScope" bindingContext="_rua84d9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru429N9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+F" command="_ruX5od9cEeSKbvIcVz4E9w"/>
  </bindingTables>
  <bindingTables xmi:id="_ru42-d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.nvdlEditorScope" bindingContext="_rubj8N9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru42-t9cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+H" command="_ruX5lN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_rvAyxN9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S R" command="_ruX5m99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvBZ0t9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S O" command="_ruYgyd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvCA4N9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+S D" command="_ruZHtd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_ru6sId9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugging.xslt" bindingContext="_rua8499cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru6sIt9cEeSKbvIcVz4E9w" keySequence="SHIFT+F7" command="_ruYg4d9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru76Qt9cEeSKbvIcVz4E9w" keySequence="CTRL+F5" command="_ruYg099cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru8hU99cEeSKbvIcVz4E9w" keySequence="F8" command="_ruYgzN9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru-Wgd9cEeSKbvIcVz4E9w" keySequence="SHIFT+F6" command="_ruYg8N9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru-Wg99cEeSKbvIcVz4E9w" keySequence="ALT+F7" command="_ruYg0d9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru-Whd9cEeSKbvIcVz4E9w" keySequence="F7" command="_ruYguN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru-9kt9cEeSKbvIcVz4E9w" keySequence="F6" command="_ruZHw99cEeSKbvIcVz4E9w"/>
  </bindingTables>
  <bindingTables xmi:id="_ru6sI99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugging.xquery" bindingContext="_rubj9d9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru6sJN9cEeSKbvIcVz4E9w" keySequence="SHIFT+F7" command="_ruYgqN9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru76Q99cEeSKbvIcVz4E9w" keySequence="CTRL+F5" command="_ruZHtN9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru8hVN9cEeSKbvIcVz4E9w" keySequence="F8" command="_ruZHsN9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_ru-Wgt9cEeSKbvIcVz4E9w" keySequence="SHIFT+F6" command="_ruZHs99cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru-WhN9cEeSKbvIcVz4E9w" keySequence="ALT+F7" command="_ruYg9d9cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru-Wht9cEeSKbvIcVz4E9w" keySequence="F7" command="_ruZH099cEeSKbvIcVz4E9w"/>
    <bindings xmi:id="_ru_koN9cEeSKbvIcVz4E9w" keySequence="F6" command="_ruYgx99cEeSKbvIcVz4E9w"/>
  </bindingTables>
  <bindingTables xmi:id="_ru8hUN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.jsEditorScope" bindingContext="_rubj9t9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru8hUd9cEeSKbvIcVz4E9w" keySequence="CTRL+/" command="_ruYgrN9cEeSKbvIcVz4E9w"/>
  </bindingTables>
  <bindingTables xmi:id="_ru_kpt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.dtdEditorScope" bindingContext="_rubj899cEeSKbvIcVz4E9w">
    <bindings xmi:id="_ru_kp99cEeSKbvIcVz4E9w" keySequence="CTRL+SHIFT+\" command="_ruYg599cEeSKbvIcVz4E9w"/>
  </bindingTables>
  <bindingTables xmi:id="_rvBZ199cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.transformationEditorScope" bindingContext="_rua85N9cEeSKbvIcVz4E9w">
    <bindings xmi:id="_rvBZ2N9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+T C" command="_ruYgy99cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvCA499cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+T T" command="_ruZHsd9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_rvCA5N9cEeSKbvIcVz4E9w" keySequence="ALT+SHIFT+T D" command="_ruZHyN9cEeSKbvIcVz4E9w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_r8_kYd9cEeSKbvIcVz4E9w" bindingContext="_r8_kYN9cEeSKbvIcVz4E9w"/>
  <bindingTables xmi:id="_r8_kY99cEeSKbvIcVz4E9w" bindingContext="_r8_kYt9cEeSKbvIcVz4E9w"/>
  <bindingTables xmi:id="_r8_kZd9cEeSKbvIcVz4E9w" bindingContext="_r8_kZN9cEeSKbvIcVz4E9w"/>
  <bindingTables xmi:id="_r9ALcd9cEeSKbvIcVz4E9w" bindingContext="_r9ALcN9cEeSKbvIcVz4E9w"/>
  <bindingTables xmi:id="_r9ALc99cEeSKbvIcVz4E9w" bindingContext="_r9ALct9cEeSKbvIcVz4E9w"/>
  <bindingTables xmi:id="_r9ALdd9cEeSKbvIcVz4E9w" bindingContext="_r9ALdN9cEeSKbvIcVz4E9w"/>
  <bindingTables xmi:id="_r9ALd99cEeSKbvIcVz4E9w" bindingContext="_r9ALdt9cEeSKbvIcVz4E9w"/>
  <bindingTables xmi:id="_r9Aygd9cEeSKbvIcVz4E9w" bindingContext="_r9AygN9cEeSKbvIcVz4E9w"/>
  <rootContext xmi:id="_yXMApIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_yXMApYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_yXMApoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_yX2H8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Widget in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_yX2H8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_yX2H8oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_yX2H84AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" name="Editing JavaScript Source" description="Editing JavaScript Source Context">
          <children xmi:id="_yX2IIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" name="JavaScript View" description="JavaScript View Context"/>
        </children>
        <children xmi:id="_yX2H94AlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" name="Editing SQL" description="Editing SQL Context"/>
        <children xmi:id="_yX2H-YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_yX2H-4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_yX2H_IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_yX2H_YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_yX2H_oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsp.core.jspsource" name="JSP Source" description="JSP Source"/>
          <children xmi:id="_yX2H_4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_yX2IAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_yX2IAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_yX2IAoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_yX2IBIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_yX2IBoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_yX2ICYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_yX2ICoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" name="Editing JSP Source" description="Editing JSP Source"/>
          <children xmi:id="_yX2IDoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_yX2IGYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.css.core.csssource" name="Editing CSS Source" description="Editing CSS Source"/>
          <children xmi:id="_yX2IG4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.html.core.htmlsource" name="Editing HTML Source" description="Editing HTML Source"/>
          <children xmi:id="_yX2IIoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
          <children xmi:id="_yX2IJoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.html.occurrences" name="HTML Source Occurrences" description="HTML Source Occurrences"/>
        </children>
        <children xmi:id="_yX2IBYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_yX2ICIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.pagedesigner.editorContext" name="Using Web Page Editor" description="Key binding context when using the web page editor"/>
        <children xmi:id="_yX2ID4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_yX2IGoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsd.ui.text.editor.context" name="Editing XSD context"/>
        <children xmi:id="_yX2IHYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_yX2IHoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_yX2IH4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_yX2IIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_yX2II4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
        <children xmi:id="_yX2IJYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" name="In Faces Config Editor" description="Key binding context when using the Faces Config Editor"/>
        <children xmi:id="_rua84N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.rncEditorScope" name="RNC Editor Scope" description="RNC Editing Context"/>
        <children xmi:id="_rua84d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.cssEditorScope" name="CSS Editor Scope" description="CSS Editing Context"/>
        <children xmi:id="_rua84t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.allEditorsScope" name="All Editors Scope" description="All Editing Contexts"/>
        <children xmi:id="_rua85N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.transformationEditorScope" name="Transformation Editor Scope" description="Transformation Editing Context"/>
        <children xmi:id="_rua85d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xmlCommonEditorScope" name="XML Common Editor Scope" description="XML Common Editing Context">
          <children xmi:id="_rua85t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xslEditorScope" name="XSL Editor Scope" description="XSL Editing Context"/>
          <children xmi:id="_rua8599cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.wsdlEditorScope" name="WSDL Editor Scope" description="WSDL Editing Context"/>
          <children xmi:id="_rubj8N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.nvdlEditorScope" name="NVDL Editor Scope" description="NVDL Editing Context"/>
          <children xmi:id="_rubj8d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.rngEditorScope" name="RNG Editor Scope" description="RNG Editing Context"/>
          <children xmi:id="_rubj8t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsdEditorScope" name="XSD Editor Scope" description="XSD Editing Context"/>
          <children xmi:id="_rubj9N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xmlEditorScope" name="XML Editor Scope" description="XML Editing Context"/>
        </children>
        <children xmi:id="_rubj899cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.dtdEditorScope" name="DTD Editor Scope" description="DTD Editing Context"/>
        <children xmi:id="_rubj9t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.jsEditorScope" name="JS Editor Scope" description="JS Editing Context"/>
      </children>
      <children xmi:id="_yX2H9IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_yX2H9oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_yX2H-oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.schemaobjecteditor.schemaediting" name="Schema Object Editor" description="Schema Object Editor"/>
      <children xmi:id="_yX2IDYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_yX2IEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_yX2IEYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_yX2IEoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_yX2IE4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_yX2IFIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsl.debug.ui.context" name="XSLT Debugging" description="Context for debugging XSLT"/>
        <children xmi:id="_yX2IFYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_yX2IF4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_yX2IGIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_yX2IHIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_yX2IJIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
      <children xmi:id="_rua8499cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugging.xslt" name="XSLT Debugging" description="XSLT Debugging"/>
      <children xmi:id="_rubj9d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugging.xquery" name="XQuery Debugging" description="XQuery Debugging"/>
    </children>
    <children xmi:id="_yXMAp4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_yX2H9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_yX2H-IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_yX2IA4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsd.ui.editor.sourceView" name="XSD Editor Source View" description="XSD Editor Source View"/>
  <rootContext xmi:id="_yX2IB4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.wsdl.ui.editor.sourceView" name="WSDL Editor Source View" description="WSDL Editor Source View"/>
  <rootContext xmi:id="_yX2IC4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsd.ui.editor.designView" name="XSD Editor Design View" description="XSD Editor Design View"/>
  <rootContext xmi:id="_yX2IDIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_yX2IFoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.wsdl.ui.editor.designView" name="WSDL Editor Design View" description="WSDL Editor Design View"/>
  <rootContext xmi:id="_y3USMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_y3USMoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.actionSet" name="Auto::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet"/>
  <rootContext xmi:id="_y3U5QIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_y3U5QoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_y3U5RIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_y3U5RoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_y3U5SIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_y3U5SoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_y3U5TIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_y3VgUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_y3VgUoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_y3VgVIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_y3VgVoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_y3VgWIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_y3VgWoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_y3WHYIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_y3WHYoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation" name="Auto::org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation"/>
  <rootContext xmi:id="_y3WHZIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet" name="Auto::org.eclipse.jst.j2ee.J2eeMainActionSet"/>
  <rootContext xmi:id="_y3WHZoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_y3WHaIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_y3WHaoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_y3WHbIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_y3WucIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_y3WucoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_y3WudIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_y3WudoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_y3WueIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.core.search.searchActionSet" name="Auto::org.eclipse.rse.core.search.searchActionSet"/>
  <rootContext xmi:id="_y3WueoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_y3WufIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.CVSActionSet" name="Auto::org.eclipse.team.cvs.ui.CVSActionSet"/>
  <rootContext xmi:id="_y3XVgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_y3XVgoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_y3XVhIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_y3XVhoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_y3XViIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_y3XVioAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_y3XVjIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_y3X8kIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_y3X8koAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_y3X8lIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_y3X8loAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_y3X8mIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_y3X8moAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_y3YjoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.JavaActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_y3YjooAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.A_OpenActionSet" name="Auto::org.eclipse.wst.jsdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_y3YjpIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.CodingActionSet" name="Auto::org.eclipse.wst.jsdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_y3YjpoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.SearchActionSet" name="Auto::org.eclipse.wst.jsdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_y3YjqIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_y3YjqoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_y3YjrIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.web.ui.wizardsActionSet" name="Auto::org.eclipse.wst.web.ui.wizardsActionSet"/>
  <rootContext xmi:id="_y3ZKsIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.ws.explorer.explorer" name="Auto::org.eclipse.wst.ws.explorer.explorer"/>
  <rootContext xmi:id="_r8_kYN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.urlsupport.actionset.openURL" name="Auto::com.oxygenxml.editor.urlsupport.actionset.openURL"/>
  <rootContext xmi:id="_r8_kYt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.report.actionset.reportProblem" name="Auto::com.oxygenxml.report.actionset.reportProblem"/>
  <rootContext xmi:id="_r8_kZN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.tools.actionSet" name="Auto::com.oxygenxml.editor.tools.actionSet"/>
  <rootContext xmi:id="_r9ALcN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xmlrefactory.actionSet" name="Auto::com.oxygenxml.editor.xmlrefactory.actionSet"/>
  <rootContext xmi:id="_r9ALct9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xslrefactory.actionSet" name="Auto::com.oxygenxml.editor.xslrefactory.actionSet"/>
  <rootContext xmi:id="_r9ALdN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.source.actionSet" name="Auto::com.oxygenxml.editor.source.actionSet"/>
  <rootContext xmi:id="_r9ALdt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.xslt.actionsSet" name="Auto::com.oxygenxml.debugger.xslt.actionsSet"/>
  <rootContext xmi:id="_r9AygN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.xquery.actionsSet" name="Auto::com.oxygenxml.debugger.xquery.actionsSet"/>
  <descriptors xmi:id="_yZ1sAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_yZ4IQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_yZ9n0IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_yZ-O4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.plan.planView" label="Execution Plan" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.plan/icons/sqlplan.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_yZ-18IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.result.resultView" label="SQL Results" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.result.ui/icons/sqlresult.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_yZ_dAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaArIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaArIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaBSMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaB5QIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaCgUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaCgUYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaDHYIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yaDucIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yaDucYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yaEVgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yaEVgYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_yaE8kIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaFjoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_yaGKsIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Display" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaGxwIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yaHY0IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yaIm8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yaIm8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yaJOAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yaJOAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yaJ1EIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_yaJ1EYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yaKcIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaKcIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yaLDMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_yaLDMYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.ui.jpaStructureView" label="JPA Structure" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-structure.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_yaLqQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.ui.jpaDetailsView" label="JPA Details" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-details.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_yaMRUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsf.ui.component.ComponentTreeView" label="JSF Component Tree" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_yaMRUYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsf.ui.tagregistry.TagRegistryView" label="Tag Registry" iconURI="platform:/plugin/org.eclipse.jst.jsf.ui/icons/obj16/library_obj.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_yaM4YIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.ws.jaxws.ui.views.AnnotationsView" label="Annotation Properties" iconURI="platform:/plugin/org.eclipse.jst.ws.jaxws.ui/icons/eview16/prop_ps.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Web Services</tags>
  </descriptors>
  <descriptors xmi:id="_yaNfcIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_yaOGgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_yaOGgYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_yaOtkIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_yaPUoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_yaP7sIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_yaQiwIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yaQiwYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yaRw4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yaRw4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yaSX8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_yaSX8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.shells.ui.view.commandsView" label="Remote Shell" iconURI="platform:/plugin/org.eclipse.rse.shells.ui/icons/full/cview16/commands_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaS_AIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.terminals.ui.view.TerminalView" label="Terminals" iconURI="platform:/plugin/org.eclipse.rse.terminals.ui/icons/terminal_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaTmEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.ui.view.systemView" label="Remote Systems" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaUNIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.ui.view.teamView" label="Team" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/team_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaUNIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.ui.view.systemTableView" label="Remote System Details" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaUNIoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.ui.view.SystemSearchView" label="Remote Search" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/obj16/system_search.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaU0MIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.ui.view.scratchpad.SystemScratchpadViewPart" label="Remote Scratchpad" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/scratchpad_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaU0MYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.ui.view.monitorView" label="Remote Monitor" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/system_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_yaVbQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaWCUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaWCUYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ccvs.ui.RepositoriesView" label="CVS Repositories" iconURI="platform:/plugin/org.eclipse.team.cvs.ui/icons/full/eview16/repo_rep.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:CVS</tags>
  </descriptors>
  <descriptors xmi:id="_yaWpYIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ccvs.ui.EditorsView" label="CVS Editors" iconURI="platform:/plugin/org.eclipse.team.cvs.ui/icons/full/eview16/rep_editors_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:CVS</tags>
  </descriptors>
  <descriptors xmi:id="_yaXQcIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_yaX3gIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_yaX3gYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.view.TerminalView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view/icons/cview16/terminal_view.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_yaYekIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaYekYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaZFoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_yaZssIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaaTwIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaaTwYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" category="org.eclipse.e4.primaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaa60IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaa60YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yaa60oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yabh4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yabh4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" category="org.eclipse.e4.primaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yacI8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" allowMultiple="true" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yacwAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" category="org.eclipse.e4.secondaryNavigationStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yadXEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yadXEYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_yad-IIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_yaelMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" label="Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/class_hi.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_yafzUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" label="Script Explorer" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/package.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_yagaYIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/call_hierarchy.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_yagaYYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/source.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_yagaYoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.JavadocView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/javadoc.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_yahBcIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_yahogIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_yaiPkIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_yaiPkYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.views.XPathView" label="XPath" iconURI="platform:/plugin/org.eclipse.wst.xml.xpath.ui/icons/full/xpath.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_yai2oIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsl.jaxp.debug.ui.resultview" label="Result" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_yai2oYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsl.ui.view.outline" label="Stylesheet Model" iconURI="platform:/plugin/org.eclipse.wst.xsl.ui/icons/full/hierarchy.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxDaIN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.DBExplorerView" label="Data Source Explorer" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DbConnection16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> DB</tags>
  </descriptors>
  <descriptors xmi:id="_rxFPUN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.DBTableExplorerView" label="Table Explorer" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DbConnection16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> DB</tags>
  </descriptors>
  <descriptors xmi:id="_rxF2YN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.OxygenTextView" label="Text" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/TextView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxGdcN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.OxygenSequenceView" label="Sequence" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/SequenceView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxGdcd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.XSDPaletteView" label="Palette" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameXsdPalette16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxHEgN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.OxygenResultsMapView" label="XProc Results" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/XProcResultsView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxHEgd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.OxygenBrowserView" label="Browser" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/BrowserView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxHrkN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.XPathResultsView" label="XPath Results" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/XPathView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxISoN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.XPathBuilderView" label="XPath/XQuery Builder" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/XPathView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxISod9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.OxygenDPIView" label="Results" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DPIView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxISot9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.ScenariosView" label="Transformation Scenarios" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/TransformConfig16.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxI5sN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.XsltXQueryInputView" label="XSLT/XQuery input" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameXsltInput16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxI5sd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.WSDLView" label="WSDL SOAP Analyser" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/WsdlView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxJgwN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.ModelView" label="Model" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameModel16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxJgwd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.AuthorReviewView" label="Review" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameReview16.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxKH0N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.EditorPropertiesView" label="Editor Properties" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameProperties16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxKH0d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.DITAMapsManagerView" label="DITA Maps Manager" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameDITAMap16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxKu4N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.DependencesHierarchyView" label="Resource Hierarchy/Dependencies" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DependencesHierarchy16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxKu4d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.ComponentDependencesHierarchyView" label="Component Dependencies" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/ComponentDependencies16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxLV8N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.AttributesView" label="Attributes" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameAttributes16.png" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxLV8d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.FacetsView" label="Facets" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameFacets16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxLV8t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.EntitiesView" label="Entities" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameEntities16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxL9AN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.view.ElementsView" label="Elements" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameContext16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> XML</tags>
  </descriptors>
  <descriptors xmi:id="_rxL9Ad9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.XSLTDebuggerView" label="XSLT Debugger" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DebuggerXSLTView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxMkEN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.XQueryDebuggerView" label="XQuery Debugger" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DebuggerXQueryView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxMkEd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.VariablesView" label="Variables" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameVariables16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxNLIN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.WatchView" label="XWatch" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameXWatch16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxNLId9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.ContextView" label="Context" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameContext16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxNyMN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.XslMessageView" label="Messages" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameMessages16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxNyMd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.profiler.InvocationTreeView" label="Invocation tree" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameInvocationTree16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxNyMt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.BreakpointsView" label="Breakpoints" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameBreakpoints16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxOZQN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.StackView" label="Stack" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameStack16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxOZQd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.BackMappingStackView" label="Output Mapping Stack" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameBMStack16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxPAUN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.TraceView" label="Trace" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameTrace16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxZ_cN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.TemplatesView" label="Templates" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameTemplates16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxamgN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugger.NodeSetView" label="Nodes/Values Set" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameNodesValuesSet16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxamgd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.profiler.HotSpotView" label="Hot Spots" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/DockableFrameHotspots16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxbNkN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.output.OutputView" label="Output" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/OutputView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxbNkd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.output.Saxon8OutputView" label="Result Documents" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/Saxon8OutputView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <descriptors xmi:id="_rxb0oN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.output.OutputBrowserView" label="HTML Output" iconURI="platform:/plugin/com.oxygenxml.editor/eclipseIcons/OutputBrowserView16.gif" category="org.eclipse.e4.secondaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:&lt;oXygen/> Debugger</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_yXlpQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpQYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpQoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXlpQ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_yXlpRIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpRYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpRoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpR4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpSIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInTypeHierarchyView" commandName="Show JavaScript Element Type Hierarchy" description="Show a JavaScript element in the Type Hierarchy view" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXlpSYAlEeS_EpJ0MUkvAA" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yXlpSoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpS4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpTIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpTYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpToAlEeS_EpJ0MUkvAA" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpT4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXlpUYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.callhierarchy.view" commandName="JavaScript Call Hierarchy" description="Show the Call Hierarchy view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.commands.export" commandName="Export Profiles Command" description="Command to export connection profiles" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQUYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQUoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQU4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQVIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.compareWithRemote" commandName="Compare With Latest from Repository" description="Compare with Content on CVS Server" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQVYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQVoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.ignore" commandName="Add to .cvsignore" description="Ignore the Selected Resources when Synchronizing" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQV4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQWIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQWYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.views.XPathView.prefixes" commandName="&amp;Edit Namespace Prefixes" category="_yXkbOYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQWoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.replaceWithRevision" commandName="Replace With Revision" description="Replace with Revision on CVS Server" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQW4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.toggleCommentAction" commandName="Toggle Comment" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQXIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQXYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQXoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQX4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQYIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQYYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQYoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQY4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQZIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQZYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQZoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQZ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQaIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_yXj0FYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQaYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQaoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.commands.openElementInEditor" commandName="Open JavaScript Element" description="Open a JavaScript element in its editor" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXmQa4AlEeS_EpJ0MUkvAA" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yXmQbIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.TypesView" commandName="JavaScript Types" description="Show the Types view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQbYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQboAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQb4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQcIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQcYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQcoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXmQc4AlEeS_EpJ0MUkvAA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yXmQdIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQdYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQdoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQd4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQeIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQeYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.MembersView" commandName="JavaScript Members" description="Show the Members view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQeoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQe4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQfIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.discoveryWizardCommand" commandName="%command.name" description="%command.description" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQfYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQfoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQf4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.compareWithRevision" commandName="Compare With Revision" description="Compare with Revision on CVS Server" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQgYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify var access" description="Invokes quick assist and selects 'Qualify var access'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQgoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQg4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQhIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQhYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQhoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQh4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQiIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQiYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQioAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXmQi4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3YIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3YYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3YoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.debug.ui.script.opensource" commandName="Open Source" description="Shows the JavaScript source for the selected script element" category="_yXkbI4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3Y4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3ZIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_yXkbNoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3ZYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3ZoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3Z4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.ws.jaxws.ui.configure.handlers" commandName="Configure Handlers" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3aIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3aYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3aoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3a4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3bIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3bYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXm3boAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_yXm3b4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3cIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3cYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.debug.ui.breakpoint.properties" commandName="JavaScript Breakpoint Properties" description="View and edit the properties for a given JavaScript breakpoint" category="_yXkbI4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3coAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3c4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3dIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3dYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3doAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3d4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3eIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3eYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3eoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3e4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3fIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3fYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.newEntity" commandName="JPA Entity" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3foAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3f4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3gIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3gYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3goAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3g4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_yXkbPoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3hIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.result.terminate" commandName="Terminate Result" category="_yXkbLIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3hYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3hoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3h4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() functions for the type" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3iIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3iYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3ioAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3i4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3jIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method in its hierarchy" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3jYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.showHistory" commandName="Show History" description="Show History" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3joAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3j4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3kIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.sync" commandName="Synchronize with Repository" description="Synchronize the workspace resources with those in the repository" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3kYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXm3koAlEeS_EpJ0MUkvAA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yXm3k4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3lIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter functions for type's vars" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3lYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3loAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3l4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Search" description="Adds all plug-ins in the target platform to java search" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXm3mIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXm3mYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_yXm3moAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnecIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnecYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnecoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_yXj0EIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnec4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnedIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnedYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnedoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXned4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.JavaBrowsingPerspective" commandName="JavaScript Browsing" description="Show the JavaScript Browsing perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneeIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneeYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneeoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnee4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnefIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnefYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnefoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnef4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnegIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnegYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnegoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneg4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnehIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnehYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnehoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneh4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_yXkbIIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneiIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_yXkbPYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneiYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneioAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnei4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnejIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnejYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnejoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_yXkbJIAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXnej4AlEeS_EpJ0MUkvAA" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_yXnekIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnekYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnekoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnek4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnelIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnelYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneloAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnel4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnemIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the JavaScript file" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnemYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnemoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnem4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnenIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnenYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnenoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnen4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneoYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneooAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneo4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnepIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnepYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnepoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.ProjectsView" commandName="JavaScript Projects" description="Show the Projects view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnep4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneqIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneqYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneqoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory function to encapsulate invocation of the selected constructor" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneq4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnerIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXnerYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXneroAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXner4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFgIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFgYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.replaceWithTag" commandName="Replace With Another Branch or Version" description="Replace with Branch or Version on the CVS Server" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFgoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFg4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFhIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFhYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to var" description="Invokes quick assist and selects 'Convert local variable to var'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFhoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFh4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFiIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFiYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFioAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFi4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFjIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFjYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_yXkbIoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFjoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFj4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_yXkbIYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFkIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_yXkbK4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXoFkYAlEeS_EpJ0MUkvAA" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_yXoFkoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFk4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFlIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFlYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.saveAsTemplateAction" commandName="Save as Template" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFloAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFl4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFmIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFmYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFmoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFm4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFnIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFnYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFnoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFn4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXoFoYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_yXoFooAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFo4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFpIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFpYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFpoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_yXj0FYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFp4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFqIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFqYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFqoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFq4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFrIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Git Index" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFrYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.refreshFromDatabaseAction" commandName="Refresh from Database" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFroAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteAsOneStatementAction" commandName="Execute Selected Text As One Statement" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFr4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFsIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.commands.addrepository" commandName="New Repository Profile Command" description="Command to create a new repository profile" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFsYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFsoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.JavaPerspective" commandName="JavaScript" description="Show the JavaScript perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFs4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFtIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.update" commandName="Update" description="Update resources with new content from the repository" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFtYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsp.ui.add.imports" commandName="Add Im&amp;port" description="Create import declaration for selection" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFtoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.modify.method.parameters" commandName="Change Function Signature" description="Change function signature includes parameter names and parameter order" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFt4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.upgradeToEclipseLinkMappingFile" commandName="Upgrade to EclipseLink Mapping File" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoFuIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_yXkbJ4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXoFuYAlEeS_EpJ0MUkvAA" elementId="url" name="URL"/>
    <parameters xmi:id="_yXoFuoAlEeS_EpJ0MUkvAA" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_yXoskIAlEeS_EpJ0MUkvAA" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_yXoskYAlEeS_EpJ0MUkvAA" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_yXoskoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static var and uses the new static var" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosk4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.result.removeAllInstances" commandName="Remove All Visible Results" category="_yXkbLIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoslIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoslYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosloAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosl4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosmIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosmYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosmoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_yXkbJ4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXosm4AlEeS_EpJ0MUkvAA" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_yXosnIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosnYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosnoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosn4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosoIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosoYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosooAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoso4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXospIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXospYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXospoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosp4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.copycolumn" commandName="Copy" category="_yXkbKIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosqIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosqYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosqoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosq4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosrIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.debugAction" commandName="Debug" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosrYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosroAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosr4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXossIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXossYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jaxb.ui.generateJaxbClasses" commandName="JAXB Classes..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXossoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoss4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXostIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXostYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXostoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXost4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosuIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosuYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosuoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosu4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosvIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosvYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosvoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosv4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoswIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoswYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoswoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosw4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosxIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosxYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosxoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_yXj0FYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosx4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosyIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosyYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.generate.xml" commandName="&amp;XML File..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosyoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXosy4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoszIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXoszYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_yXkbJIAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXoszoAlEeS_EpJ0MUkvAA" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_yXosz4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsd.ui.refactor.makeTypeGlobal" commandName="Make &amp;Anonymous Type Global" description="Promotes anonymous type to global level and replaces its references" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos0IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos0YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos0oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos04AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos1IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos1YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos1oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos14AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos2IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_yXkbN4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos2YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos2oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos24AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXos3IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpToIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpToYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTooAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.commands.showcategory" commandName="Show Category" description="Show Category" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTo4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTpIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTpYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTpoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTp4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.updateSwitch" commandName="Switch to Another Branch or Version" description="Switch to Another Branch or Version" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTqIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTqYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTqoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTq4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in JavaScript editors" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTrIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTrYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTroAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeMapAs" commandName="Map As" category="_yXkbO4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXpTr4AlEeS_EpJ0MUkvAA" elementId="specifiedPersistentAttributeMappingKey" name="specified mapping key" optional="false"/>
    <parameters xmi:id="_yXpTsIAlEeS_EpJ0MUkvAA" elementId="defaultPersistentAttributeMappingKey" name="default mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_yXpTsYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTsoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTs4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_yXkbLoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTtIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTtYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to var" description="Invokes quick assist and selects 'Assign to var'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTtoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.convertJavaGenerators" commandName="Move Java Generators to XML..." category="_yXkbOIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTt4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTuIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTuYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTuoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTu4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTvIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.commands.addprofile" commandName="New Connection Profile Command" description="Command to create a new connection profile" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXpTvYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.ui.ignoreCategory" name="ignoreCategory"/>
    <parameters xmi:id="_yXpTvoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.ui.useSelection" name="useSelection"/>
  </commands>
  <commands xmi:id="_yXpTv4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Git Index" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTwIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTwYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTwoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTw4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTxIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected function" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTxYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTxoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_yXj0EoAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXpTx4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_yXpTyIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTyYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_yXj0EIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTyoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.generateEntities" commandName="Generate Entities from Tables..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTy4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTzIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTzYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTzoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpTz4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.indent" commandName="Indent Line" description="Indents the current line" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT0IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT0YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.pagedesigner.vertical" commandName="Vertical Layout" category="_yXj0E4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT0oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT04AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.addToPersistenceUnit" commandName="Add to Persistence Unit" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT1IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT1YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT1oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT14AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT2IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT2YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT2oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT24AlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Project" description="Update Maven Project configuration and dependencies" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT3IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT3YAlEeS_EpJ0MUkvAA" elementId="refresh.schema.editor.action.id" commandName="Refresh from Server" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT3oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT34AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT4oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.convertJavaQueries" commandName="Move Java Queries to XML..." category="_yXkbOIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT44AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT5IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT5YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT5oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsp.ui.refactor.move" commandName="Move" description="Move a Java Element to another package" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT54AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT6IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT6YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.pastecolumn" commandName="Paste" category="_yXkbKIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT6oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT64AlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXpT7IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteCurrentAction" commandName="Execute Current Text" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6sIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6sYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6soAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6s4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6tIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6tYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6toAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6t4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6uIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_yXkbPIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6uYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6uoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6u4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6vIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6vYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6voAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_yXkbJ4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXp6v4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_yXp6wIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6wYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6woAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6w4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a JavaScript editor" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6xIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_yXkbK4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXp6xYAlEeS_EpJ0MUkvAA" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_yXp6xoAlEeS_EpJ0MUkvAA" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_yXp6x4AlEeS_EpJ0MUkvAA" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_yXp6yIAlEeS_EpJ0MUkvAA" elementId="revert.schema.editor.action.id" commandName="Revert Object" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6yYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.debug" commandName="Debug XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6yoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6y4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven Dependency" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6zIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6zYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6zoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.shells.ui.actions.LaunchShellCommand" commandName="Launch Shell" category="_yXkbJoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6z4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.core.synchronizeClasses" commandName="Synchronize Class List" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp60IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.navigate.gotopackage" commandName="Go to Folder" description="Go to Folder" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp60YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp60oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp604AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp61IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.inner.to.top.level" commandName="Convert Member Type to Top Level" description="Convert member type to top level" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp61YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp61oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp614AlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp62IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp62YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSQLAction" commandName="Execute All" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp62oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp624AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove &amp;API Problem Filters..." description="Remove API problem filters for this project" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp63IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp63YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp63oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp634AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp64IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp64YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.updateAll" commandName="Update All Incoming Changes" description="Update all incoming changes with new content from the repository" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp64oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp644AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXp65IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_yXp65YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp65oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp654AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXp66IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_yXp66YAlEeS_EpJ0MUkvAA" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp66oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.addLocation" commandName="Add Repository Location" description="Add a new CVS repository location" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp664AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp67IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_yXkbMoAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXp67YAlEeS_EpJ0MUkvAA" elementId="title" name="Title"/>
    <parameters xmi:id="_yXp67oAlEeS_EpJ0MUkvAA" elementId="message" name="Message"/>
    <parameters xmi:id="_yXp674AlEeS_EpJ0MUkvAA" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_yXp68IAlEeS_EpJ0MUkvAA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_yXp68YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp68oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp684AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp69IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp69YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp69oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp694AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6-IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6-YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6-oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.cutcolumn" commandName="Cut" category="_yXkbKIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6-4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.debug.ui.evaluate.command" commandName="Evaluate" description="Evaluates the selected text in the JavaScript editor" category="_yXkbI4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6_IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6_YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6_oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp6_4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp7AIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp7AYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp7AoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp7A4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp7BIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp7BYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXp7BoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.views.XPathView.processor.xpathprocessor" commandName="XPath Processor" category="_yXkbOYAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXqhwIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_yXqhwYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhwoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_yXkbJ4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXqhw4AlEeS_EpJ0MUkvAA" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_yXqhxIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhxYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhxoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhx4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhyIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhyYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhyoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhy4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhzIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhzYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhzoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqhz4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh0IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh0YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh0oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh04AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh1IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh1YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_yXkbLoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh1oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh14AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh2IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh2YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh2oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh24AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh3IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh3YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.run" commandName="Run XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh3oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh34AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh4oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh44AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh5IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh5YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh5oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh54AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh6IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh6YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Git Index with HEAD Revision" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh6oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh64AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh7IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jaxb.eclipselink.ui.command.addEclipseLinkJaxbProperty" commandName="Add EclipseLink JAXB property" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh7YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh7oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh74AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh8oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh84AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh9IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or function" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh9oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh94AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.branch" commandName="Branch" description="Branch" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh-IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.PackagesView" commandName="JavaScript Folders" description="Show the Folders view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh-YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh-oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh-4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh_IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh_YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh_oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.GenerateDiff" commandName="Create Patch" description="Compare your workspace contents with the server and generate a diff file that can be used as a patch file." category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqh_4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiAoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiA4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiBIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiBYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiBoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiB4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiCIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiCYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiCoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiC4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.cvsPerspective" commandName="CVS Repository Exploring" description="Open the CVS Repository Exploring Perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiDIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiDYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiDoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXqiD4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_yXqiEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiEYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiEoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new function parameter based on the selected expression" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiE4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiFIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiFYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiFoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" commandName="JavaScript Type Hierarchy" description="Show the Type Hierarchy view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiF4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiGIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiGYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiGoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.javadoc.comment" commandName="Add JSDoc Comment" description="Add a JSDoc comment stub to the member element" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiG4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiHIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXqiHYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools &amp;Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI0IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI0YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI0oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI04AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI1IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Push" commandName="Push" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI1YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI1oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.convertJavaProjectToJpa" commandName="Convert to JPA Project..." category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI14AlEeS_EpJ0MUkvAA" elementId="org.eclipse.emf.codegen.ecore.ui.Generate" commandName="Generate Code" description="Generate code for the EMF models in the workspace" category="_yXkbKoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI2IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.generate.javadoc" commandName="Generate JSDoc" description="Generates JSDoc for a selectable set of JavaScript resources" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI2YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI2oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI24AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI3IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI3YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_yXj0EoAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrI3oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_yXrI34AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI4oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI44AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI5IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI5YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI5oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI54AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI6IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI6YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.generateDynamicEntities" commandName="Generate Dynamic Entities from Tables..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI6oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI64AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI7IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI7YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI7oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI74AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" commandName="JavaScript Script Explorer" description="Show the Script Explorer" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI8oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI84AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI9IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI9oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI94AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI-IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_yXkbPYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI-YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.entityMappingsAddPersistentClass" commandName="Add Class..." category="_yXkbO4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI-oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Vars" description="Choose vars to initialize and constructor from superclass to call " category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI-4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI_IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI_YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI_oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.commit" commandName="Commit" description="Commit resources to the repository" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrI_4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.setKeywordSubstitution" commandName="Change ASCII/Binary Property" description="Change whether the selected resources should be treated as ASCII or binary on the CVS Server" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsd.ui.refactor.rename.element" commandName="&amp;Rename XSD element" description="Rename XSD element" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJAoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJA4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJBIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJBYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_yXj0EYAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrJBoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_yXrJB4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_yXrJCIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_yXrJCYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJCoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJC4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrJDIAlEeS_EpJ0MUkvAA" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_yXrJDYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJDoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJD4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJEYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.checkout" commandName="Checkout from CVS" description="Checkout from CVS" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJEoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJE4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJFIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_yXkbIYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJFYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJFoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJF4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_yXj0GoAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrJGIAlEeS_EpJ0MUkvAA" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_yXrJGYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJGoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJG4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJHIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.wsdl.ui.refactor.rename.element" commandName="Rename WSDL component" description="Renames WSDL component" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJHYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_yXkbK4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrJHoAlEeS_EpJ0MUkvAA" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_yXrJH4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.SourceView" commandName="JavaScript Declaration" description="Show the Declaration view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJIoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJI4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrJJIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_yXrJJYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJJoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJJ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJKIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.override.methods" commandName="Override/Implement Functions" description="Override or implement functions from super types" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJKYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJKoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJK4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJLIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_yXkbIYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJLYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJLoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJL4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrJMYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv4IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv4YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv4oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv44AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv5IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv5YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInPackageView" commandName="Show JavaScript Element in Script Explorer" description="Select JavaScript element in the Script Explorer view" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrv5oAlEeS_EpJ0MUkvAA" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yXrv54AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsd.ui.refactor.makeElementGlobal" commandName="Make Local Element &amp;Global" description="Promotes local element to global level and replaces its references" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv6IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv6YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv6oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Editor Area Visibility" description="Toggles the visibility of the editor area" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv64AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv7IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv7YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv7oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.GotoMatchingTokenAction" commandName="Goto Matching Token" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv74AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API &amp;Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv8IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Functions" description="Add delegate functions for a type's vars" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv8oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv84AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.tag" commandName="Tag as Version" description="Tag the resources with a CVS version tag" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv9IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.convertJavaConverters" commandName="Move Java Converters to XML..." category="_yXkbOIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv9oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv94AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv-IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.in.package.view" commandName="Show in Script Explorer" description="Show the selected element in the Script Explorer" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv-YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv-oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv-4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv_IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_yXkbLoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv_YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv_oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrv_4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to var" description="Invokes quick assist and selects 'Assign parameter to var'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_yXkbNoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwAoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwA4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwBIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwBYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwBoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwB4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwCIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwCYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwCoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwC4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwDIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwDYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwDoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwD4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwEYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwEoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwE4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwFIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwFYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwFoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwF4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwGIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwGYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwGoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwG4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwHIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwHYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven Plugin" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwHoAlEeS_EpJ0MUkvAA" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwH4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwIoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwI4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwJIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwJYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwJoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwJ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.external.javadoc" commandName="Open External JSDoc" description="Open the JSDoc of the selected element in an external browser" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwKIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwKYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwKoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwK4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.persistentTypeAddVirtualAttribute" commandName="Add Virtual Attribute..." category="_yXkbO4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwLIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXrwLYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_yXrwLoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_yXrwL4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newDynamicEntity" commandName="EclipseLink Dynamic Entity" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwMYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwMoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwM4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwNIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwNYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwNoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.saveToDatabaseAction" commandName="Save to Database" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwN4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwOIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwOYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwOoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwO4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwPIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwPYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwPoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwP4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXml" commandName="Add Attribute to XML" category="_yXkbO4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwQYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwQoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwQ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXrwRIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.persistentTypeMapAs" commandName="Map As" category="_yXkbO4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXsW8IAlEeS_EpJ0MUkvAA" elementId="persistentTypeMappingKey" name="mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_yXsW8YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.DMLDialogSelectionAction" commandName="Edit in SQL Query Builder..." category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW8oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW84AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.dbws.ui.generateDbws" commandName="Generate Database Web Services" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW9IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW9YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW9oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.attachProfileAction" commandName="Set Connection Information" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW94AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW-IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_yXkbNoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW-YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW-oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW-4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW_IAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW_YAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.connectivity.commands.import" commandName="Import Profiles Command" description="Command to import connection profiles" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW_oAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsW_4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXAIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXAYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXAoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXA4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXBIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXBYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXBoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXB4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXCIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXCYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXCoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXC4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXDIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXDYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXDoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXD4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXEIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXEYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXEoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_yXkbMoAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXsXE4AlEeS_EpJ0MUkvAA" elementId="title" name="Title"/>
    <parameters xmi:id="_yXsXFIAlEeS_EpJ0MUkvAA" elementId="message" name="Message"/>
    <parameters xmi:id="_yXsXFYAlEeS_EpJ0MUkvAA" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_yXsXFoAlEeS_EpJ0MUkvAA" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_yXsXF4AlEeS_EpJ0MUkvAA" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_yXsXGIAlEeS_EpJ0MUkvAA" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_yXsXGYAlEeS_EpJ0MUkvAA" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_yXsXGoAlEeS_EpJ0MUkvAA" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_yXsXG4AlEeS_EpJ0MUkvAA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_yXsXHIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXHYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXHoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.method" commandName="Extract Function" description="Extract a set of statements or an expression into a new function and use the new function" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXH4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.generateDDL" commandName="Generate Tables from Entities..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.runAction" commandName="Run" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXIoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXI4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.add" commandName="Add to Version Control" description="Add the Selected Resources to Version Control" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXJIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXJYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXJoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXJ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXKIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXKYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXKoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXK4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXLIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXLYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_yXkbNYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXLoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.replace" commandName="Replace With Latest from Repository" description="Replace with last committed content from CVS Server" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXL4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXMYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXMoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXM4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.JavadocView" commandName="Documentation" description="Show the JavaScript Documentation view" category="_yXj0EYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXNIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXNYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXNoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXN4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_yXkbN4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXOIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXsXOYAlEeS_EpJ0MUkvAA" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_yXsXOoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.terminals.ui.actions.LaunchTerminalCommand" commandName="Launch Terminal " category="_yXkbJoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXO4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected JavaScript comment lines" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXPIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.result.removeInstance" commandName="Remove Result" category="_yXkbLIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXPYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Git Index" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXPoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.discovery.ui.discoveryWizardCommand" commandName="Discovery Wizard" description="shows the connector discovery wizard" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXP4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXmlAndMap" commandName="Add Attribute to XML and Map..." category="_yXkbO4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXQYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXQoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXQ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXRIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXRYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXRoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXR4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXSIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXSYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXSoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXS4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xsd.ui.refactor.renameTargetNamespace" commandName="Rename Target Namespace" description="Changes the target namespace of the schema" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXTIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXTYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_yXj0EIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXToAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_yXkbLYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXT4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXUIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXUYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXUoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXU4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.pagedesigner.design" commandName="Graphical Designer" category="_yXj0E4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXVIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXsXVYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-AIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-AYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-AoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.replaceWithBase" commandName="Revert to Base" description="Revert to Base revisions" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-A4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Var" description="Create getting and setting functions for the var and use only those to access the var" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-BIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-BYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-BoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-B4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Var" description="Convert a local variable to a var" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-CIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-CYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-CoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-C4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-DIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-DYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-DoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-D4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-EIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newEclipseLinkMappingFile" commandName="EclipseLink ORM Mapping File" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-EYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-EoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-E4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-FIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-FYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.jsp.ui.refactor.rename" commandName="Rename" description="Rename a Java Element" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-FoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-F4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-GIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-GYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_yXj0GoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-GoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.RepositoriesViewRebase" commandName="Rebase on" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-G4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-HIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.pagedesigner.horizotal" commandName="Horizontal Layout" category="_yXj0E4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-HYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-HoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-H4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-IIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-IYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-IoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-I4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-JIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-JYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_yXkbIYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-JoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXs-J4AlEeS_EpJ0MUkvAA" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_yXs-KIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-KYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_yXkbJ4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXs-KoAlEeS_EpJ0MUkvAA" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_yXs-K4AlEeS_EpJ0MUkvAA" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_yXs-LIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.pagedesigner.source" commandName="Source Code" category="_yXj0E4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-LYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-LoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-L4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.showAnnotation" commandName="Show Annotation" description="Show Annotation" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-MIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-MYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected function" category="_yXkbQIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-MoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-M4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-NIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-NYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-NoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-N4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-OIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Git Index" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-OYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_yXkbJIAlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXs-OoAlEeS_EpJ0MUkvAA" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_yXs-O4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.compareWithTag" commandName="Compare With Another Branch or Version" description="Compare with a Branch or a Version on the CVS Server" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-PIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-PYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-PoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-P4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-QIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-QYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_yXj0GIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-QoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-Q4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_yXkbM4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-RIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_yXkbL4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-RYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-RoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_yXkbP4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXs-R4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_yXs-SIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_yXs-SYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-SoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-S4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.makePersistent" commandName="Make Persistent..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-TIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSelectionAction" commandName="Execute Selected Text" category="_yXkbNIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-TYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jaxb.ui.command.createPackageInfo" commandName="Create package-info.java" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-ToAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_yXkbKYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-T4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_yXkbOoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-UIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_yXkbJYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-UYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_yXkbMIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-UoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.newMappingFile" commandName="JPA ORM Mapping File" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-U4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.commitAll" commandName="Commit All Outgoing Changes" description="Commit all outgoing changes to the repository" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-VIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-VYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into JavaScript comments" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-VoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-V4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.merge" commandName="Merge" description="Merge" category="_yXkbMYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-WIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.xmlFileUpgradeToLatestVersion" commandName="Upgrade JPA Document Version" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-WYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_yXj0F4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-WoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_yXj0EoAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-W4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_yXkbJ4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-XIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-XYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-XoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.commands.openscrapbook" commandName="Open SQL Scrapboo&amp;k" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-X4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_yXj0G4AlEeS_EpJ0MUkvAA">
    <parameters xmi:id="_yXs-YIAlEeS_EpJ0MUkvAA" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_yXs-YYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_yXj0GYAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yXs-YoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the JavaScript file" category="_yXj0G4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykbLYIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykbycIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet/org.eclipse.datatools.sqltools.sqlscrapbook.actions.OpenScrapbookAction" commandName="Open SQL Scrapbook" description="Open scrapbook to edit SQL statements" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdAkIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdAkYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdAkoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdAk4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdAlIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdAlYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdAloAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdnoIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykdnoYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykfc0IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykfc0YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykfc0oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgD4IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgD4YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEArtifact" commandName="Servlet" description="Create a new Servlet" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgD4oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEProject" commandName="Dynamic Web Project" description="Create a Dynamic Web project" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgD44AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgq8IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgq8YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgq8oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgq84AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgq9IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.rse.core.search.searchActionSet/org.eclipse.rse.core.search.searchAction" commandName="Remote..." description="Opens Remote Search dialog page for text and file searching on remote systems" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykgq9YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykhSAIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykh5EIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykh5EYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykigIIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenFileWizard" commandName="JavaScript Source File" description="New JavaScript file" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykigIYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenProjectWizard" commandName="JavaScript Project..." description="New JavaScript Project" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykigIoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.refactor.show.refactoring.history" commandName="History..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykigI4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.SearchActionSet/org.eclipse.wst.jsdt.ui.actions.OpenJavaSearchPage" commandName="JavaScript..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHMIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHMYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHMoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHM4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newCSSFile" commandName="CSS" description="Create a new Cascading Style Sheet" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHNIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newJSFile" commandName="JavaScript" description="Create a new JavaScript file" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHNYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newHTMLFile" commandName="HTML" description="Create a new HTML page" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHNoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.ws.explorer.explorer/org.eclipse.wst.ws.internal.explorer.action.LaunchWSEAction" commandName="Launch the Web Services Explorer" description="Launch the Web Services Explorer" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjHN4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuQIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.rullerDoubleClick/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Add Breakpoint" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuQYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.exporter.genModelEditorContribution/org.eclipse.emf.exporter.ui.GenModelExportActionDelegate.Editor" commandName="Export Model..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuQoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.importer.genModelEditorContribution/org.eclipse.emf.importer.ui.GenModelReloadActionDelegate.Editor" commandName="Reload..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuQ4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.RemoveMappingActionID" commandName="Remove Mapping" description="Remove the mapping associated with the selected objects." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuRIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.TypeMatchMappingActionID" commandName="Match Mapping by Type" description="Create child mappings automatically by type." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuRYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.NameMatchMappingActionID" commandName="Match Mapping by Name" description="Create child mappings automatically by name." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuRoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateOneSidedMappingActionID" commandName="Create One-sided Mapping" description="Create a new mapping for the selected object." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuR4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateMappingActionID" commandName="Create Mapping" description="Create a new mapping between the selected objects." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykjuSIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddOuputRootActionID" commandName="Add Output Root..." description="Add new output root." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykkVUIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddInputRootActionID" commandName="Add Input Root..." description="Add new input root." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykkVUYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykkVUoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykkVU4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykkVVIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykkVVYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykkVVoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8YIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8YYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8YoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8Y4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8ZIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8ZYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8ZoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8Z4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykk8aIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljcIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljcYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljcoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljc4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljdIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljdYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljdoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykljd4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.debug.ui.togglebreakpoint/org.eclipse.wst.jsdt.debug.ui.RulerToggleBreakpoint" commandName="Toggle Breakpoint" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKgIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKgYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKgoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKg4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKhIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKhYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.editor/org.eclipse.wst.wsdl.ui.actions.ReloadDependenciesActionDelegate" commandName="Reload Dependencies" description="Reload Dependencies" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKhoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmKh4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxkIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxkYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxkoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxk4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxlIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxlYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxloAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykmxl4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYoIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYoYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYooAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYo4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYpIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYpYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYpoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_yknYp4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykn_sIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykn_sYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykn_soAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykn_s4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykn_tIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykn_tYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykn_toAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomwIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomwYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomwoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomw4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomxIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomxYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomxoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykomx4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN0IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN0YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN0oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN04AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN1IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN1YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN1oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN14AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykpN2IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp04IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp04YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp04oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp044AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp05IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp05YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="Focus on Active Task" description="Focus on Active Task" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp05oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykp054AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb8IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb8YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb8oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb84AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb9IAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb9YAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb9oAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykqb94AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDAIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDAYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDAoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDA4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDBIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDBYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.rse.ui.view.systemView.toolbar/org.eclipse.rse.ui.view.systemView.toolbar.linkWithSystemView" commandName="Link with Editor" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDBoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.add.scriptload.breakpoint" commandName="Add Script Load Breakpoint" description="Add Script Load Breakpoint" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDB4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Suspend For All Script Loads" description="Suspends when any script is loaded" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrDCIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.suspend.on.exceptions" commandName="Suspend On JavaScript Exceptions" description="Suspend on all JavaScript exceptions" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrqEIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::debugViewActions/org.eclipse.wst.jsdt.debug.ui.show.all.scripts" commandName="Show All Scripts" description="Shows or hides all scripts loaded in the visible targets" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrqEYAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.functions" commandName="Show function variables" description="Show or hide function variables" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrqEoAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.this" commandName="Show 'this' variable" description="Show or hide the this variable" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrqE4AlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.prototypes" commandName="Show proto variables" description="Show or hide proto variables" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ykrqFIAlEeS_EpJ0MUkvAA" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.view/org.eclipse.wst.wsi.ui.internal.actions.actionDelegates.ValidateWSIProfileActionDelegate" commandName="WS-I Profile Validator" description="Validate WS-I Message Log File" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ruX5kN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.removeBreakpoint" commandName="Remove breakpoint" description="Remove a breakpoint" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5kd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.renamePrefix" commandName="Rename Prefix" description="Rename current prefix" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5kt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.deleteElementTag" commandName="Delete Element's Tag" description="Delete Element's Tag" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5k99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.create.global.parameter.from.selection" commandName="Extract global parameter" description="Extract the selected text to a new global parameter" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5lN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.search.componentDependencies" commandName="Show Component Dependencies" description="Show the places where a component is referred" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5ld9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.generateDocumentation" commandName="Generate &amp;Documentation" description="Generate documentation for editor" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5lt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.expandAllFolds" commandName="Expand All" description="Expand all folds" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5l99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.checkSpell" commandName="Check Spelling" description="Check spelling on editor content" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5mN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.moveUpFeature" commandName="Move Up" description="Move Up" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5md9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.extractGlobalType" commandName="Extract global type" description="Extract global type" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5mt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.associateSchema" commandName="Associate Schema" description="Associate a schema for validating current document" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5m99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.search.references" commandName="Search References" description="Search references using the previous search scope" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5nN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.expandChildFolds" commandName="Expand Child Folds" description="Expand Child Folds" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5nd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.openInBrowser" commandName="Open in System Application" description="Open the current document in system application" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5nt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.editSettings" commandName="Edit Settings" description="Edit Settings" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5n99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.goToDefinition" commandName="Show Definition" description="Show Definition" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5oN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.extractGlobalComponent" commandName="Extract global component" description="Extract global component" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruX5od9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.prettyPrint" commandName="Pretty Print" description="Pretty print document content" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgoN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.remove.highlight" commandName="Remove highlight(s)" description="Remove highlight(s)" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgod9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.renameCurrentElement" commandName="Rename" description="Quick rename of current element" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgot9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.showHierarchy" commandName="Show Resource Hierarchy" description="Show XSD, XSL or RNG Resource Hierarchy" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgo99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.search.references.in" commandName="Search References in..." description="Search references specifying a search scope" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgpN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.create.stylesheet.from.selection" commandName="Move to another stylesheet" description="Move the selected components to another stylesheet. If the selected stylesheet does not exist it will be created." category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgpd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.create.global.variable.from.selection" commandName="Extract global variable" description="Extract the selected text to a new global variable" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgpt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.joinElements" commandName="Join Elements" description="Join the elements" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgp99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.add.component.documentation" commandName="Add component documentation" description="Add component documentation" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgqN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stepOut.xquery" commandName="Step &amp;Out" description="Step out." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgqd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.surroundWithTag" commandName="Surround with tags" description="Surround with tags" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgqt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.add.comment" commandName="Add Comment" description="Add Comment" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgq99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.addBreakpoint" commandName="Add breakpoint" description="Add a breakpoint" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgrN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.toggleLineComment" commandName="Toggle Line Comment" description="Toggle Line Comment" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgrd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.learnStr" commandName="Learn Structure" description="Learn document structure" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgrt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.create.local.variable.from.selection" commandName="Extract local variable" description="Extract the selected text to a new local variable" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgr99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.goAfterNextTag" commandName="Go after Next Tag" description="Go after Next Tag" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgsN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.removeAllMarkup" commandName="Remove All Markup" description="Removes all the markup from the selected region" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgsd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.move.rename" commandName="Move/Rename" description="Renames or moves the selected DITA Map resource." category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgst9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.validate.cached" commandName="Validate (cached)" description="Validate editor content with cached resources" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgs99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.insertXInclude" commandName="Insert XInclude" description="Insert XInclude" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgtN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.showDefinition" commandName="Show Definition" description="Show Definition" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgtd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.findAllElements" commandName="Find all elements" description="Find all elements" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgtt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.watch" commandName="Watch expression" description="Watch expression" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgt99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.collapseOtherFolds" commandName="Collapse Other Folds" description="Collapse Other Folds" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYguN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stepInto.xslt" commandName="Step Into" description="Step in." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgud9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.changeSchemaVersion" commandName="Change XML Schema version" description="Change XML Schema version" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgut9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.goToMatchingBracket" commandName="Go to Matching Bracket" description="Go to Matching Bracket" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgu99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.validateExternal" commandName="Validate with" description="Validate editor content using external specified schema" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgvN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.associateXsltStylesheet" commandName="Associate XSLT Stylesheet" description="Associate an XSLT stylesheet with an xml-stylesheet instruction" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgvd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.highlight" commandName="Highlight" description="Highlight" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgvt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.create.template.parameter.from.selection" commandName="Extract template parameter" description="Extract the selected text to a new template parameter" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgv99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.copyXPath" commandName="Copy XPath" description="Copy XPath expression to clipboard" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgwN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.track.changes" commandName="Track Changes" description="Track Changes" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgwd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.wsdlAnalyser" commandName="WSDL SOAP Analyser" description="WSDL SOAP Analyser" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgwt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.toggleComment" commandName="Toggle Comment" description="Toggle Comment" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgw99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.redefineComponent" commandName="Redefine Component" description="Redefines the selected component(s)" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgxN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.run.xquery" commandName="Run" description="Run the current debugging session." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgxd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.moveDownFeature" commandName="Move Down" description="Move Down" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgxt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.pasteAsReference" commandName="Paste as Reference" description="Paste as Reference" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgx99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stop.xquery" commandName="Stop" description="Stop the current debugging session." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgyN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.reject.track.change" commandName="Reject Change" description="Reject Change" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgyd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.search.occurrences.in.editor" commandName="Search Occurrences in Current File" description="Search occurrences in the current file" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgyt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.flattenSchema" commandName="Flatten Schema" description="Flatten Schema" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgy99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.transformConfiguration" commandName="Configure Transformation Scenario(s)" description="Configure Transformation Scenario(s)" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgzN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stepOver.xslt" commandName="Step O&amp;ver" description="Step over." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgzd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.toggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle a breakpoint's state" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgzt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.openResource" commandName="Open resource" description="Open resource" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYgz99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.report.reportProblem" commandName="Report &lt;oXygen/> problem" description="Report &lt;oXygen/> problem" category="_yXkbK4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ruYg0N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.splitElement" commandName="Split Element" description="Split the current element in two" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg0d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.run2End.xslt" commandName="Run to &amp;End" description="Run to the end of debugging session." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg0t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xmlToJSON" commandName="XML to JSON" description="XML to JSON" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg099cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.run2Cursor.xslt" commandName="Run to &amp;Cursor" description="Run to the cursor." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg1N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.surroundWithCDATA" commandName="Surround With CDATA" description="Surround in CDATA" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg1d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.remove.comment" commandName="Remove Comment" description="Remove Comment" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg1t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.collapseChildFolds" commandName="Collapse Child Folds" description="Collapse Child Folds" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg199cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.predefined.color.highlight.3" commandName="Red" description="Red" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg2N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.predefined.color.highlight.2" commandName="Blue" description="Blue" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg2d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.predefined.color.highlight.1" commandName="Green" description="Green" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg2t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.predefined.color.highlight.0" commandName="Yellow" description="Yellow" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg299cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.predefined.color.highlight.5" commandName="Magenta" description="Magenta" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg3N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.predefined.color.highlight.4" commandName="Light cyan" description="Light cyan" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg3d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.shiftLeft" commandName="ShiftLeft" description="Shift left current selection" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg3t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.learnWord" commandName="Learn Word" description="Learn word at caret position" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg399cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.renameElement" commandName="Rename Element" description="Rename current element" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg4N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.search.declaration.in" commandName="Search Declaration in..." description="Search declaration specifying a search scope" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg4d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stepOut.xslt" commandName="Step &amp;Out" description="Step out." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg4t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.joinAndNormalize" commandName="Join and Normalize Lines" description="Join and Normalize Lines" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg499cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.selectAttributes" commandName="Select Attributes" description="Select element's attributes" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg5N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.makeOptionalComponent" commandName="Optional" description="Make optional" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg5d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.extract.attribute.from.element" commandName="Extract Attributes From Element" description="Extract attribute as xsl:attribute" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg5t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.clearValidateMarkers" commandName="Clear Validation Markers" description="Clear validation markers for current editor" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg599cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.convert" commandName="Convert to" description="Convert with Schema Converter/Generator" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg6N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.css.minifyCss" commandName="Minify CSS" description="Minify CSS" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg6d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.goToMatchingTag" commandName="Go to Matching Tag" description="Go to Matching Tag" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg6t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.selectContent" commandName="Select Content" description="Select element's content" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg699cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.urlsupport.openURL" commandName="Open URL" description="Open an URL" category="_yXkbJIAlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_ruYg7N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.selectElement" commandName="Select element" description="Select element" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg7d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.shiftRight" commandName="ShiftRight" description="Shift right current selection" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg7t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.run.xslt" commandName="Run" description="Run the current debugging session." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg799cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.indentSelection" commandName="Indent Selection" description="Indent current selection" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg8N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.pause.xslt" commandName="Pause" description="Pause the current debugging session." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg8d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.removeText" commandName="Remove Text" description="Removes text from the selected region" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg8t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.verifySignature" commandName="Verify Signature" description="Verify Signature" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg899cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.editAnnotations" commandName="Edit Annotations" description="Edit Annotations" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg9N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.comment.track.change" commandName="Comment change" description="Comment change" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg9d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.run2End.xquery" commandName="Run to &amp;End" description="Run to the end of debugging session." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg9t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.wsdlDocumentation" commandName="WSDL Documentation" description="WSDL Documentation" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg999cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.create.template.from.selection" commandName="Extract template" description="Extract the selected text to a new named template." category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg-N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.editAttributes" commandName="Edit Attributes" description="Edit Attributes" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg-d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.removeFeature" commandName="Remove feature" description="Remove feature" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg-t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.makeReferenceComponent" commandName="Reference" description="Make reference to a component" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg-99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.editReference" commandName="Edit Reference" description="Edit Reference" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruYg_N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.selectParentElement" commandName="Select parent" description="Select parent" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHsN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stepOver.xquery" commandName="Step O&amp;ver" description="Step over." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHsd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.transform" commandName="Apply Transformation Scenario(s)" description="Apply Transformation Scenario(s)" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHst9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xpath" commandName="XPath" description="Apply an XPath expression" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHs99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.pause.xquery" commandName="Pause" description="Pause the current debugging session." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHtN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.run2Cursor.xquery" commandName="Run to &amp;Cursor" description="Run to the cursor." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHtd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.search.declaration" commandName="Search Declaration" description="Search declaration using the previous search scope" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHtt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.prettyPrintElement" commandName="Format and Indent Element" description="Format and Indent Element" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHt99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.surroundWithLastTag" commandName="Surround with last tag" description="Surround with last tag" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHuN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.codeTemplates" commandName="Code Templates" description="Code Templates" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHud9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.sign" commandName="Sign" description="Sign" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHut9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.configure.validation.scenario" commandName="Configure Validation Scenario(s)" description="Configure Validation Scenario(s)" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHu99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.content.no.color.highlight" commandName="No color (erase)" description="No color (erase)" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHvN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.openExternalSchema" commandName="Open External Schema" description="Open External Schema" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHvd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.stop.highlighting" commandName="Stop highlighting" description="Stop highlighting" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHvt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.validate" commandName="Validate" description="Validate editor content" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHv99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.edit.comment" commandName="Edit comment" description="Edit comment" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHwN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.escapeSelection" commandName="Escape Selection" description="Escape Selection" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHwd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.openFile" commandName="Open file" description="Open file" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHwt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.rename.component" commandName="Rename Component in" description="Rename component in current working set" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHw99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stop.xslt" commandName="Stop" description="Stop the current debugging session." category="_ruXSgt9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHxN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.rename.inplace.component" commandName="Rename Component" description="Rename component in current file" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHxd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.addFeature" commandName="Add feature" description="Add feature" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHxt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.canonicalize" commandName="Canonicalize" description="Canonicalize" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHx99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.overrideComponent" commandName="Override Component" description="Overrides the selected component(s)" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHyN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.debugScenario" commandName="Debug Scenario" description="Debug Scenario" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHyd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.manage.reviews" commandName="Manage reviews" description="Manage reviews" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHyt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.goToDefinition" commandName="Go to definition" description="Go to definition" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHy99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.xsd.makeUnboundedComponent" commandName="Unbounded" description="Make unbounded" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHzN9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.goAfterPreviousTag" commandName="Go after Previous Tag" description="Go after Previous Tag" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHzd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.author.accept.track.change" commandName="Accept Change" description="Accept Change" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHzt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.saveStr" commandName="Save Structure" description="Save learned document structure" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZHz99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.toggleFold" commandName="Toggle Fold" description="Toggle Fold" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZH0N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.importEntities" commandName="Import Entities" description="Import Entities" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZH0d9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.wellform" commandName="Wellform Check" description="Check editor content to be wellformed" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZH0t9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.showDependences" commandName="Show Resource Dependencies" description="Show XSD, XSL or RNG Resource Dependencies" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZH099cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.stepInto.xquery" commandName="Step Into" description="Step in." category="_ruXSgd9cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_ruZH1N9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.unescapeSelection" commandName="Unescape Selection" description="Unescape Selection" category="_ruXSg99cEeSKbvIcVz4E9w"/>
  <commands xmi:id="_r8fOEN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.verifySignature" commandName="Verify Signature..." description="Verify Signature" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8fOEd9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.xmlToJSON" commandName="XML to JSON..." description="XML to JSON" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8fOEt9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.sign" commandName="Sign..." description="Sign" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8f1IN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.canonicalize" commandName="Canonicalize..." description="Canonicalize" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8f1Id9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.regexpBuilder" commandName="XML Schema Regular Expression Builder..." description="XML Schema regular expression builder" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8f1It9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.generateSchema" commandName="Convert DB Structure to XML Schema..." description="Convert &amp;DB Structure to XML Schema" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8f1I99cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.generateXML" commandName="Generate Sample XML Files..." description="Generate Sample XML Files" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8f1JN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.wsdlDocumentation" commandName="WSDL Documentation..." description="Create WSDL documentation" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8f1Jd9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.xqDocumentation" commandName="XQuery Documentation..." description="Create XQuery documentation" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8oYAN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.schemaDocumentation" commandName="XML Schema Documentation..." description="Create XML Schema documentation" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8oYAd9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.stylesheetDocumentation" commandName="Stylesheet Documentation..." description="Create stylesheet documentation" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8oYAt9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.tools.actionSet/com.oxygenxml.editor.actions.tools.trang" commandName="Generate/Convert Schema..." description="Generate/Convert Schema" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8oYA99cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xmlrefactory.actionSet/com.oxygenxml.editor.actions.xmlrefactory.deleteElementTags" commandName="Delete Element Tags" description="Delete the current element tags" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8oYBN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xmlrefactory.actionSet/com.oxygenxml.editor.actions.xmlrefactory.joinElements" commandName="Join Elements" description="Join current elements" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_EN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xmlrefactory.actionSet/com.oxygenxml.editor.actions.xmlrefactory.splitElement" commandName="Split Element" description="Split current element" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_Ed9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xmlrefactory.actionSet/com.oxygenxml.editor.actions.xmlrefactory.renamePrefix" commandName="Rename Prefix..." description="Rename current element's prefix" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_Et9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xmlrefactory.actionSet/com.oxygenxml.editor.actions.xmlrefactory.renameElement" commandName="Rename Element..." description="Rename current element" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_E99cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xslrefactory.actionSet/com.oxygenxml.editor.actions.refactoring.renameInProject" commandName="Rename Component..." description="Rename component in current working set" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_FN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xslrefactory.actionSet/com.oxygenxml.editor.actions.refactoring.createStylesheet" commandName="Move to another stylesheet" description="Move the selected components to another stylesheet. If the selected stylesheet does not exist it will be created." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_Fd9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.xslrefactory.actionSet/com.oxygenxml.editor.actions.refactoring.createTemplate" commandName="Extract template" description="Extract the selected text to a new named template." category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_Ft9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.source.actionSet/com.oxygenxml.editor.actions.source.prettyPrintElement" commandName="Format and Indent Element" description="&amp;Format and Indent Element" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8o_F99cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.source.actionSet/com.oxygenxml.editor.actions.source.indentSelection" commandName="Indent Selection" description="&amp;Indent Selection" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8pmIN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.source.actionSet/com.oxygenxml.editor.actions.source.importEntitiesList" commandName="Import Entities List..." description="Import Entities List" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8pmId9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.source.actionSet/com.oxygenxml.editor.actions.source.insertXInclude" commandName="Insert XInclude..." description="Insert XInclude" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8pmIt9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.source.actionSet/com.oxygenxml.editor.actions.source.unescapeSelection" commandName="Unescape Selection..." description="Unescape Selection" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8pmI99cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.source.actionSet/com.oxygenxml.editor.actions.source.escapeSelection" commandName="Escape Selection..." description="Escape Selection" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <commands xmi:id="_r8pmJN9cEeSKbvIcVz4E9w" elementId="AUTOGEN:::com.oxygenxml.editor.source.actionSet/com.oxygenxml.editor.actions.source.toggleComment" commandName="Toggle Comment" description="&amp;Toggle Comment" category="_yXkbP4AlEeS_EpJ0MUkvAA"/>
  <addons xmi:id="_yXMAqIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_yXMAqYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_yXMAqoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_yXMAq4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_yXMArIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_yXMArYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_yXMAroAlEeS_EpJ0MUkvAA" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_yXMAr4AlEeS_EpJ0MUkvAA" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_yXMAsIAlEeS_EpJ0MUkvAA" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_yXMAsYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_yYfoMIAlEeS_EpJ0MUkvAA" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <categories xmi:id="_yXj0EIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_yXj0EYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_yXj0EoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_yXj0E4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jst.pagedesigner.pagelayout" name="Web Page Editor Layout"/>
  <categories xmi:id="_yXj0FIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_yXj0FYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_yXj0FoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_yXj0F4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_yXj0GIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_yXj0GYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.category.source" name="Source" description="JavaScript Source Actions"/>
  <categories xmi:id="_yXj0GoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_yXj0G4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_yXkbIIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_yXkbIYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_yXkbIoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="%commands.category.name" description="%commands.category.description"/>
  <categories xmi:id="_yXkbI4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.debug.ui.category" name="JavaScript Debug" description="Tooling for debugging JavaScript"/>
  <categories xmi:id="_yXkbJIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_yXkbJYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_yXkbJoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.rse.ui.commands.category" name="Remote Systems"/>
  <categories xmi:id="_yXkbJ4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_yXkbKIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaedtor.10x" name="ASA 9.x table schema editor"/>
  <categories xmi:id="_yXkbKYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_yXkbKoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.emf.codegen.ecore.ui.Commands" name="EMF Code Generation" description="Commands for the EMF code generation tools"/>
  <categories xmi:id="_yXkbK4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_yXkbLIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.result.category" name="SQL Results View"/>
  <categories xmi:id="_yXkbLYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_yXkbLoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_yXkbL4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_yXkbMIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_yXkbMYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.team.cvs.ui.actionSet" name="CVS" description="Actions that apply when working with CVS repositories"/>
  <categories xmi:id="_yXkbMoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_yXkbM4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_yXkbNIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.datatools.sqltools.sqleditor.category" name="Database Tools" description="Database Development tools"/>
  <categories xmi:id="_yXkbNYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_yXkbNoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_yXkbN4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_yXkbOIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.jpaMetadataConversionCommands" name="JPA Metadata Conversion"/>
  <categories xmi:id="_yXkbOYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.xml.views.XPathView" name="XPath"/>
  <categories xmi:id="_yXkbOoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_yXkbO4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.jpt.jpa.ui.jpaStructureViewCommands" name="JPA Structure View"/>
  <categories xmi:id="_yXkbPIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_yXkbPYAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_yXkbPoAlEeS_EpJ0MUkvAA" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_yXkbP4AlEeS_EpJ0MUkvAA" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_yXkbQIAlEeS_EpJ0MUkvAA" elementId="org.eclipse.wst.jsdt.ui.category.refactoring" name="Refactor - JavaScript" description="JavaScript Refactoring Actions"/>
  <categories xmi:id="_ruXSgd9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.category.xquery" name="XQuery Debugger" description="XQuery Debugger commands"/>
  <categories xmi:id="_ruXSgt9cEeSKbvIcVz4E9w" elementId="com.oxygenxml.debugger.category.xslt" name="XSLT Debugger" description="XSLT Debugger commands"/>
  <categories xmi:id="_ruXSg99cEeSKbvIcVz4E9w" elementId="com.oxygenxml.editor.category.OxygenXMLEditor" name="&lt;oXygen/> XML Editor" description="&lt;oXygen/> XML Editor commands"/>
</application:Application>
