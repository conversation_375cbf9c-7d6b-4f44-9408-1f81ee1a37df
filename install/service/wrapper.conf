#********************************************************************
# TestWrapper Properties
#
# NOTE - Please use src/conf/wrapper.conf.in as a template for your
#        own application rather than the values used for the
#        TestWrapper sample.
#********************************************************************
# Java Application
#wrapper.java.command=C:\java17_03\bin\java
wrapper.java.command=C:\Program Files\Eclipse Adoptium\jdk-8.0.372.7-hotspot\jre\bin\java

# Java Main class.  This class must implement the WrapperListener interface
#  or guarantee that the WrapperManager class is initialized.  Helper
#  classes are provided to do this for you.  See the Integration section
#  of the documentation for details.
wrapper.java.mainclass=com.kskyb.magent.service.ServiceWrapper

# Java Classpath (include wrapper.jar)  Add class path elements as
#  needed starting from 1
wrapper.java.classpath.1=../lib/log4j-1.2.15.jar
wrapper.java.classpath.2=../lib/mysql-connector-java-3.1.12-bin.jar
#wrapper.java.classpath.3=../lib/msbase.jar
#wrapper.java.classpath.4=../lib/mssqlserver.jar
#wrapper.java.classpath.5=../lib/msutil.jar
wrapper.java.classpath.3=../lib/sqljdbc42.jar
wrapper.java.classpath.4=../lib/mAgent.jar
wrapper.java.classpath.5=../bin
wrapper.java.classpath.6=./wrapper.jar
wrapper.java.classpath.7=../lib/ojdbc14.jar

# Java Library Path (location of Wrapper.DLL or libwrapper.so)
wrapper.java.library.path.1=.

# Java Additional Parameters
wrapper.java.additional.1=-Dfile.encoding=KSC5601
wrapper.java.additional.2=-Dconfig.path=../bin/config.ini
wrapper.java.additional.3=-Dresource.context=com.kskyb.magent.ResourceContext

# Initial Java Heap Size (in MB)
#wrapper.java.initmemory=3

# Maximum Java Heap Size (in MB)
#wrapper.java.maxmemory=64

# Application parameters.  Add parameters as needed starting from 1
wrapper.app.parameter.1=start

#********************************************************************
# Wrapper Logging Properties
#********************************************************************
# Format of output for the console.  (See docs for formats)
wrapper.console.format=PM

# Log Level for console output.  (See docs for log levels)
wrapper.console.loglevel=INFO

# Log file to use for wrapper output logging.
wrapper.logfile=../log/wrapper.log

# Format of output for the log file.  (See docs for formats)
wrapper.logfile.format=LPTM

# Log Level for log file output.  (See docs for log levels)
wrapper.logfile.loglevel=INFO

# Maximum size that the log file will be allowed to grow to before
#  the log is rolled. Size is specified in bytes.  The default value
#  of 0, disables log rolling.  May abbreviate with the 'k' (kb) or
#  'm' (mb) suffix.  For example: 10m = 10 megabytes.
wrapper.logfile.maxsize=0

# Maximum number of rolled log files which will be allowed before old
#  files are deleted.  The default value of 0 implies no limit.
wrapper.logfile.maxfiles=0

# Log Level for sys/event log output.  (See docs for log levels)
wrapper.syslog.loglevel=NONE

#********************************************************************
# Wrapper Windows Properties
#********************************************************************
# Title to use when running as a console
wrapper.console.title=KskyB MAgent Console

#********************************************************************
# Wrapper Windows NT/2000/XP Service Properties
#********************************************************************
# WARNING - Do not modify any of these properties when an application
#  using this configuration file has been installed as a service.
#  Please uninstall the service before modifying this section.  The
#  service can then be reinstalled.

# Name of the service
wrapper.ntservice.name=KskyB-MAgent

# Display name of the service
wrapper.ntservice.displayname=KskyB MAgent Application

# Description of the service
wrapper.ntservice.description=KskyB MAgent Application Description

# Service dependencies.  Add dependencies as needed starting from 1
wrapper.ntservice.dependency.1=

# Mode in which the service is installed.  AUTO_START or DEMAND_START
wrapper.ntservice.starttype=AUTO_START

# Allow the service to interact with the desktop.
wrapper.ntservice.interactive=false

