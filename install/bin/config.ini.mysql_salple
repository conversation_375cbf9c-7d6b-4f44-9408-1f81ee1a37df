#임시파일을 저장하는 디렉토리를 지정합니다_
#디렉토리가 꼭 존재하여야 하며 한글이 포함된 디렉토리는 인식하지 못합니다_
#디렉토리구분자는 꼭! '/'로 구분해 주어야함_
################################################################
#로그 디렉토리 경로 설정
log_dir=D:/1.개발/1.SMS/JAVA Agent/Java_agent_final/kskyb.agent/install/log
################################################################
################################################################
#MMS 컨텐츠 디렉토리 경로 설정
mms_content_dir=D:/1.개발/1.SMS/JAVA Agent/Java_agent_final/kskyb.agent/install/content
################################################################
io_charset=KSC5601
#관리포트 : 시스템에서 사용할 포트
admin_port=11112
#서버와 통신 에러시 재시도 interval ( sec )
server_error_wait_interval=5
########################## 디비연결정보 ########################
#사용하는 데이터 베이스의 타입을 지정합니다_
#각 디비를 지정할 때 vender가 제공하는 jdbc driver 를 agent_bat의 classpath에 추가해 주어야합니다
## MYSQL #######################################################
db_type=mysql
#데이터베이스 서버의 아이피와 리스닝 포트를 지정합니다_
db_connect_driver=com.mysql.jdbc.Driver
db_connect_url=*************************************
#데이터베이스 로그인 아이디와 패스워드를 지정합니다_
db_connect_id=neomms
db_connect_pass=neomms
#데이터베이스 케릭터 셋
db_base_charset=KSC5601
db_in_charset=KSC5601
db_out_charset=KSC5601
############################ SMS ###############################
#SMS 서비스 사용 여부를 지정합니다_ ( true: 사용 / false:미사용 )
sms_service=true
sms_send_service=true
sms_report_service=true
#SMS 전송 유효시간
sms_send_ttl=4320
#sms_send_limit_time=1800~0600

#SMS DB Move 유효 (hour)
sms_migrate_ttl=36
#SMS 서버 정보
sms_gw_ip=*************
sms_gw_port=52000
#sms_gw_ip=*************
#sms_gw_port=47000
################################################################
sms_gw_auth_id1=l002
sms_gw_auth_password1=p002
#sms_gw_auth_id1=smstest
#sms_gw_auth_password1=smstest
sms_gw_auth_id2=l003
sms_gw_auth_password2=p003
sms_gw_auth_id3=l004
sms_gw_auth_password3=p004
################################################################
#멀티 아이디 연결시 true 로 서정
sms_gw_multi_report=false
################################################################
#GW 연결 체크 주기 (ms)
sms_gw_send_check_interval=1

#PING cycle (sec)
sms_gw_send_ping_interval=10
#GW Report Check 주기 (sec)
sms_gw_report_check_interval=1
#GW Report 최대 대기시간 (sec)
sms_gw_report_ping_interval=10
#sms 전송버퍼
sms_send_buffer_limit=2000
sms_send_buffer_empty_wait=5
#SMS 로그버퍼
sms_report_buffer_limit=2000
#SMS 로그 마이그레이션 플래그
sms_migrate_log=true
sms_migrate_log_table=log

#SMS 로그 마이그레이션 주기
sms_migrate_log_interval=60
#fixed_delay: 종료후 일정시간 기다린후 시작 ( 1 )
#fixed-period: 지정된 시간 구간에서 실행 ( 0 )
sms_migrate_log_interval_pattern=1
#SMS 추가 조건절
#sms_select_target_addon_query=LIMIT 100
#sms_select_additional_info=[key|value]
#원본테이블 폴링 주기
sms_select_target_interval=10
#wait 방식
#fixed_delay: 종료후 일정시간 기다린후 시작 ( 1 )
#fixed-period: 지정된 시간 구간에서 실행 ( 0 )
sms_select_target_interval_pattern=1
#리포트 수신시 EOF 를 수신하였을 경우 재시도 횟수
sms_report_eof_wait_count=10
#리포트 수신시 EOF 를 수신하였을 경우 waiting 시간 ( sec )
sms_report_eof_wait_time=5
#메세지 전송결과 수신시 EOF 를 수신하였을 경우 재시도 횟수
sms_send_eof_wait_count=10
#메세지 전송결과 수신시 EOF 를 수신하였을 경우 waiting 시간 ( sec )
sms_send_eof_wait_time=2

############################ MMS ###############################
#MMS 서비스 유무
mms_service=true
mms_send_service=true
mms_report_service=true
#MMS 전송 유효시간
mms_send_ttl=4320
#mms_send_limit_time=1800~0600
#MMS DB Move 유효 ( hour )
mms_migrate_ttl=60
#MMS 서버 접속 정보
mms_gw_ip=*************
mms_gw_port=36000
#mms_gw_ip=*************
#mms_gw_port=36000
################################################################
mms_gw_auth_id1=mms_batch1
mms_gw_auth_password1=mms_batch1
##mms_gw_auth_id2=id2
##mms_gw_auth_password2=id2
##mms_gw_auth_id3=id3
##mms_gw_auth_password3=pwd3
##mms_gw_auth_id4=id4
##mms_gw_auth_password4=pwd4
################################################################
#멀티 아이디 연결시 true 로 서정
mms_gw_multi_report=false
################################################################
#GW 연결 체크 주기 (sec)
mms_gw_send_check_interval=5
#GW Report Check 주기 (sec)
mms_gw_report_check_interval=5
#PING cycle
#GW Send 최대 대기시간 (sec)
mms_gw_send_ping_interval=10
#GW Report 최대 대기시간 (sec)
mms_gw_report_ping_interval=10
#원본테이블 폴링 주기 (sec)
mms_select_target_interval=10
#wait 방식
#fixed_delay: 종료후 일정시간 기다린후 시작 ( 1 )
#fixed-period: 지정된 시간 구간에서 실행 ( 0 )
mms_select_target_interval_pattern=1
#MMS 전송버퍼
mms_send_buffer_limit=2000
#MMS 로그버퍼
mms_result_buffer_limit=2000
#원본테이블 추출 추가 쿼리
mms_select_target_addon_query_mms=
#로그테이블 이동
mms_migrate_log=true
mms_migrate_log_table=log
#MMS 로그 마이그레이션 주기
mms_migrate_log_interval=300
#fixed_delay: 종료후 일정시간 기다린후 시작 ( 1 )
#fixed-period: 지정된 시간 구간에서 실행 ( 0 )
mms_migrate_log_interval_pattern=1
#리포트 수신시 EOF 를 수신하였을 경우 재시도 횟수
mms_report_eof_wait_count=10
#리포트 수신시 EOF 를 수신하였을 경우 waiting 시간 ( sec )
mms_report_eof_wait_time=10
#메세지 전송결과 수신시 EOF 를 수신하였을 경우 재시도 횟수
mms_send_eof_wait_count=10
#로그레벨
console_log_level=DEBUG
file_log_level=DEBUG

table_kb_tran=kb_tran
table_kb_tran_log=kb_tran_log
table_kb_mms_grp=kb_mms_grp
table_kb_mms_tran=kb_mms_tran
table_kb_mms_contents=kb_mms_contents
table_kb_mms_grp_log=kb_mms_grp_log
table_kb_mms_tran_log=kb_mms_tran_log
table_kb_mms_contents_log=kb_mms_contents_log
table_kb_mms_msg=kb_mms_msg
table_kb_mms_msg_log=kb_mms_msg_log
################################################################
#암호화 방식 (DEFAULT : base64)
################################################################
#sms.encryptType=base64
#mms.encryptType=base64
sms.encryptType=aes_base64
mms.encryptType=aes_base64
################################################################
################################################################
#common.log 파일에 sql 로그 남김
logging.sql=false
################################################################
################################################################
#buffer 사용 여부 default : true
sms.send.use.buffer=true
sms.report.use.buffer=true
mms.send.use.buffer=true
mms.report.use.buffer=true
