
#로그 디렉토리 경로 설정
#log_dir=E:/COMPANY/00.Default/Java_agent_mms_sms/kskyb.agent/install/log/
log_dir=D:/Java_agent_mms_sms/kskyb.agent/install/log/
#MMS 컨텐츠 디렉토리 경로 설정
mms_content_dir=../content/

io_charset=KSC5601

io_charset=KSC5601

#관리포트 : 시스템에서 사용할 포트
admin_port=11113
server_error_wait_interval=5

### DB Config ###
#db_type=oracle
#db_connect_driver=oracle.jdbc.driver.OracleDriver
#db_connect_url=****************************************

db_type=mssql
db_connect_driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
#db_connect_url=****************************************************
db_connect_url=****************************************************;encrypt=true;trustServerCertificate=true
#db_connect_url=****************************************************;encrypt=true

#db_type=mysql
#db_connect_driver=com.mysql.jdbc.Driver
#db_connect_url=***********************************

server_retry_count=3

db_connect_id=test
db_connect_pass=kskyb9466

db_base_charset=KSC5601
db_in_charset=KSC5601
db_out_charset=KSC5601

ext_report=2106,2100,-1

############################ SMS ###############################

sms_service=false
sms_send_service=false
sms_report_service=false

sms_send_ttl=4320
sms_migrate_ttl=36

sms_gw_ip=***************
sms_gw_port=47000


### Account ###
#sms_gw_auth_id1=smst_1
#sms_gw_auth_password1=smst_1

#sms_gw_auth_id1=kms_s1
#sms_gw_auth_password1=kms_s1!

sms_gw_auth_id1=jy_s1
sms_gw_auth_password1=jy_s1!

sms_gw_multi_report=false

sms_gw_send_check_interval=1
sms_gw_report_check_interval=1
#sms_gw_send_check_interval=2
sms_gw_send_ping_interval=8
#sms_gw_report_check_interval=5
sms_gw_report_ping_interval=1

sms_send_buffer_limit=500
sms_send_buffer_empty_wait=5
sms_report_buffer_limit=2000

sms_migrate_log=true
sms_migrate_log_table=log

sms_migrate_log_interval=30
sms_migrate_log_interval_pattern=1

sms_select_target_interval=5
sms_select_target_interval_pattern=1

sms_report_eof_wait_count=10
sms_report_eof_wait_time=5

sms_send_eof_wait_count=10
sms_send_eof_wait_time=2
sms.logbuffer.validation.query=true

############################ MMS ###############################

mms_service=true
mms_send_service=true
mms_report_service=true

mms_send_ttl=4320
mms_migrate_ttl=36

mms_gw_ip=***************
mms_gw_port=36000

### Account ###
#mms_gw_auth_id1=mmst_2
#mms_gw_auth_password1=mmst_2

#mms_gw_auth_id1=kms_l1
#mms_gw_auth_password1=kms_l1!

mms_gw_auth_id1=jy_l1
mms_gw_auth_password1=jy_l1!

mms_gw_multi_report=false


mms_gw_send_check_interval=2
mms_gw_send_ping_interval=8
mms_gw_report_check_interval=5
mms_gw_report_ping_interval=20
mms_select_target_interval=5
mms_select_target_interval_pattern=1

mms_send_buffer_limit=2
mms_result_buffer_limit=2

mms_select_target_addon_query_mms=

mms_migrate_log=true
mms_migrate_log_table=log

mms_migrate_log_interval=30
mms_migrate_log_interval_pattern=1

mms_report_eof_wait_count=3
mms_report_eof_wait_time=1
mms_send_eof_wait_count=10
mms_send_fail_retry=false

#log_level
#console_log_level=INFO
#file_log_level=INFO
console_log_level=DEBUG
file_log_level=DEBUG

table_kb_tran=MTS_SMS_MSGy
table_kb_tran_log=MTS_SMS_MSG_LOGy
table_kb_mms_contents=MTS_MMS_MSG_CONTENTSy
table_kb_mms_contents_log=MTS_MMS_MSG_CONTENTS_LOGy
table_kb_mms_msg=MTS_MMS_MSGy
table_kb_mms_msg_log=MTS_MMS_MSG_LOGy

#table_kb_tran=TBL_MTS_SMS_MSG
#table_kb_tran_log=TBL_MTS_SMS_MSG_LOG
#table_kb_mms_contents=TBL_MTS_MMS_CONTENTS
#table_kb_mms_contents_log=TBL_MTS_MMS_CONTENTS_LOG
#table_kb_mms_msg=TBL_MTS_MMS_MSG
#table_kb_mms_msg_log=TBL_MTS_MMS_MSG_LOG

logging.sql=true

sms.send.use.buffer=true
sms.report.use.buffer=true
mms.send.use.buffer=true
mms.report.use.buffer=true

mms.sendAllEncrypt=false
db_pw_encryption=false
mms.logbuffer.validation.query=false


#sms_send_limit_time=1026~1028
sms_send_limit_time2=1400~1500
mms_send_limit_time2=1400~2000
#mms_send_limit_time=1026~1128
