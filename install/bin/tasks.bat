title MAgent client
echo off

set MAGENT_HOME=F:\eclipse-workspace-202006\Java_agent_mms_sms\kskyb.agent\install
set JAVA_HOME="C:\Program Files\Eclipse Adoptium\jdk-8.0.372.7-hotspot\jre"

set middleware_cp=%MAGENT_HOME%\lib\mAgent.jar

set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\log4j-1.2.15.jar
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\commons-dbcp-1.2.1.jar
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\commons-pool-1.3.jar
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\fscontext.jar
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\providerutil.jar
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\log4j-1.2.15.jar
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\wrapper.jar

rem oracle
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\ojdbc14.jar

rem mssql 2000 (아래 sqljdbc4_v2.jar 에서 안될 경우 사용)
rem set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\msbase.jar
rem set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\mssqlserver.jar
rem set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\msutil.jar

rem mssql 2000 이상 java 6.0 이상
rem set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\sqljdbc4_v2.jar
rem set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\jtds-1.2.jar
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\sqljdbc42.jar

rem mysql 4
set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\mysql-connector-java-3.1.12-bin.jar

rem mysql 5 이상
rem set middleware_cp=%middleware_cp%;%MAGENT_HOME%\lib\mysql-connector-java-5.1.6-bin.jar

set magent_min_heap=56M
set magent_max_heap=512M

set JAVA_OPT=-Xms%magent_min_heap%
set JAVA_OPT=%JAVA_OPT% -Xmx%magent_max_heap%
set JAVA_OPT=%JAVA_OPT% -classpath %middleware_cp%

rem %JAVA_HOME%\bin\java %JAVA_OPT% com.kskyb.magent.Main start
%JAVA_HOME%\bin\java %JAVA_OPT% com.kskyb.magent.InternalCommand tasks

pause
