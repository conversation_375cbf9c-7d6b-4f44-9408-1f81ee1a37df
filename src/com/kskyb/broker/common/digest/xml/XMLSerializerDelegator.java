package com.kskyb.broker.common.digest.xml;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Constructor;
import java.util.List;
import java.util.Map;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import com.kskyb.broker.common.digest.xml.resource.XMLInputSourceContext;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.lang.Constant;
import com.kskyb.broker.lang.DigestClassLoader;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ReflectUtil;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.lang.ThreadAttribute;
import com.kskyb.broker.util.StringUtils;
import com.kskyb.broker.util.TypeConvertUtil;
import com.kskyb.broker.util.Utilities;
import com.kskyb.broker.util.xml.XMLUtil;
import com.kskyb.broker.util.xml.XMLWriter;


/**
 * XMLSerializer를 사용하기위한 다양한 접근 패턴을 제공한다.
 * 
 * <AUTHOR>
 * 
 */
public final class XMLSerializerDelegator {
	// ----------------------------------------------------------------------------------------------- Analyze
	/**
	 * @param <T>
	 * @param clazz
	 * @param resourceName
	 * @param rootType
	 * @return
	 */
	public static final <T> T loadFromClassResource(final Class clazz,
													final String resourceName,
													final Class<T> rootType) {
		try {
			final Document doc = XMLInputSourceContext.create(	clazz,
																resourceName,
																true);
			return loadSingleInstance(	doc,
										rootType);
		}
		catch (Throwable thw) {
			ExceptionGW.filterThrowable(StringUtils.getSequenceMappedMessage("load.single.instance.error"),
										thw);
			return null;
		}
	}

	/**
	 * @param <T>
	 * @param in
	 * @param rootType
	 * @param map
	 * @return
	 */
	public static final <T> T loadFromStream(	final InputStream in,
												final Class<T> rootType) {
		try {
			final Document doc = XMLInputSourceContext.createFromStream(in,
																		true);
			return loadSingleInstance(	doc,
										rootType);
		}
		catch (Throwable thw) {
			ExceptionGW.filterThrowable(StringUtils.getSequenceMappedMessage("load.single.instance.error"),
										thw);
			return null;
		}
	}

	/**
	 * @param source
	 * @param loader
	 * @param rootType
	 * @return
	 * @throws Throwable
	 */
	public static final <T> T loadFromString(	final String source,
												final Class<T> rootType) {
		try {
			return loadSingleInstance(	XMLInputSourceContext.create(	source,
																		false),
										rootType);
		}
		catch (Throwable thw) {
			ExceptionGW.filterThrowable(StringUtils.getSequenceMappedMessage("load.single.instance.error"),
										thw);
			return null;
		}
	}

	/**
	 * @param <T>
	 * @param doc
	 * @param rootType
	 * @return
	 * @throws Throwable
	 */
	public static final <T> T loadSingleInstance(	final Document doc,
													final Class<T> rootType) {
		if (doc == null) {
			return null;
		}

		return XMLSerializer.getSerializer().load(	doc,
													rootType);
	}

	/**
	 * @param clazz
	 * @param name
	 * @param rootType
	 * @return
	 */
	public static final <T> T[] iterateFromClassStream(	final Class clazz,
														final String name,
														final Class<T> rootType) {
		return iterateFromStream(	clazz.getResourceAsStream(name),
									rootType);
	}

	/**
	 * @param in
	 * @param rootType
	 * @return
	 */
	public static final <T> T[] iterateFromStream(	final InputStream in,
													final Class<T> rootType) {
		try {
			final Document doc = XMLInputSourceContext.createFromStream(in,
																		false);
			return XMLSerializer.getSerializer().iterate(	doc,
															rootType);
		}
		catch (Throwable thw) {
			ExceptionGW.filterThrowable(StringUtils.getSequenceMappedMessage("stream.instance.load.error"),
										thw);
		}
		return (T[]) Constant.NULL_SINGLE_ARRAY;
	}

	/**
	 * 이름을 기준으로 지정타입의 인스턴스를 읽어온다.
	 * 
	 * @param name
	 * @return
	 */
	public static final <T> T loadInstanceByName(	final String name,
													final Class<T> type) {
		try {
			return Utilities.convert(	DigestClassLoader.getClassloader().loadClass(name).newInstance(),
										type);
		}
		catch (Throwable thw) {
			if (DebugEchoEvent.isEnable()) {
				DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"class.instance.lookup.fail",
																		name));
			}
		}

		final Document nodes = XMLInputSourceContext.createFromUri(	name,
																	true);
		if (nodes == null) {
			if (DebugEchoEvent.isEnable()) {
				DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"key.resource.empty",
																		name));
			}
			return null;
		}

		// TODO : 여러개를 읽어서 하나만 instance로 만들면 오해의 여지가 있음.<br>
		// 확인해서 일률적으로 로딩가능하도록 수정해야함.
		Object[] objs = null;

		try {
			objs = XMLSerializer.getSerializer().iterate(	nodes,
															type);

			if (objs == null || objs.length == 0) {
				if (DebugEchoEvent.isEnable()) {
					DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"key.resource.do.not.contain.runtime",
																			name));
				}
				return null;
			}

			if (objs.length != 1) {
				if (DebugEchoEvent.isEnable()) {
					DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"key.resource.contain.too.many.runtime",
																			name,
																			String.valueOf(objs.length)));
				}
				return null;
			}

			return Utilities.convert(	objs[0],
										type);
		}
		catch (Throwable thw) {
			ExceptionGW.filterThrowable(ResourceContext.getBundledMessage("iterate.error"),
										thw);
		}
		return null;
	}

	/**
	 * @param node
	 * @return
	 */
	public static final Class<?> extractClass(final Node node) {
		final String className = XMLUtil.getAttribute(	node,
														XMLDigestCodec.CLASS_INDEX);
		if (className != null) {
			return DigestClassLoader.loadSharedClass(className);
		}

		final Map<String, Class> namedMap = ThreadAttribute.getTypedAttribute(	XMLDigestConstants.THREAD_KEY_ALIAS_MAP,
																				Map.class);
		if (namedMap != null) {
			final String nodeName = node.getNodeName();
			final Class<?> clazz = namedMap.get(nodeName);
			if (clazz != null) {
				return clazz;
			}
		}

		ExceptionGW.filterMessage(StringUtils.getSequenceMappedMessage(	"missing.class.index.attribute",
																		XMLWriter.toString(node)));
		return null;
	}

	/**
	 * @param elm
	 * @param targetClass
	 * @param parent
	 * @return
	 * @throws Throwable
	 */
	public static final Object getObjectFromElement(final Element elm,
													final Class targetClass,
													final Object parent) throws XMLDigestFailException {
		if (elm == null) {
			return null;
		}

		if (targetClass != Object.class && targetClass.isInstance(elm)) {
			return elm;
		}

		final Object obj = XMLDigestCodecFactory.getFactory().decode(	parent,
																		elm);
		if (targetClass.isInstance(obj)) {
			return obj;
		}

		final Object convert = TypeConvertUtil.getParamValueBaseOnType(	targetClass,
																		obj);

		if (convert instanceof Throwable) {
			throw new XMLDigestFailException(	XMLDigestFailException.TYPE_DECODE_SINGLE_EXCEPTION,
												StringUtils.getSequenceMappedMessage(	"invalid.load.object.type",
																						obj.getClass().getName(),
																						targetClass.getName()),
												(Throwable) convert);
		}

		return convert;
	}

	/**
	 * @param elm
	 * @param map
	 * @param type
	 * @return
	 * @throws Throwable
	 */
	public static final Object createInializedObjectFromElement(final Class type,
																final Element elm) throws XMLDigestFailException {
		Element conElm = XMLUtil.getFirstNamedElement(	elm,
														XMLConstructorDecoderFactory.CONSTRUCTOR_NODE_NAME);
		if (conElm == null) {
			try {
				return ReflectUtil.newInstance(type);
			}
			catch (Throwable thw) {
				ExceptionGW.filterThrowable(StringUtils.getSequenceMappedMessage(	"create.default.instance.fail",
																					type.getName()),
											thw);
				return null;
			}
		}

		// -- 생성자를 따로 만드는 경우에 한하여 사용하지만 사용한 경우가 없다.
		List<Element> list = null;
		Object[] args = null;
		Class[] types = null;
		try {
			list = ResourceContext.getList();

			XMLUtil.extractChildElement(conElm,
										XMLConstructorDecoderFactory.CONSTRUCTOR_ARG_NODE_NAME,
										list);

			args = new Object[list.size()];
			types = new Class[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Element argElm = list.get(i);
				args[i] = XMLDigestCodecFactory.getFactory().decode(null,
																	argElm);

				String sTypeClassName = argElm.getAttribute("type");

				if (sTypeClassName != null) {
					types[i] = TypeConvertUtil.getPrimitiveClassType(sTypeClassName);
					final Object tmp = TypeConvertUtil.getParamValueBaseOnType(	types[i],
																				args[i]);
					if (tmp instanceof Throwable) {
						throw (Throwable) args[i];
					}
					args[i] = tmp;
				}
				else {
					types[i] = args[i].getClass();
				}

			}

			Constructor constructor = type.getConstructor(types);

			if (constructor == null) {
				ExceptionGW.filterMessage(StringUtils.getSequenceMappedMessage(	"element.based.constructor.not.found",
																				type.getName()));
				return null;
			}

			return constructor.newInstance(args);

		}
		catch (Throwable thw) {
			XMLDigestFailException.filter(	XMLDigestFailException.TYPE_DECODE_SINGLE_EXCEPTION,
											"",
											thw);
		}
		finally {
			ResourceContext.recycleList(list);
		}
		return null;
	}

	// ----------------------------------------------------------------------------------------------- Serialize
	/**
	 * @param target
	 * @param nodeName
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public static final String getDigestXML(final Object target,
											final String nodeName,
											final String charset) throws Exception {
		ByteArrayOutputStream out = null;
		try {
			out = ResourceContext.getByteArrayOutputStream();
			XMLSerializer.getSerializer().serialize(target,
													out,
													nodeName,
													charset);
			return out.toString(charset);
		}
		finally {
			ObjectFinalizer.close(out);
		}
	}

	/**
	 * @param targetObject
	 * @param file
	 * @param nodeName
	 * @param charset
	 * @throws Exception
	 */
	public static final void serializeToFile(	final Object targetObject,
												final File file,
												final String nodeName,
												final String charset) throws Exception {
		// -- mandatory check -- //
		if (file == null) {
			ExceptionGW.filterMessage(StringUtils.getSequenceMappedMessage("store.file.cannot.be.null"));
			return;
		}

		if (file.exists() && !file.canWrite()) {
			ExceptionGW.filterMessage(StringUtils.getSequenceMappedMessage(	"store.file.cannot.write",
																			file.getName()));
		}

		OutputStream out = null;
		try {
			out = new FileOutputStream(file);
			XMLSerializer.getSerializer().serialize(targetObject,
													out,
													nodeName,
													charset);
		}
		finally {
			ObjectFinalizer.close(out);
		}
	}

	/**
	 * @param elm
	 * @param name
	 * @param obj
	 * @throws XMLDigestFailException
	 */
	public static final Element appendNamedElement(	final Element elm,
													final String name,
													final Object obj) throws XMLDigestFailException {
		final Document doc = elm.getOwnerDocument();

		Element hash = null;
		try {
			hash = doc.createElement(name);
		}
		catch (Throwable thw) {
			thw.printStackTrace();
			return hash;
		}

		XMLDigestCodecFactory.getFactory().encode(	hash,
													obj);

		elm.appendChild(hash);
		return hash;
	}

	/**
	 * @param obj
	 * @return
	 */
	public static final Object copy(final Object obj) {
		ByteArrayOutputStream out = null;
		try {
			out = ResourceContext.getByteArrayOutputStream();
			XMLSerializer.getSerializer().serialize(obj,
													out,
													"copy",
													Constant.FILE_ENCODING);
			final String source = out.toString(Constant.FILE_ENCODING);

			return loadFromString(	source,
									Object.class);
		}
		catch (Throwable thw) {
			ExceptionGW.filterThrowable(ResourceContext.getBundledMessage(	"object.copy.error",
																			String.valueOf(obj)),
										thw);
		}
		finally {
			ObjectFinalizer.close(out);
		}

		return null;
	}
}
