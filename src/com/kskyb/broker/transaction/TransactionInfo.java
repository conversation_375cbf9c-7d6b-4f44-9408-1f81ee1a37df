package com.kskyb.broker.transaction;

import com.kskyb.broker.lang.AtomicReferenceAdapter;
import com.kskyb.broker.lang.CloseAdapter;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.util.ArrayMap;
import com.kskyb.broker.util.UniqueKey;

/**
 * <AUTHOR>
 * 
 */
public final class TransactionInfo
									extends
									CloseAdapter {
	/**
	 * 
	 */
	private static final boolean INNER_DEBUG = System.getProperty(	"verbos",
																	"false").equalsIgnoreCase("true");

	/**
	 * 
	 */
	protected final ArrayMap<String, Object> attr;

	/**
	 * 
	 */
	protected final ArrayMap<String, ITransactionControl> trControls;

	/**
	 * 
	 */
	protected final long transactionKey;

	/**
	 * 
	 */
	protected final Object owner;

	/**
	 * 
	 */
	final transient boolean root;

	/**
	 * 
	 */
	TransactionInfo(final boolean isRoot,
					final Object owner) {
		super(true);
		this.root = isRoot;
		this.owner = owner;
		this.attr = new ArrayMap();
		this.trControls = new ArrayMap<String, ITransactionControl>();
		this.transactionKey = UniqueKey.getInstance().generateKey();
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#setAttribute(java.lang.String, java.lang.Object)
	 */
	public final Object setAttribute(	final String key,
										final Object value) {
		final Object obj = this.attr.put(	key,
											value);
		if (INNER_DEBUG) {
			CommonLogGW.debug(ResourceContext.getBundledMessage("tr.attr.regist",
																String.valueOf(this.transactionKey),
																String.valueOf(this.attr.hashCode()),
																String.valueOf(key),
																String.valueOf(value),
																String.valueOf(value.hashCode()),
																String.valueOf(obj)));
		}
		return obj;
	}

	/**
	 * @param key
	 * @return
	 */
	public final Object getAttribute(final String key) {
		final Object obj = this.attr.get(key);
		if (INNER_DEBUG && obj == null) {
			CommonLogGW.debug(ResourceContext.getBundledMessage("tr.attr.missing",
																String.valueOf(this.transactionKey),
																String.valueOf(this.attr.hashCode()),
																String.valueOf(key)));
		}

		return obj;
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#removeAttribute(java.lang.String)
	 */
	public final Object removeAttribute(final String key) {
		final Object obj = this.attr.remove(key);
		if (INNER_DEBUG) {
			CommonLogGW.debug(ResourceContext.getBundledMessage("tr.attr.regist",
																String.valueOf(this.transactionKey),
																String.valueOf(this.attr.hashCode()),
																String.valueOf(key),
																obj == null	? "null"
																			: String.valueOf(obj.hashCode())));
		}
		return obj;
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#startTransaction()
	 */
	public void startTransaction() {
		if (TransactionEvent.isAvailable()) {
			TransactionEvent.startTransaction(this);
		}
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#registTransactionControl(com.kskyb.broker.transaction.ITransactionControl)
	 */
	public final void registTransactionControl(final ITransactionControl control) {
		final String name = control.getTransactionName();

		synchronized (this.trControls) {
			if (this.trControls.containsKey(name)) {
				ExceptionGW.filterMessage(	-1,
											ResourceContext.getBundledMessage(	"duplicate.regist.transaction.control",
																				name,
																				String.valueOf(control)));
				return;
			}
			CommonLogGW.info(ResourceContext.getBundledMessage(	this.root	? "regist.master.control"
																			: "regist.sub.control",
																name,
																String.valueOf(control),
																this.toString()));
			this.trControls.put(name,
								control);
		}
	}

	/**
	 * @param trName
	 * @return
	 */
	public final ITransactionControl getTransctionControl(final String trName) {
		synchronized (this.trControls) {
			return this.trControls.get(trName);
		}
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#removeTransctionControl(java.lang.String)
	 */
	public ITransactionControl removeTransctionControl(String trName) {
		synchronized (this.trControls) {
			return this.trControls.remove(trName);
		}
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#completeTransaction(boolean)
	 */
	public final void completeTransaction(final boolean success) {
		final boolean event = TransactionEvent.isAvailable();
		try {
			synchronized (this.trControls) {
				try {
					final ArrayMap<String, ITransactionControl> map = this.trControls;
					final int length = map.size();
					for (int i = 0; i < length; i++) {
						final ITransactionControl control = map.getValueByIndex(i);
						try {
							if (success) {
								control.commit();
							}
							else {
								control.rollback();
							}
							if (event) {

							}
							CommonLogGW.info(ResourceContext.getBundledMessage(	"complete.tr.object",
																				String.valueOf(control.getTransactionObject()),
																				success	? "commit"
																						: "rollback",
																				this.toString()));
						}
						catch (Throwable thw) {
							CommonLogGW.error(	ResourceContext.getBundledMessage(	"complete.tr.object.error",
																					String.valueOf(control.getTransactionObject()),
																					success	? "commit"
																							: "rollback",
																					this.toString()),
												thw);
						}
					}

					this.trControls.clear();
				}
				catch (Throwable thw) {
					CommonLogGW.error(	ResourceContext.getBundledMessage(	"complete.tr.object.process.error",
																			this.toString()),
										thw);
				}
			}

			synchronized (this.attr) {
				try {
					ObjectFinalizer.finalizeMap(this.attr);
				}
				catch (Throwable thw) {
					CommonLogGW.error(	ResourceContext.getBundledMessage(	"finalize.attribute.error",
																			this.toString()),
										thw);
				}
			}
		}
		finally {
			ObjectFinalizer.close(this);
		}
		CommonLogGW.info(ResourceContext.getBundledMessage(	this.root	? "complete.master.transaction"
																		: "complete.sub.transaction",
															this.toString()));
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#getTransactionKey()
	 */
	public final long getTransactionKey() {
		return this.transactionKey;
	}

	/**
	 * @see world.dragon.transaction.ITransactionInfo#getOwner()
	 */
	public final Object getOwner() {
		if (this.owner == null) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"must.initialize.transaction.owner",
																			this.getClass().getName()));
		}
		return this.owner;
	}

	/**
	 * @see com.kskyb.broker.lang.CloseAdapter#innerClose()
	 */
	@Override
	protected void innerClose() {
		//
	}

	/**
	 * 
	 */
	final AtomicReferenceAdapter<String> adapter = new AtomicReferenceAdapter<String>() {
		/**
		 * @see com.kskyb.broker.util.Descriptable.DescriptAdapter#make()
		 */
		@Override
		protected String make() {
			StringBuffer buffer = null;
			try {
				buffer = ResourceContext.getAppendable();
				buffer.append("tr:");
				buffer.append(String.valueOf(TransactionInfo.this.transactionKey));
				buffer.append(", owner:");
				buffer.append(String.valueOf(TransactionInfo.this.owner));
				return buffer.toString();
			}
			finally {
				ResourceContext.recycleAppendable(buffer);
			}
		}
	};

	/**
	 * @see java.lang.Object#toString()
	 */
	@Override
	public final String toString() {
		return this.adapter.getValue();
	}
}
