package com.kskyb.broker.transaction;

import static java.lang.management.ManagementFactory.THREAD_MXBEAN_NAME;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.ArrayList;
import java.util.List;
import java.util.jar.Attributes;

import javax.management.MBeanServer;

import com.kskyb.broker.jmx.JMXUtil;
import com.kskyb.broker.lang.Constant;
import com.kskyb.broker.lang.ErrorCode;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.lang.ThreadAttribute;
import com.kskyb.broker.lang.ThreadFactory;
import com.kskyb.broker.lang.ThreadFactoryEvent;
import com.kskyb.broker.lang.ThreadSelector;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.util.ArrayMap;
import com.kskyb.broker.util.SequenceMap;
import com.kskyb.broker.util.param.CommonDataSheet;


/**
 * <AUTHOR>
 * 
 */
public final class TransactionThreadFactory
											extends
											ThreadFactory {
	/**
	 * 
	 */
	protected static final ThreadInfo[] BLANK_THREAD_INFO = new ThreadInfo[0];

	/**
	 * 
	 */
	protected static final long[] BLANK_IDS = new long[0];

	/**
	 * @param attr
	 */
	public static final void load(final Attributes attr) {
		TransactionThreadFactory instance = new TransactionThreadFactory();
		ThreadFactory.setFactory(instance);
	}

	/**
	 * 
	 */
	final ArrayMap<String, LocalThreadGroup> groupMap = new ArrayMap<String, LocalThreadGroup>();

	/**
	 * 
	 */
	boolean modify = false;

	/**
	 * 
	 */
	String[] names = Constant.BLANK_STRING_ARRAY;

	/**
	 * 
	 */
	private TransactionThreadFactory() {
		this.createGroup(	DEFAULT_NORMAL_GROUP,
							false);
		this.createGroup(	DEFAULT_TRANSACTION_GROUP,
							true);
	}

	/**
	 * @see com.kskyb.broker.lang.ThreadFactory#createGroup(java.lang.String, boolean)
	 */
	@Override
	protected final void createGroup(	final String name,
										final boolean transaction) {
		synchronized (this.groupMap) {
			if (this.groupMap.containsKey(name)) {
				return;
			}

			this.groupMap.put(	name,
								new LocalThreadGroup(	name,
														transaction));
			this.modify = true;
		}
	}

	/**
	 * @see com.kskyb.broker.lang.ThreadFactory#destroyGroup(java.lang.String)
	 */
	@Override
	protected final void destroyGroup(final String name) {
		LocalThreadGroup group = null;
		synchronized (this.groupMap) {
			group = this.groupMap.remove(name);
			if (group == null) {
				ExceptionGW.filterMessage(	ErrorCode.CODE_COMMUNICATION_COMMON,
											ResourceContext.getBundledMessage(	"cannot.find.thread.group",
																				name));
				return;
			}
		}
		group.destroy();
	}

	/**
	 * @see com.kskyb.broker.lang.ThreadFactory#getGroupNames()
	 */
	@Override
	protected String[] getGroupNames() {
		synchronized (this.groupMap) {
			if (this.modify) {
				this.names = this.groupMap.getKeyList().toArray(Constant.BLANK_STRING_ARRAY);
				this.modify = false;
			}
		}
		return this.names;
	}

	/**
	 * @see com.kskyb.broker.lang.ThreadFactory#createThread(java.lang.String, java.lang.Runnable, java.lang.String)
	 */
	@Override
	protected final Thread createThread(final String groupName,
										final Runnable target,
										final String name) {
		final LocalThreadGroup group = this.groupMap.get(groupName);

		if (group == null) {
			ExceptionGW.filterMessage(	ErrorCode.CODE_COMMUNICATION_COMMON,
										ResourceContext.getBundledMessage(	"cannot.find.thread.group",
																			groupName));
			return null;
		}

		return group.createThread(	target,
									name);
	}

	/**
	 * @see com.kskyb.broker.lang.ThreadFactory#updateThreadName(java.lang.Thread, java.lang.String)
	 */
	@Override
	protected final void updateThreadName(	final Thread thread,
											final String name) {
		final String groupName = thread.getThreadGroup().getName();

		final LocalThreadGroup group = this.groupMap.get(groupName);

		if (group == null) {
			return;
		}

		group.updateThreadName(	thread,
								name);
	}

	/**
	 * @see com.kskyb.broker.lang.ThreadFactory#generateThreadInfo(java.lang.String)
	 */
	@Override
	protected final CommonDataSheet generateThreadInfo(final String groupName) {
		final LocalThreadGroup group = this.groupMap.get(groupName);

		if (group == null) {
			return null;
		}
		return group.generateThreadInfo();
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	private class LocalThreadGroup
									implements
									ThreadSelector {
		/**
		 * 
		 */
		final ThreadGroup group;

		/**
		 * 
		 */
		final boolean transaction;

		/**
		 * 
		 */
		final String prefix;

		/**
		 * @param name
		 */
		LocalThreadGroup(	final String name,
							final boolean flag) {
			this.group = new ThreadGroup(name);
			this.transaction = flag;
			this.prefix = name.concat(":");
		}

		/**
		 * @param target
		 * @param name
		 * @return
		 */
		synchronized final Thread createThread(	final Runnable target,
												final String name) {
			return this.transaction	? new TransactionThread(this.group,
															target,
															this.getThreadName(name))
									: new NormalThread(	this.group,
														target,
														this.getThreadName(name));
		}

		/**
		 * @param name
		 * @return
		 */
		final String getThreadName(final String name) {
			return this.prefix.concat(name);
		}

		/**
		 * @param name
		 * @return
		 */
		final boolean isThisGroupName(final String name) {
			return name.startsWith(this.prefix);
		}

		/**
		 * @see com.kskyb.broker.lang.ThreadSelector#accept(long, java.lang.String)
		 */
		public boolean accept(	final long threadId,
								final String threadName) {
			if (threadName == null) {
				return false;
			}
			return this.isThisGroupName(threadName);
		}

		/**
		 * @param thread
		 * @param name
		 */
		final void updateThreadName(final Thread thread,
									final String name) {
			thread.setName(this.getThreadName(name));
		}

		/**
		 * @return
		 */
		final CommonDataSheet generateThreadInfo() {
			return JMXUtil.filterThreadInfo(getLocalThreadInfoBySelector(this));
		}

		/**
		 * 
		 */
		final void destroy() {
			//
		}
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	private class TransactionThread
									extends
									Thread
											implements
											TransactionSupportThread {
		/**
		 * @param group
		 * @param target
		 * @param name
		 */
		TransactionThread(	final ThreadGroup group,
							final Runnable target,
							final String name) {
			super(	group,
					target,
					name);
		}

		/**
		 * @see java.lang.Thread#run()
		 */
		@Override
		public void run() {
			TransactionManager manager = TransactionManager.getManager();
			try {
				if (ThreadFactoryEvent.isAvailable()) {
					ThreadFactoryEvent.fireThreadStart(this);
				}
				manager.createTransaction();
				super.run();
			}
			catch (Throwable thw) {
				CommonLogGW.fatal(	ResourceContext.getBundledMessage("transaction.thread.invoke.error"),
									thw);
			}
			finally {
				if (ThreadFactoryEvent.isAvailable()) {
					ThreadFactoryEvent.fireThreadFinish(this);
				}
				manager.destroyTransaction(true);
			}
		}

	}

	/**
	 * <AUTHOR>
	 * 
	 */
	private class NormalThread
								extends
								Thread {
		/**
		 * @param group
		 * @param target
		 * @param name
		 */
		NormalThread(	ThreadGroup group,
						Runnable target,
						String name) {
			super(	group,
					target,
					name);
		}

		/**
		 * @see java.lang.Thread#run()
		 */
		@Override
		public void run() {
			try {
				if (ThreadFactoryEvent.isAvailable()) {
					ThreadFactoryEvent.fireThreadStart(this);
				}
				super.run();
			}
			catch (Throwable thw) {
				CommonLogGW.fatal(	ResourceContext.getBundledMessage("non.transaction.thread.invoke.error"),
									thw);
			}
			finally {
				if (ThreadFactoryEvent.isAvailable()) {
					ThreadFactoryEvent.fireThreadFinish(this);
				}
			}
		}
	}

	/**
	 * 
	 */
	public static final void managgeAttributeMap() {
		final boolean eventFlag = ThreadFactoryEvent.isAvailable();
		final ThreadInfo[] list = getLocalThreadInfoBySelector(null);

		List<Long> manageIds = null;
		List<Long> currentIds = null;
		List<Long> newIds = null;
		SequenceMap<String, Object> attrMap = null;
		try {
			manageIds = ResourceContext.getList();
			currentIds = ResourceContext.getList();
			newIds = ResourceContext.getList();
			attrMap = ResourceContext.getSequenceMap();

			ThreadAttribute.dumpManageIds(manageIds);
			for (final ThreadInfo thread : list) {
				if (thread == null) {
					continue;
				}
				final boolean terminate = thread.getThreadState() == Thread.State.TERMINATED;
				currentIds.add(thread.getThreadId());
				if (eventFlag) {
					if (terminate) {
						ThreadFactoryEvent.fireThreadTerminated(thread);
					}
					ThreadAttribute.dumpThreadAttribute(new Long(thread.getThreadId()),
														attrMap,
														false);
					ThreadFactoryEvent.fireThreadRunning(	thread.getThreadId(),
															attrMap);
					attrMap.clear();
				}
			}

			for (final Long id : currentIds) {
				if (manageIds.remove(id)) {
					while (manageIds.remove(id)) {
						//
					}
				}
				else if (eventFlag) {
					newIds.add(id);
				}
			}

			if (eventFlag) {
				for (final Long id : newIds) {
					ThreadFactoryEvent.fireThreadRegistered(id);
				}
			}

			for (final Long id : manageIds) {
				ThreadAttribute.dumpThreadAttribute(id,
													attrMap,
													true);
				if (eventFlag) {
					ThreadFactoryEvent.fireThreadRemoved(	id,
															attrMap);
				}
				ObjectFinalizer.finalizeMap(attrMap);
			}
		}
		finally {
			ResourceContext.recycleList(manageIds);
			ResourceContext.recycleList(currentIds);
			ResourceContext.recycleList(newIds);
		}
	}

	/**
	 * 
	 */
	private static final MBeanServer mbs = ManagementFactory.getPlatformMBeanServer();

	/**
	 * 
	 */
	private static ThreadMXBean tmbean = null;

	/**
	 * @return
	 */
	public static final ThreadMXBean getLocalThreadMXBean() {
		if (tmbean == null) {
			try {
				tmbean = ManagementFactory.newPlatformMXBeanProxy(	mbs,
																	THREAD_MXBEAN_NAME,
																	ThreadMXBean.class);
			}
			catch (Throwable thw) {
				thw.printStackTrace();
				return null;
			}
		}
		return tmbean;
	}

	/**
	 * @param ids
	 * @return
	 */
	public static final ThreadInfo[] getLocalThreadInfoByID(long[] ids) {
		final ThreadMXBean bean = getLocalThreadMXBean();

		return bean.getThreadInfo(ids);
	}

	/**
	 * @param selector
	 * @return
	 */
	public static final ThreadInfo[] getLocalThreadInfoBySelector(final ThreadSelector selector) {
		final ThreadMXBean bean = getLocalThreadMXBean();

		long[] ids = bean.getAllThreadIds();

		final ThreadInfo[] infos = bean.getThreadInfo(ids);

		if (selector == null) {
			return infos;
		}

		final int length = infos.length;

		final ArrayList<ThreadInfo> list = new ArrayList<ThreadInfo>();

		for (int i = 0; i < length; i++) {
			final ThreadInfo info = infos[i];
			if (info == null) {
				continue;
			}
			if (selector.accept(info.getThreadId(),
								info.getThreadName())) {
				list.add(info);
			}
		}

		return list.toArray(new ThreadInfo[0]);
	}
}
