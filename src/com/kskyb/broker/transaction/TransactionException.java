package com.kskyb.broker.transaction;

import com.kskyb.broker.util.Descripter;

/**
 * <AUTHOR>
 * 
 */
public abstract class TransactionException
											extends
											RuntimeException {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8729364283210750392L;

	/**
	 * 
	 */
	final transient Object owner;

	/**
	 * 
	 */
	final String ownerDesc;

	/**
	 * @param owner
	 * @param message
	 */
	protected TransactionException(	final Object owner,
									final String message) {
		super(message);
		this.owner = owner;
		this.ownerDesc = Descripter.makeDescription(owner);
	}

	/**
	 * @return the owner
	 */
	public final Object getOwner() {
		return this.owner;
	}

	/**
	 * @return the ownerDesc
	 */
	public final String getOwnerDesc() {
		return this.ownerDesc;
	}
}
