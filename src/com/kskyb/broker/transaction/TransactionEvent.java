package com.kskyb.broker.transaction;

import com.kskyb.broker.event.BroadcastEvent;
import com.kskyb.broker.event.BroadcastEventBroker;
import com.kskyb.broker.event.BroadcastEventListenerAdapter;
import com.kskyb.broker.util.ClassConstantsMap;

/**
 * <AUTHOR>
 * 
 */
public final class TransactionEvent
									extends
									BroadcastEvent {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8591011824641597399L;

	/**
	 * 
	 */
	public static final int TYPE_CREATED_ROOT_TRANSACTION = 0;

	/**
	 * 
	 */
	public static final int TYPE_CREATED_SUB_TRANSACTION = TYPE_CREATED_ROOT_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_STARTED_ROOT_TRANSACTION = TYPE_CREATED_SUB_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_STARTED_SUB_TRANSACTION = TYPE_STARTED_ROOT_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_COMMIT_ROOT_TRANSACTION = TYPE_STARTED_SUB_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_COMMIT_SUB_TRANSACTION = TYPE_COMMIT_ROOT_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_ROLLBACK_ROOT_TRANSACTION = TYPE_COMMIT_SUB_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_ROLLBACK_SUB_TRANSACTION = TYPE_ROLLBACK_ROOT_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_COMMIT_TRANSACTION_OBJECT = TYPE_ROLLBACK_SUB_TRANSACTION + 1;

	/**
	 * 
	 */
	public static final int TYPE_ROLLBACK_TRANSACTION_OBJECT = TYPE_COMMIT_TRANSACTION_OBJECT + 1;

	/**
	 * 
	 */
	public static final int TYPE_FOUND_ORPHAN_TRANSACTION = TYPE_ROLLBACK_TRANSACTION_OBJECT + 1;

	/**
	 * 
	 */
	public static final int TYPE_TRANSACTION_CRASHED = Integer.MAX_VALUE;

	/**
	 * 
	 */
	public static final ClassConstantsMap CONSTANT_MAP = new ClassConstantsMap(	TransactionEvent.class,
																				"TYPE_");

	/**
	 * 
	 */
	public static final String KEY_OWNER = "KEY_OWNER";

	/**
	 * 
	 */
	public static final String KEY_TRANSACTION_INSTANCE = "KEY_TRANSACTION_INSTANCE";

	/**
	 * 
	 */
	public static final String KEY_TRANSACTION_OBJECT = "KEY_TRANSACTION_OBJECT";

	/**
	 * 
	 */
	public static final String KEY_TRANSACTION_KEY = "KEY_TRANSACTION_KEY";

	/**
	 * 
	 */
	public static final String KEY_MESSAGE = "KEY_MESSAGE";

	/**
	 * @return
	 */
	public static final boolean isAvailable() {
		return BroadcastEventBroker.getInstance().isAcceptable(TransactionEvent.class);
	}

	/**
	 * @param caseValue
	 * @return
	 */
	public static final String getEventName(final int caseValue) {
		return CONSTANT_MAP.getConstantName(caseValue);
	}

	/**
	 * @param owner
	 * @param message
	 */
	public static final void transactionCrash(	final Object owner,
												final String message) {
		final TransactionEvent event = new TransactionEvent(TYPE_TRANSACTION_CRASHED,
															owner);
		event.setAttribute(	KEY_MESSAGE,
							message);

		BroadcastEventBroker.getInstance().broadcast(event);
	}

	/**
	 * @param owner
	 * @param info
	 */
	public static final void createTransaction(final TransactionInfo info) {
		fireTransactionInfo(info.root	? TYPE_CREATED_ROOT_TRANSACTION
										: TYPE_CREATED_SUB_TRANSACTION,
							info);
	}

	/**
	 * @param owner
	 * @param info
	 */
	public static final void startTransaction(final TransactionInfo info) {
		fireTransactionInfo(info.root	? TYPE_STARTED_ROOT_TRANSACTION
										: TYPE_STARTED_SUB_TRANSACTION,
							info);
	}

	/**
	 * @param info
	 * @param commit
	 */
	public static final void completeTransaction(	final TransactionInfo info,
													final boolean commit) {
		fireTransactionInfo(info.root	? commit ? TYPE_COMMIT_ROOT_TRANSACTION
												: TYPE_ROLLBACK_ROOT_TRANSACTION
										: commit ? TYPE_COMMIT_SUB_TRANSACTION
												: TYPE_ROLLBACK_SUB_TRANSACTION,
							info);
	}

	/**
	 * @param info
	 */
	public static final void foundOrphanTransaction(final TransactionInfo info) {
		fireTransactionInfo(TYPE_FOUND_ORPHAN_TRANSACTION,
							info);
	}

	/**
	 * @param info
	 * @param obj
	 * @param commit
	 */
	public static final void completeTransactionObject(	final TransactionInfo info,
														final Object obj,
														final boolean commit) {
		fireTransactionObject(	commit	? TYPE_COMMIT_TRANSACTION_OBJECT
										: TYPE_ROLLBACK_TRANSACTION_OBJECT,
								info,
								obj);
	}

	/**
	 * @param owner
	 * @param message
	 */
	private static final void fireTransactionInfo(	final int type,
													final TransactionInfo info) {
		final TransactionEvent event = new TransactionEvent(type,
															info.owner);
		event.setAttribute(	KEY_TRANSACTION_INSTANCE,
							info);
		BroadcastEventBroker.getInstance().broadcast(event);
	}

	/**
	 * @param type
	 * @param info
	 * @param obj
	 */
	private static final void fireTransactionObject(final int type,
													final TransactionInfo info,
													final Object obj) {
		final TransactionEvent event = new TransactionEvent(type,
															info.owner);
		event.setAttribute(	KEY_TRANSACTION_INSTANCE,
							info);
		event.setAttribute(	KEY_TRANSACTION_OBJECT,
							obj);
		BroadcastEventBroker.getInstance().broadcast(event);
	}

	/**
	 * @see com.kskyb.broker.event.BroadcastEvent#getEventName()
	 */
	@Override
	public String getEventName() {
		return getEventName(this.getType());
	}

	/**
	 * @param type
	 */
	private TransactionEvent(	final int type,
								final Object owner) {
		super(type);
		this.setAttribute(	KEY_OWNER,
							owner);
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public static abstract class Listener
											extends
											BroadcastEventListenerAdapter<TransactionEvent> {
		/**
		 * 
		 */
		protected Listener() {
			this(false);
		}

		/**
		 * @param auto
		 */
		public Listener(boolean auto) {
			super(	TransactionEvent.class,
					auto);
		}
	}
}
