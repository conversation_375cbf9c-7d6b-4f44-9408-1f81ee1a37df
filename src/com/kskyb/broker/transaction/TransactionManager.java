package com.kskyb.broker.transaction;

import java.util.ArrayList;

import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.lang.ThreadAttribute;
import com.kskyb.broker.util.ArrayMap;


/**
 * <AUTHOR>
 * 
 */
public final class TransactionManager {
	/**
	 * 
	 */
	private static final TransactionManager manager = new TransactionManager();

	/**
	 * 
	 */
	public static final short GLOVAL_LEVEL_TRANSACTION = 0;

	/**
	 * 
	 */
	public static final short LOCAL_LEVEL_TRANSACTION = 1;

	/**
	 * @return
	 */
	public static final TransactionManager getManager() {
		return manager;
	}

	// -- implements LocaleContext end -- //
	/**
	 * 
	 */
	protected final ArrayMap<Object, ArrayList<TransactionInfo>> hash = new ArrayMap<Object, ArrayList<TransactionInfo>>();

	/**
	 * 
	 */
	private TransactionManager() {
		super();
	}

	/**
	 * 
	 */
	private static final String KEY_TRANSACTION_OWNER = TransactionManager.class.getName().concat(".tr.owner");

	static {
		ThreadAttribute.regist(	KEY_TRANSACTION_OWNER,
								"transaction.manager.tr.owner.desc");
	}

	/**
	 * @return
	 */
	public final Object extractTransactionOwner() {
		final Object trOwner = ThreadAttribute.getAttribute(KEY_TRANSACTION_OWNER);

		return trOwner == null	? Thread.currentThread()
								: trOwner;
	}

	/**
	 * @param trOwner
	 */
	public final void registTransactionOwner(final Object trOwner) {
		ThreadAttribute.setAttribute(	KEY_TRANSACTION_OWNER,
										trOwner);
	}

	/**
	 * 
	 */
	public final void removeTransactionOwner() {
		ThreadAttribute.removeAttribute(KEY_TRANSACTION_OWNER);
	}

	// ------------------------------------------------------------------------------------------------ public service method
	/**
	 * 
	 */
	public void createTransaction() {
		this.createTransaction(this.extractTransactionOwner());
	}

	/**
	 * 
	 */
	public void createTransaction(final Object owner) {
		synchronized (this.hash) {
			if (this.hash.containsKey(owner)) {
				final String message = ResourceContext.getBundledMessage(	"already.regist.transaction",
																			owner.toString());
				if (TransactionEvent.isAvailable()) {
					TransactionEvent.transactionCrash(	owner,
														message);
				}
				throw new TransactionCrashException(owner,
													message);
			}

			final TransactionInfo trInfo = this.createTransactionInfo(	true,
																		owner);
			trInfo.startTransaction();

			final ArrayList<TransactionInfo> list = new ArrayList<TransactionInfo>();
			list.add(trInfo);
			this.hash.put(	owner,
							list);
		}
	}

	/**
	 * 
	 */
	public final void destroyTransaction(final boolean commit) {
		this.destroyTransaction(this.extractTransactionOwner(),
								commit);
	}

	/**
	 * 
	 */
	public final void destroyTransaction(	final Object owner,
											final boolean commit) {
		final boolean event = TransactionEvent.isAvailable();
		final ArrayList<TransactionInfo> list = this.hash.remove(owner);

		if (list == null) {
			final String message = ResourceContext.getBundledMessage(	"transaction.not.found",
																		owner.toString());
			if (event) {
				TransactionEvent.transactionCrash(	owner,
													message);
			}
			throw new TransactionCrashException(owner,
												message);
		}

		synchronized (list) {
			while (list.size() > 1) {
				final TransactionInfo info = list.remove(list.size() - 1);
				if (info.root && event) {
					TransactionEvent.transactionCrash(	owner,
														ResourceContext.getBundledMessage(	"sub.transaction.is.master.transaction",
																							owner.toString()));
				}
				if (event) {
					TransactionEvent.foundOrphanTransaction(info);
				}
				info.completeTransaction(false);
			}

			final TransactionInfo info = list.remove(0);

			if (!info.root && event) {
				TransactionEvent.transactionCrash(	owner,
													ResourceContext.getBundledMessage(	"root.transaction.is.not.a.master.transaction",
																						owner.toString()));
			}

			info.completeTransaction(commit);
		}
	}

	/**
	 * @return
	 */
	public final long startTransaction() {
		return this.startTransaction(this.extractTransactionOwner());
	}

	/**
	 * @param owner
	 * @return
	 */
	public final long startTransaction(final Object owner) {
		TransactionInfo info = this.getTransactionStruct(	owner,
															ACT_PUSH);
		info.startTransaction();
		return info.getTransactionKey();
	}

	/**
	 * @param trKey
	 * @param commit
	 */
	public void completeTransaction(final long trKey,
									final boolean commit) {
		this.completeTransaction(	this.extractTransactionOwner(),
									trKey,
									commit);
	}

	/**
	 * @param owner
	 * @param trKey
	 * @param commit
	 */
	public void completeTransaction(final Object owner,
									final long trKey,
									final boolean commit) {
		if (trKey < 0) {
			return;
		}
		final TransactionInfo info = this.getTransactionStruct(	owner,
																ACT_POP);

		if (info.getTransactionKey() != trKey) {
			final String message = ResourceContext.getBundledMessage(	"different.transaction.key",
																		owner.toString(),
																		String.valueOf(trKey),
																		String.valueOf(info.getTransactionKey()));
			if (TransactionEvent.isAvailable()) {
				TransactionEvent.transactionCrash(	owner,
													message);
			}
			throw new TransactionCrashException(owner,
												message);
		}

		info.completeTransaction(commit);
	}

	/**
	 * @param key
	 * @param value
	 */
	public final void registAttribute(	final String key,
										final Object value) {
		final TransactionInfo info = this.getTransactionStruct(ACT_GET);
		info.setAttribute(	key,
							value);
	}

	/**
	 * @param key
	 * @return
	 */
	public final Object removeAttribute(final String key) {
		final TransactionInfo info = this.getTransactionStruct(ACT_GET);
		return info.removeAttribute(key);
	}

	/**
	 * @param owner
	 * @param key
	 * @return
	 */
	public final Object getAttribute(final String key) {
		final TransactionInfo info = this.getTransactionStruct(ACT_GET);
		return info.getAttribute(key);
	}

	/**
	 * @param key
	 * @param value
	 */
	public final void registTransactionControl(final ITransactionControl control) {
		final TransactionInfo info = this.getTransactionStruct(ACT_GET);
		info.registTransactionControl(control);
	}

	/**
	 * @param key
	 * @return
	 */
	public final ITransactionControl removeTransactionControl(final String key) {
		final TransactionInfo info = this.getTransactionStruct(ACT_GET);
		return info.removeTransctionControl(key);
	}

	/**
	 * @param owner
	 * @param key
	 * @return
	 */
	public final ITransactionControl getTransactionControl(final String key) {
		final TransactionInfo info = this.getTransactionStruct(ACT_GET);
		return info.getTransctionControl(key);
	}

	/**
	 * @return
	 */
	public final boolean isInTransaction() {
		return this.isInTransaction(this.extractTransactionOwner());
	}

	/**
	 * @param owner
	 * @return
	 */
	public final boolean isInTransaction(final Object owner) {
		return this.hash.containsKey(owner);
	}

	/**
	 * @param remove
	 * @param error
	 * @return
	 */
	public final TransactionInfo getTransactionStruct() {
		return this.getTransactionStruct(ACT_GET);
	}

	// --------------------------------------------------------------------------------------- protected inner service

	/**
	 * 
	 */
	private static final int ACT_GET = 1;

	/**
	 * 
	 */
	private static final int ACT_PUSH = ACT_GET + 1;

	/**
	 * 
	 */
	private static final int ACT_POP = ACT_PUSH + 1;

	/**
	 * @param type
	 * @return
	 */
	private final TransactionInfo getTransactionStruct(final int type) {
		final Object owner = this.extractTransactionOwner();
		return this.getTransactionStruct(	owner,
											type);
	}

	/**
	 * 
	 * @param owner
	 * @param remove
	 * @param error
	 * @return
	 */
	private final TransactionInfo getTransactionStruct(	final Object owner,
														final int type) {
		final boolean event = TransactionEvent.isAvailable();
		final ArrayList<TransactionInfo> tran = this.hash.get(owner);

		if (tran == null) {
			final String message = ResourceContext.getBundledMessage(	"current.transaction.does.not.exist",
																		owner.toString());
			if (event) {
				TransactionEvent.transactionCrash(	owner,
													message);
			}
			throw new TransactionNotFoundException(	owner,
													message);
		}

		synchronized (tran) {
			if (tran.size() < 1) {
				final String message = ResourceContext.getBundledMessage(	"there.is.only.master.transaction.or.not",
																			owner.toString());
				if (event) {
					TransactionEvent.transactionCrash(	owner,
														message);
				}
				throw new TransactionCrashException(owner,
													message);
			}

			switch (type) {
				case ACT_GET: {
					return tran.get(tran.size() - 1);
				}

				case ACT_POP: {
					if (tran.size() == 1) {
						final String message = ResourceContext.getBundledMessage(	"cannot.popup.master.transaction",
																					owner.toString());
						if (event) {
							TransactionEvent.transactionCrash(	owner,
																message);
						}
						throw new TransactionCrashException(owner,
															message);
					}
					return tran.remove(tran.size() - 1);
				}

				case ACT_PUSH: {
					final TransactionInfo child = this.createTransactionInfo(	false,
																				owner);
					tran.add(child);
					return child;
				}

				default: {
					throw new RuntimeException("unknown act type");
				}
			}
		}

	}

	/**
	 * @param owner
	 */
	// private final void reorganizeTransaction(final Object owner) {
	// TransactionInfo info = this.popupSubTransaction(owner);
	//
	// // root transaction이면 종료
	// if (info == null) {
	// return;
	// }
	//
	// do {
	// CommonLogGW.info(ResourceContext.getBundledMessage( "reorganize.uncomplete.transaction.rollback",
	// info.toString()));
	// info.completeTransaction(false);
	// info = this.popupSubTransaction(owner);
	// } while (info != null);
	// }

	/**
	 * @return
	 */
	private final TransactionInfo createTransactionInfo(final boolean root,
														final Object owner) {
		final TransactionInfo info = new TransactionInfo(	root,
															owner);

		if (TransactionEvent.isAvailable()) {
			TransactionEvent.createTransaction(info);
		}

		return info;
	}
}
