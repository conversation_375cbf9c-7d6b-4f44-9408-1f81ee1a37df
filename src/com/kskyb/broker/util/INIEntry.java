package com.kskyb.broker.util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 
 */
public final class INIEntry {
	/**
	 * 
	 */
	final String uri;

	/**
	 * 
	 */
	final Map<String, String> map;

	/**
	 * 
	 */
	Object[] instance = null;

	/**
	 * @param uri
	 */
	protected INIEntry(	final String uri,
						final Map<String, String> global) {
		this.uri = uri;
		this.map = new HashMap<String, String>();
		this.map.putAll(global);
	}

	/**
	 * @return the uri
	 */
	public final String getUri() {
		return this.uri;
	}

	/**
	 * @return the map
	 */
	public final Map<String, String> getMap() {
		return this.map;
	}

	/**
	 * @return the instance
	 */
	public final Object[] getInstance() {
		return this.instance;
	}

	/**
	 * @param instance
	 *            the instance to set
	 */
	public final void setInstance(final Object[] instance) {
		this.instance = instance;
	}

	/**
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return this.uri;
	}

}
