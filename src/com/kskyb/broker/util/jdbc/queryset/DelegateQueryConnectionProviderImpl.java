package com.kskyb.broker.util.jdbc.queryset;

import java.io.IOException;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.SQLException;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.logging.CommonLogGW;


/**
 * <AUTHOR>
 * 
 */
public final class DelegateQueryConnectionProviderImpl
														implements
														DelegateQueryConnectionProvider {
	/**
	 * 
	 */
	public DelegateQueryConnectionProviderImpl() {
		super();
	}

	/**
	 * @param driver
	 * @param url
	 * @param user
	 * @param passwd
	 */
	public DelegateQueryConnectionProviderImpl(	Class clazz,
												String url,
												String user,
												String passwd) throws Exception {
		this.driverClass = clazz;
		this.driver = clazz.getName();
		this.url = url;
		this.user = user;
		this.passwd = passwd;
		this.validateDelegateQueryConnectionProviderImpl();
	}

	/**
	 * 
	 */
	Connection conn = null;

	/**
	 * @see com.kskyb.broker.util.jdbc.queryset.DelegateQueryConnectionProvider#getConnection(com.kskyb.broker.util.jdbc.queryset.DelegateQuery)
	 */
	public final Connection getConnection(final DelegateQuery model) throws SQLException {
		if (this.conn == null) {
			this.conn = this.connector == null	? DriverManager.getConnection(	this.url,
																				this.info)
												: this.connector.connect(	this.url,
																			this.info);
		}
		return this.conn;
	}

	/**
	 * @see com.kskyb.broker.util.jdbc.queryset.DelegateQueryConnectionProvider#releaseConnection(java.sql.Connection)
	 */
	public final void releaseConnection(final Connection connection) {
		//
	}

	/**
	 * @see java.io.Closeable#close()
	 */
	public void close() throws IOException {
		ObjectFinalizer.close(this.conn);
	}

	/**
	 * 
	 */
	private java.util.Properties info = new java.util.Properties();

	/**
	 * 
	 */
	Class driverClass = null;

	/**
	 * 
	 */
	Driver connector = null;

	/**
	 * @throws Exception
	 * 
	 */
	@ValidationMethod
	private final void validateDelegateQueryConnectionProviderImpl() throws Exception {
		if (this.driverClass == null) {
			this.driverClass = Class.forName(this.driver);
		}

		try {
			if (Driver.class.isAssignableFrom(this.driverClass)) {
				this.connector = (Driver) this.driverClass.newInstance();
			}
		}
		catch (Throwable thw) {
			CommonLogGW.error(	ResourceContext.getBundledMessage(	"jdbc.driver.load.error",
																	this.driver),
								thw);
		}

		if (this.user != null) {
			this.info.put(	"user",
							this.user);
		}
		if (this.passwd != null) {
			this.info.put(	"password",
							this.passwd);
		}

		this.getConnection(null);
	}

	/**
	 * 
	 */
	@MemberField(elementName = "driver")
	private String driver = null;

	/**
	 * 
	 */
	@MemberField(elementName = "url")
	private String url = null;

	/**
	 * 
	 */
	@MemberField(elementName = "user")
	private String user = null;

	/**
	 * 
	 */
	@MemberField(elementName = "passwd")
	private String passwd = null;
}
