package com.kskyb.broker.util.model;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * 
 */
public abstract class ProxyModelHandler {
	/**
	 * 
	 */
	Class type = null;

	/**
	 * @return the type
	 */
	final Class getType() {
		return this.type;
	}

	/**
	 * @param type
	 *            the type to set
	 */
	final void setType(Class type) {
		this.type = type;
	}

	/**
	 * @param method
	 * @return
	 */
	public abstract MethodFilter make(final Method method);
}
