package com.kskyb.broker.util;

import java.io.IOException;
import java.util.Map;

import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.logging.CommonLogGW;


/**
 * 다른 ISelector 인스턴스를 중계하는 Selector
 * 
 * <AUTHOR>
 * 
 */
public final class DelegateSelector
									extends
									AbstractSelector {
	/**
	 * @param selector
	 * @return
	 */
	public static final DelegateSelector getInstance(	final ISelector selector,
														final boolean filter) {
		DelegateSelector delegator = new DelegateSelector();
		delegator.assign(	selector,
							filter);
		return delegator;
	}

	/**
	 * 
	 */
	ISelector selector = null;

	/**
	 * 
	 */
	Object drillDownObj = null;

	/**
	 * 
	 */
	public DelegateSelector() {
		super(true);
	}

	/**
	 * @param _selector
	 */
	public void assign(	final ISelector _selector,
						final boolean filter) {
		if (filter && _selector.size() == 1 && _selector.getParameterLength() == 1) {
			try {
				_selector.open();
				_selector.next();
				final Object tmp = _selector.removeObject(_selector.getParameterList()[0]);
				if (tmp instanceof ISelector) {
					this.drillDownObj = tmp;
					this.selector = SelectorWrapper.wrap(this.drillDownObj);
					return;
				}
			}
			catch (Exception thw) {
				ObjectFinalizer._finalize(this.drillDownObj);
				this.drillDownObj = null;
				CommonLogGW.warn(	"selector.drill.down.error",
									thw);
			}
			finally {
				_selector.endRead();
			}
		}

		if (_selector == null) {
			System.out.println("~");
		}

		this.selector = _selector;
	}

	// -- ISelector delegate methods -- //
	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#containKey(java.lang.Object)
	 */
	public boolean containKey(String key) {
		return this.selector.containKey(key);
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#getObject(java.lang.Object,java.lang.Object)
	 */
	public Object getObject(String key,
							Object defaultValue) {
		return this.selector.getObject(	key,
										defaultValue);
	}

	/**
	 * @see com.kskyb.broker.util.AbstractSelector#removeObjectDel(java.lang.String)
	 */
	@Override
	protected final Object removeObjectDel(String key) {
		return this.selector.removeObject(key);
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#getParameterLength()
	 */
	public int getParameterLength() {
		return this.selector.getParameterLength();
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#getParameterList()
	 */
	public String[] getParameterList() {
		return this.selector.getParameterList();
	}

	/**
	 * @see com.kskyb.broker.util.ISelector#hasNext()
	 */
	public boolean hasNext() throws IOException {
		return this.selector.hasNext();
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#next(java.util.Map)
	 */
	public boolean next(Map map) throws IOException {
		return this.selector.next(map);
	}

	/**
	 * @see com.kskyb.broker.util.ISelector#getCurrentAsMap()
	 */
	public SequenceMap<String, Object> getCurrentAsMap() {
		return this.selector.getCurrentAsMap();
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#open()
	 */
	public void open() throws IOException {
		this.selector.open();
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#rewind()
	 */
	public void rewind() throws IOException {
		this.selector.rewind();
	}

	/**
	 * @see com.kskyb.broker.util.AbstractSelector#setObjectDel(java.lang.String, java.lang.Object, boolean)
	 */
	@Override
	protected void setObjectDel(final String key,
								final Object value,
								final boolean force) {
		this.selector.setObject(key,
								value,
								force);
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#size()
	 */
	public int size() {
		return this.selector == null ? 0
									: this.selector.size();
	}

	/**
	 * 
	 * @see com.kskyb.broker.util.ISelector#endRead()
	 */
	public void endRead() {
		if (this.selector != null) {
			this.selector.endRead();
		}
	}

	/**
	 * @see com.kskyb.broker.lang.CloseAdapter#innerClose()
	 */
	@Override
	protected void innerClose() {
		if (this.selector == null) {
			return;
		}
		this.selector.endRead();

		if (this.drillDownObj != null) {
			ObjectFinalizer.close(this.selector);
		}

		this.selector = null;

		ObjectFinalizer._finalize(this.drillDownObj);
	}
}
