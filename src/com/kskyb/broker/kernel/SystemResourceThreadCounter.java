package com.kskyb.broker.kernel;

import java.util.HashMap;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.logging.CommonLogGW;


/**
 * <AUTHOR>
 * 
 */
public final class SystemResourceThreadCounter {
	/**
	 * 
	 */
	private final HashMap<Thread, Long> map = new HashMap<Thread, Long>();

	/**
	 * 
	 */
	public SystemResourceThreadCounter() {
		super();
	}

	/**
	 * @param msg
	 * @param limit
	 * @param delay
	 */
	public SystemResourceThreadCounter(	final String msg,
										final int limit,
										final long delay) {
		this();
		this.message = msg;
		this.limit = limit;
		this.delay = delay;
	}

	/**
	 * @param desc
	 */
	public final void up() {
		final long time = System.currentTimeMillis() + this.delay;
		final Thread thread = Thread.currentThread();
		do {
			synchronized (this.map) {
				if (this.map.containsKey(thread)) {
					throw new RuntimeException("cannot assign again");
				}

				if (this.map.size() < this.limit) {
					this.map.put(	thread,
									thread.getId());
					return;
				}

				try {
					this.map.wait(this.delay);
				}
				catch (InterruptedException e) {
					// e.printStackTrace();
				}
			}
		} while (time > System.currentTimeMillis());

		throw new SystemResourceExhaustedException(this.message);
	}

	/**
	 * 
	 */
	public final void down() {
		synchronized (this.map) {
			try {
				final Thread thread = Thread.currentThread();
				final Long id = this.map.remove(thread);
				if (id != null) {
					if (id.longValue() != thread.getId()) {
						CommonLogGW.warn(ResourceContext.getBundledMessage(	"counter.thread.id.is.different",
																			String.valueOf(id),
																			String.valueOf(thread.getId())));
					}
					return;
				}
				CommonLogGW.warn(ResourceContext.getBundledMessage("perhapse.up.error.thread.acccess"));
			}
			catch (Throwable thw) {
				CommonLogGW.error(	ResourceContext.getBundledMessage("resource.count.down.error"),
									thw);
			}
			finally {
				this.map.notifyAll();
			}
		}
	}

	/**
	 * 
	 */
	@MemberField(nullable = false)
	private String message = null;

	/**
	 * 
	 */
	@MemberField(nullable = false)
	private int limit = -1;

	/**
	 * 
	 */
	@MemberField(nullable = false)
	private long delay = -1;
}
