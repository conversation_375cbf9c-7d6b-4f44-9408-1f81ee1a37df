package com.kskyb.broker.annotation;

/**
 * 모델 필드 타입을 지정한다.
 * 
 * <AUTHOR>
 * 
 */
public interface IFieldType {
	/**
	 * 배열
	 */
	public static final short ARRAY = 0;

	/**
	 * 맵
	 */
	public static final short HASH = 1;

	/**
	 * 오브젝트
	 */
	public static final short OBJECT = 3;

	/**
	 * 스트링
	 */
	public static final short CDATA = 5;

	/**
	 * 컬럼 타입에 종속됨
	 */
	public static final short DEPENDS = 6;

	/**
	 * 속성값
	 */
	public static final short ATTRIBUTE = 7;
}
