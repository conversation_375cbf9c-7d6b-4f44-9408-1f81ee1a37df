package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * 
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface ExportServiceArgument {
	/**
	 * @return
	 */
	String name() default "";

	/**
	 * @return
	 */
	String desc();

	/**
	 * @return
	 */
	String defaultValue() default "";
}
