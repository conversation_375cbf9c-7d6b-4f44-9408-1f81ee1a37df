package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 포함된 부모 오브젝트 지정
 * 
 * <AUTHOR>
 * 
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ParentAssign {
	/**
	 * 부모 클래스 타입
	 * 
	 * @return
	 */
	short type() default IFieldType.OBJECT;
}
