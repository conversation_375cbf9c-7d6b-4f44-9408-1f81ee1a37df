package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * XML을 통해서 initialize 될경우에만 실행되도록 하는 노테이션 ( copy 시에 실행되지 않도록 함 )
 * 
 * <AUTHOR>
 * 
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface StaticValidationMethod {
	// do nothing
}
