package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 자바 프로시져에서 실행 대상 메소드를 지정
 * 
 * <AUTHOR>
 * @deprecated
 * 
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface ReflectExecuteMethod {
	/**
	 * @return
	 */
	String index() default "";
}
