package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 모델 멤버 필드 지정
 * 
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface MemberField {
	/**
	 * 모델 타입
	 * 
	 * @return
	 */
	short type() default IFieldType.DEPENDS;

	/**
	 * 대상 클래스 타입 지정
	 * 
	 * @return
	 */
	Class<?> clazz() default Void.class;

	/**
	 * 맵타입에서 키 클래스
	 * 
	 * @return
	 */
	Class<?> keyClazz() default String.class;

	/**
	 * 대상 엘리멘트 이름
	 * 
	 * @return
	 */
	String elementName() default "";

	/**
	 * 값이 없어도 되는지 여부
	 * 
	 * @return
	 */
	boolean nullable() default true;

	/**
	 * 코멘트
	 * 
	 * @return
	 */
	String comment() default "";

	/**
	 * 오브젝트 카피시에는 제외시킬지 여부
	 * 
	 * @return true : skip when copy object
	 */
	boolean skipOnCopy() default false;

	/**
	 * 오브젝트 카피할때 복제하지 않고 원본을 카피함
	 * 
	 * @return true : clone target field when copy object
	 */
	boolean singletone() default false;
}
