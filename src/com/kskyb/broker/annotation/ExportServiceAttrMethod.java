package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * 
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface ExportServiceAttrMethod {
	/**
	 * @return
	 */
	String name();

	/**
	 * @return
	 */
	String desc();

}
