package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * 
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExportServiceAttribute {
	/**
	 * @return
	 */
	boolean readOnly() default false;

	/**
	 * @return
	 */
	String name();

	/**
	 * @return
	 */
	String desc();
}
