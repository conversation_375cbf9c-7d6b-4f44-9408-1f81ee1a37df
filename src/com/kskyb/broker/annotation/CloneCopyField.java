package com.kskyb.broker.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 모델을 카피할때 대사이 되는 필드를 지정한다.
 * 
 * <AUTHOR>
 * 
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface CloneCopyField {
	/**
	 * 
	 * @return true : clone target field when copy object
	 */
	boolean singletone() default false;

	/**
	 * @return
	 */
	short type() default IFieldType.OBJECT;

	/**
	 * @return
	 */
	Class<?> clazz() default Void.class;

	/**
	 * @return
	 */
	Class<?> keyClazz() default String.class;

}
