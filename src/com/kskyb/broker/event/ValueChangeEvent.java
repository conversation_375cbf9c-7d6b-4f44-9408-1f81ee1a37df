package com.kskyb.broker.event;

import com.kskyb.broker.util.ClassConstantsMap;

/**
 * 특정 인스턴스에서 attribute가 변경되는 이벤트
 * 
 * <AUTHOR>
 * 
 */
public final class ValueChangeEvent
									extends
									BroadcastEvent {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4615885133421292449L;

	/**
	 * 
	 */
	private static final Class clazz = ValueChangeEvent.class;

	/**
	 * 
	 */
	public static final int TYPE_ATTR_CHANGE = 1;

	/**
	 * 
	 */
	public static final int TYPE_ATTR_ADD = 2;

	/**
	 * 
	 */
	public static final int TYPE_ATTR_REMOVE = 3;

	/**
	 * 
	 */
	public static final int TYPE_ATTR_REORG = 4;

	/**
	 * 
	 */
	public static final ClassConstantsMap CONSTANT_MAP = new ClassConstantsMap(	ValueChangeEvent.class,
																				"TYPE_");

	/**
	 * @param caseValue
	 * @return
	 */
	public static final String getEventName(final int caseValue) {
		return CONSTANT_MAP.getConstantName(caseValue);
	}

	/**
	 * @see com.kskyb.broker.event.BroadcastEvent#getEventName()
	 */
	@Override
	public String getEventName() {
		return getEventName(this.getType());
	}

	/**
	 * 
	 */
	final String name;

	/**
	 * 
	 */
	final Object target;

	/**
	 * 
	 */
	final Object value;

	/**
	 * @param type
	 * @param target
	 * @param value
	 */
	public ValueChangeEvent(final int type,
							final String name,
							final Object target,
							final Object value) {
		super(type);
		this.name = name;
		this.target = target;
		this.value = value;
	}

	/**
	 * @return the name
	 */
	public final String getName() {
		return this.name;
	}

	/**
	 * @return the target
	 */
	public final Object getTarget() {
		return this.target;
	}

	/**
	 * @return the value
	 */
	public final Object getValue() {
		return this.value;
	}

	/**
	 * @see com.kskyb.broker.event.BroadcastEvent#getEventClassType()
	 */
	@Override
	protected Class<? extends BroadcastEvent> getEventClassType() {
		return clazz;
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public static abstract class Listener
											extends
											BroadcastEventListenerAdapter<ValueChangeEvent> {
		/**
		 * 
		 */
		protected Listener() {
			this(false);
		}

		/**
		 * @param auto
		 */
		protected Listener(boolean auto) {
			super(	ValueChangeEvent.class,
					auto);
		}
	}

}
