package com.kskyb.broker.middleware.connector;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Map;

import com.kskyb.broker.io.SerializeCodecs;
import com.kskyb.broker.io.StreamSerializable;
import com.kskyb.broker.lang.CloseAdapter;
import com.kskyb.broker.lang.Constant;
import com.kskyb.broker.lang.ErrorCode;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.util.ArrayMap;
import com.kskyb.broker.util.Descriptable;
import com.kskyb.broker.util.Descripter;
import com.kskyb.broker.util.SequenceMap;


/**
 * 미들웨어에서 외부로부터 데이터를 전송받아 처리하고 결과를 반환하는 과정에서 사용되는 메인이 되는 데이터구조
 * 
 * <AUTHOR>
 * 
 */
public final class CommandData
								extends
								CloseAdapter
											implements
											StreamSerializable,
											Descriptable {
	/**
	 * 
	 */
	public static final String PROCESS_CONTEXT_INDEX = "PROCESS_CONTEXT";

	/**
	 * 
	 */
	public static final String PROCESS_REQUEST_SOURCE_INDEX = "REQUEST_SOURCE";

	/**
	 * 
	 */
	public static final String PROCESS_REQUEST_DESTINATION_INDEX = "REQUEST_DESTINATION";

	/**
	 * @return
	 */
	public static final CommandData getInstance() {
		final CommandData data = new CommandData();
		// data.initiate();
		return data;
	}

	/**
	 * 메세지 요청
	 */
	public static final byte MESSAGE_TYPE_REQUEST = 0;

	/**
	 * 메세지 요청 실패
	 */
	public static final byte MESSAGE_TYPE_REQUEST_FAIL = 1;

	/**
	 * 메세지 결과
	 */
	public static final byte MESSAGE_TYPE_RESPONSE = -1;

	/**
	 * 메세지 결과
	 */
	public static final byte MESSAGE_TYPE_RESPONSE_C_CHARP = 10;

	/**
	 * 최초 연결
	 * 
	 * @deprecated
	 */
	@Deprecated
	public static final byte MESSAGE_TYPE_HANDSHAKE = -2;

	/**
	 * 연결 종료 요청
	 */
	public static final byte MESSAGE_TYPE_GOODBYE = -3;

	/**
	 * 연결 종료 요청
	 */
	public static final byte MESSAGE_TYPE_GOODBYE_C_CHARP = 99;

	/**
	 * async 메세지 전송
	 * 
	 * @deprecated
	 */
	@Deprecated
	public static final byte MESSAGE_TYPE_BROADCAST_REQUEST = -4;

	/**
	 * async 메세지 수신
	 * 
	 * @deprecated
	 */
	@Deprecated
	public static final byte MESSAGE_TYPE_BROADCAST_RESPONSE = -5;

	/**
	 * 트랜잭션 commit
	 */
	public static final byte MESSAGE_TYPE_COMMIT = -6;

	/**
	 * 트랜잭션 rollback
	 */
	public static final byte MESSAGE_TYPE_ROLLBACK = -7;

	/**
	 * 연결확인 ping/ pong
	 */
	public static final byte MESSAGE_TYPE_PINGPONG = -8;

	/**
	 * 트랜잭션 시작
	 */
	public static final byte MESSAGE_TYPE_BEGIN_TRAN = -9;

	/**
	 * 
	 */
	private ArrayMap<String, Object> source = null;

	/**
	 * 
	 */
	private ArrayMap<String, Object> result = null;

	/**
	 * 
	 */
	private ArrayMap<String, Object> temp = null;

	/**
	 * 
	 */
	long transactionId = 0;

	/**
	 * 
	 */
	private boolean modify = false;

	/**
	 * 
	 */
	private String desc = null;

	/**
	 * 
	 */
	private final ArrayMap.Listener<String, Object> changeListener = new ArrayMap.Listener<String, Object>() {
		/**
		 * @see com.kskyb.broker.util.ArrayMap.Listener#changeEntries()
		 */
		@Override
		protected void changeEntries() {
			CommandData.this.modify = true;
		}

	};

	/**
	 * 
	 */
	protected CommandData() {
		super(true);
		this.transactionId = getTransactionId();
	}

	/**
	 * @see com.kskyb.broker.io.StreamSerializable#getTargetClassName()
	 */
	@Override
	public String getTargetClassName() {
		return null;
	}

	/**
	 * @see com.kskyb.broker.lang.CloseAdapter#innerClose()
	 */
	@Override
	protected void innerClose() {
		// this.source.finalizeEntry();
		ObjectFinalizer.finalizeMap(this.source);
		this.source = null;

		// this.result.finalizeEntry();
		ObjectFinalizer.finalizeMap(this.result);
		this.result = null;

		// this.temp.finalizeEntry();
		ObjectFinalizer.finalizeMap(this.temp);
		this.temp = null;
	}

	/**
	 * 
	 */
	String processId = null;

	/**
	 * @return
	 */
	public final String getProcessId() {
		return this.processId;
	}

	/**
	 * @param processId
	 */
	public final void setProcessId(String processId) {
		this.processId = processId;
	}

	/**
	 * 
	 */
	byte messageType = MESSAGE_TYPE_REQUEST;

	/**
	 * @return
	 */
	public final byte getMessageType() {
		return this.messageType;
	}

	/**
	 * @param messageType
	 */
	public final void setMessageType(byte messageType) {
		this.messageType = messageType;
	}

	/**
	 * 
	 */
	Throwable errStack = null;

	/**
	 * @param thw
	 */
	public void setError(Throwable thw) {
		this.errStack = thw;
	}

	/**
	 * @return
	 */
	public boolean isError() {
		return this.errStack != null;
	}

	/**
	 * @return
	 */
	public boolean isSuccess() {
		return this.errStack == null;
	}

	/**
	 * @return
	 */
	public Throwable getError() {
		return this.errStack;
	}

	/**
	 * @return
	 */
	public String getErrorMessage() {
		return MessageFilter.getFilter().filter(this.errStack);
	}

	// -- SOURCE MAP ENTRY -- //
	/**
	 * @param index
	 * @return
	 */
	public synchronized Map createSourceMap(String index) {
		if (this.source == null) {
			this.source = new ArrayMap(this.changeListener);
		}

		if (this.source.containsKey(index)) {
			return (Map) this.source.get(index);
		}
		Map map = new ArrayMap();

		this.source.put(index,
						map);

		return map;
	}

	/**
	 * @return
	 */
	public synchronized final SequenceMap<String, Object> getSourceMap() {
		if (this.source == null) {
			this.source = new ArrayMap(this.changeListener);
		}
		return this.source;
	}

	/**
	 * @return
	 */
	public synchronized final SequenceMap<String, Object> extractSourceMap() {
		ArrayMap<String, Object> value = this.source;
		this.source = null;
		value.setListener(null);
		return value;
	}

	/**
	 * @param key
	 * @return
	 */
	public synchronized final Object extractSource(final String key) {
		return this.source == null	? null
									: this.source.remove(key);
	}

	/**
	 * @param key
	 * @return
	 */
	public synchronized final Object getSource(final String key) {
		return this.source == null	? null
									: this.source.get(key);
	}

	/**
	 * @param key
	 * @param value
	 */
	public synchronized final void setSource(	final String key,
												final Object value) {
		Object obj = this.getSourceMap().put(	key,
												value == null	? Constant.BLANK_STRING
																: value);
		ObjectFinalizer._finalize(obj);
	}

	// -- RESULT MAP ENTRY -- //
	/**
	 * @return
	 */
	public synchronized final SequenceMap<String, Object> getResultMap() {
		if (this.result == null) {
			this.result = new ArrayMap(this.changeListener);
		}
		return this.result;
	}

	/**
	 * @return
	 */
	public synchronized final SequenceMap<String, Object> extractResultMap() {
		if (this.result == null) {
			return new ArrayMap();
		}
		this.result.setListener(null);
		SequenceMap<String, Object> value = this.result;
		this.result = null;
		return value;
	}

	/**
	 * @param map
	 */
	public synchronized final void switchResultMap(final CommandData source) {
		if (this.result != null) {
			this.result.setListener(null);
			ObjectFinalizer.finalizeMap(this.result);
			this.result = null;
		}

		this.result = source.result;
		source.result = null;
	}

	/**
	 * @param key
	 * @return
	 */
	public synchronized final Object extractResult(final Object key) {
		return this.result == null	? null
									: this.result.remove(key);
	}

	/**
	 * @param key
	 * @return
	 */
	public synchronized final Object getResult(final Object key) {
		return this.result == null	? null
									: this.result.get(key);
	}

	/**
	 * @param key
	 * @param value
	 */
	public synchronized final void setResult(	final String key,
												final Object value) {
		Object obj = this.getResultMap().put(	key,
												value == null	? Constant.BLANK_STRING
																: value);

		ObjectFinalizer._finalize(obj);
	}

	// -- TEMP MAP ENTRY -- //
	/**
	 * @return
	 */
	public synchronized final SequenceMap<String, Object> getTempMap() {
		if (this.temp == null) {
			this.temp = new ArrayMap(this.changeListener);
		}
		return this.temp;
	}

	/**
	 * @param key
	 * @return
	 */
	public synchronized final Object getTemp(final Object key) {
		return this.temp == null ? null
								: this.temp.get(key);
	}

	/**
	 * @param key
	 * @param value
	 */
	public synchronized final void setTemp(	final String key,
											final Object value) {
		Object obj = this.getTempMap().put(	key,
											value == null	? Constant.BLANK_STRING
															: value);
		ObjectFinalizer._finalize(obj);
	}

	/**
	 * @param key
	 * @param value
	 */
	public synchronized final void setAttribute(final String key,
												final Object value) {
		this.getTempMap().put(	key,
								value);
	}

	/**
	 * @param key
	 */
	public synchronized final void removeAttribute(final String key) {
		this.getTempMap().remove(key);
	}

	/**
	 * @param <T>
	 * @param key
	 * @param clazz
	 * @return
	 */
	public synchronized final <T> T getAttribute(	final String key,
													final Class<T> clazz) {
		Object obj = this.getTempMap().get(key);

		if (obj == null) {
			return null;
		}

		if (clazz.isInstance(obj)) {
			return (T) obj;
		}

		ExceptionGW.filterMessage(	ErrorCode.CODE_DATA_CONSTRAINT_COMMON,
									ResourceContext.getBundledMessage(	"attribute.class.is.different",
																		clazz.getName(),
																		obj.getClass().getName()));
		return null;
	}

	/**
	 * 
	 */
	public final void clear() {
		this.transactionId = getTransactionId();
		this.processId = null;
		ObjectFinalizer.finalizeMap(this.source);
		ObjectFinalizer.finalizeMap(this.result);
		ObjectFinalizer.finalizeMap(this.temp);
	}

	/**
	 * @param transactionId
	 */
	public void setTransactionId(long transactionId) {
		this.transactionId = transactionId;
	}

	/**
	 * @param name
	 */
	public final void setProcessContextName(String name) {
		this.setSource(	PROCESS_CONTEXT_INDEX,
						name);
	}

	/**
	 * @return
	 */
	public final String getProcessContextName() {
		Object obj = this.getSource(PROCESS_CONTEXT_INDEX);

		return obj == null	? null
							: obj.toString();
	}

	/**
	 * @see com.kskyb.broker.io.StreamSerializable#deserialize(java.io.InputStream)
	 */
	@Override
	public final void deserialize(final InputStream in) throws IOException {
		// -- transaction id -- //
		long tid = SerializeCodecs.decodeLong(in);

		// -- process id -- //
		String readPid = SerializeCodecs.decodeString(in);

		// -- message type -- //
		byte type = SerializeCodecs.decodeByte(in);

		this.setProcessId(readPid);
		this.setMessageType(type);
		this.setTransactionId(tid);

		switch (type) {
			case MESSAGE_TYPE_REQUEST: {
				SerializeCodecs.decodeMap(	in,
											this.getSourceMap());
				break;
			}
			case MESSAGE_TYPE_RESPONSE:
			case MESSAGE_TYPE_RESPONSE_C_CHARP: {
				SerializeCodecs.decodeMap(	in,
											this.getResultMap());

				Boolean bool = SerializeCodecs.decodeBoolean(in);
				String msg = SerializeCodecs.decodeString(in);
				if (bool) {
					this.setError(new ServerResponseError(msg));
				}

				break;
			}
			case MESSAGE_TYPE_COMMIT:
			case MESSAGE_TYPE_ROLLBACK:
			case MESSAGE_TYPE_GOODBYE:
			case MESSAGE_TYPE_GOODBYE_C_CHARP:
			case MESSAGE_TYPE_PINGPONG: {
				break;
			}
			default: {
				Throwable thw = new RuntimeException("data decode fail (unknown message type):" + String.valueOf(type));
				this.setError(thw);
				break;
			}
		}

	}

	/**
	 * @see com.kskyb.broker.io.StreamSerializable#serialize(java.io.OutputStream)
	 */
	@Override
	public final void serialize(final OutputStream out) throws IOException {
		// -- transaction id -- //
		SerializeCodecs.encodeLong(	this.transactionId,
									out);

		// -- process id -- //
		SerializeCodecs.encodeString(	this.processId,
										out);

		// -- message type -- //
		SerializeCodecs.encodeByte(	this.getMessageType(),
									out);

		switch (this.getMessageType()) {
			case MESSAGE_TYPE_REQUEST: {
				// if (this.source == null) {
				// this.source = new HashMap<Object, Object>();
				// }
				SerializeCodecs.encodeMap(	this.getSourceMap(),
											out);
				break;
			}

			case MESSAGE_TYPE_RESPONSE: {
				SerializeCodecs.encodeMap(	this.getResultMap(),
											out);
				SerializeCodecs.encodeBoolean(	this.isError(),
												out);
				final String message = MessageFilter.getFilter().filter(this.errStack);
				SerializeCodecs.encodeString(	message,
												out);

				break;
			}

			case MESSAGE_TYPE_GOODBYE: {
				break;
			}

			default: {
				throw new RuntimeException("unsupport message type:" + String.valueOf(this.getMessageType()));
			}
		}

	}

	/**
	 * 
	 */
	static long DEFAULT_TRANSACTION = 0;

	/**
	 * @return
	 */
	private static final long getTransactionId() {
		synchronized (CommandData.class) {
			return ++DEFAULT_TRANSACTION;
		}
	}

	/**
	 * @see com.kskyb.broker.util.Descriptable#descript(java.lang.StringBuffer, int)
	 */
	@Override
	public final void descript(	StringBuffer buffer,
								int depth) {
		if (depth > 0) {
			buffer.append(Constant.NEW_LINE_CHARS);
		}
		Descripter.makeIndent(	buffer,
								depth);
		buffer.append("Data Description START [");
		buffer.append(String.valueOf(this.getProcessId()));
		buffer.append('@');
		buffer.append(String.valueOf(this.getProcessContextName()));
		buffer.append(']');

		buffer.append(Constant.NEW_LINE_CHARS);
		Descripter.makeIndent(	buffer,
								depth);
		buffer.append("INPUT");
		Descripter.generateDescription(	buffer,
										depth + 1,
										this.getSourceMap());
		buffer.append(Constant.NEW_LINE_CHARS);
		Descripter.makeIndent(	buffer,
								depth);
		buffer.append("OUTPUT");
		Descripter.generateDescription(	buffer,
										depth + 1,
										this.getResultMap());

		if (this.isError()) {
			buffer.append(Constant.NEW_LINE_CHARS);
			Descripter.makeIndent(	buffer,
									depth);
			buffer.append("Error");
			buffer.append(Constant.NEW_LINE_CHARS);
			Descripter.generateDescription(	buffer,
											depth + 1,
											this.getError().toString());
			buffer.append(Constant.NEW_LINE_CHARS);
		}
		buffer.append(Constant.NEW_LINE_CHARS);
		Descripter.makeIndent(	buffer,
								depth);
		buffer.append("Data Description FINISH");
	}

	/**
	 * @see com.kskyb.broker.util.Descriptable#getDescription()
	 */
	@Override
	public synchronized final String getDescription() {
		if (this.desc != null && !this.modify) {
			return this.desc;
		}
		StringBuffer buffer = null;
		try {
			buffer = ResourceContext.getAppendable();

			this.descript(	buffer,
							0);

			this.desc = buffer.toString();
			this.modify = false;
		}
		catch (Throwable thw) {
			return thw.toString();
		}
		finally {
			ResourceContext.recycleAppendable(buffer);
		}
		return this.desc;
	}

	/**
	 * @see java.lang.Object#toString()
	 */
	@Override
	public final String toString() {
		return "CommandData-" + this.processId;
	}

	/**
	 * 
	 */
	public static final CommandData GOODBYE_DATA = getInstance();

	/**
	 * 
	 */
	public static final CommandData COMMIT_DATA = getInstance();

	/**
	 * 
	 */
	public static final CommandData ROLLBACK_DATA = getInstance();

	/**
	 * 
	 */
	public static final CommandData PING_DATA = getInstance();

	/**
	 * 
	 */
	public static final CommandData BEGIN_TRAN_DATA = getInstance();

	static {
		GOODBYE_DATA.setProcessId("~~");
		GOODBYE_DATA.setMessageType(CommandData.MESSAGE_TYPE_GOODBYE);

		COMMIT_DATA.setProcessId("~~");
		COMMIT_DATA.setMessageType(CommandData.MESSAGE_TYPE_COMMIT);

		ROLLBACK_DATA.setProcessId("~~");
		ROLLBACK_DATA.setMessageType(CommandData.MESSAGE_TYPE_ROLLBACK);

		PING_DATA.setProcessId("~~");
		PING_DATA.setMessageType(CommandData.MESSAGE_TYPE_PINGPONG);

		BEGIN_TRAN_DATA.setProcessId("~~");
		BEGIN_TRAN_DATA.setMessageType(CommandData.MESSAGE_TYPE_BEGIN_TRAN);
	}
}
