package com.kskyb.broker.middleware.connector;

/**
 * <AUTHOR>
 * 
 */
public abstract class MessageFilter {
	/**
	 * 
	 */
	private static MessageFilter filter = new MessageFilter() {
		/**
		 * @see com.kskyb.broker.middleware.connector.MessageFilter#generateMessage(java.lang.Throwable)
		 */
		@Override
		protected String generateMessage(Throwable thw) {
			return thw.getLocalizedMessage();
		}
	};

	/**
	 * @return the filter
	 */
	public static final MessageFilter getFilter() {
		return filter;
	}

	/**
	 * @param filter
	 *            the filter to set
	 */
	protected static final void setFilter(MessageFilter filter) {
		MessageFilter.filter = filter;
	}

	/**
	 * @param thw
	 * @return
	 */
	public final String filter(Throwable thw) {
		return thw != null	? this.generateMessage(thw)
							: "OK";
	}

	/**
	 * @param thw
	 * @return
	 */
	protected abstract String generateMessage(Throwable thw);
}
