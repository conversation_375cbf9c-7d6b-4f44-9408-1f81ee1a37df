package com.kskyb.broker.template;

import java.io.InputStream;
import java.io.Reader;

import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ThreadAttribute;
import com.kskyb.broker.util.ArrayMap;


/**
 * <AUTHOR>
 * 
 */
public final class TemplateProcessorFactory {
	/**
	 * 
	 */
	private static final boolean ECHO = System.getProperty(	"echo.template.processing",
															"false").equalsIgnoreCase("true");

	/**
	 * 
	 */
	public static final String TEMPLATE_PROCESSOR_NAME = TemplateProcessorFactory.class.getName().concat(".template.processor");

	static {
		ThreadAttribute.regist(	TEMPLATE_PROCESSOR_NAME,
								"thread.attribute.index.template.processor.factory.processor.name");
	}

	/**
	 * 
	 */
	public static final String INNER_PROCESSOR_NAME = "~inner.processor~";

	/**
	 * 
	 */
	private static final TemplateProcessorFactory factory = new TemplateProcessorFactory();

	/**
	 * @return
	 */
	public static TemplateProcessorFactory getFactory() {
		return factory;
	}

	/**
	 * 
	 */
	private final ArrayMap<String, TemplateProcessor> processorMap = new ArrayMap<String, TemplateProcessor>();

	/**
	 * 
	 */
	private TemplateProcessorFactory() {
		//
	}

	/**
	 * 
	 */
	private TemplateProcessor defaultTemplateProcessor = null;

	/**
	 * @return the defaultTemplateProcessor
	 */
	public final TemplateProcessor getDefaultTemplateProcessor() {
		return this.defaultTemplateProcessor;
	}

	/**
	 * @param processor
	 *            the defaultTemplateProcessor to set
	 */
	public final void setDefaultTemplateProcessor(final TemplateProcessor processor) {
		this.defaultTemplateProcessor = processor;
		this.registProcessor(processor);
	}

	/**
	 * @param processor
	 */
	public final void registProcessor(final TemplateProcessor processor) {
		if (this.processorMap.containsKey(processor.getName())) {
			return;
		}

		this.processorMap.put(	processor.getName(),
								processor);
	}

	/**
	 * @param in
	 * @param charset
	 * @param map
	 * @return
	 * @throws Exception
	 */
	public final String convert(final String templateId,
								final InputStream in,
								final String charset,
								final Object map) throws Exception {
		return this.convert(null,
							templateId,
							in,
							charset,
							map);
	}

	/**
	 * @param processorId
	 * @param in
	 * @param charset
	 * @param map
	 * @return
	 * @throws Exception
	 */
	public final String convert(final String processorId,
								final String templateId,
								final InputStream in,
								final String charset,
								final Object map) throws Exception {
		final TemplateInformation information = this.create(processorId,
															templateId,
															in,
															charset);

		return information.convert(map);
	}

	/**
	 * @param processorId
	 * @param in
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public final TemplateInformation create(final String processorId,
											final String templateId,
											final InputStream in,
											final String charset) throws Exception {
		return this.lookup(processorId).create(	templateId,
												in,
												charset);
	}

	/**
	 * @param processorId
	 * @param templateId
	 * @param raw
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public final TemplateInformation create(final String processorId,
											final String templateId,
											final String raw,
											final String charset) throws Exception {
		return this.lookup(processorId).create(	templateId,
												raw,
												charset);
	}

	/**
	 * @param processorId
	 * @param templateId
	 * @param reader
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public final TemplateInformation create(final String processorId,
											final String templateId,
											final Reader reader,
											final String charset) throws Exception {
		return this.lookup(processorId).create(	templateId,
												reader,
												charset);
	}

	// ------------------- convert string direct
	/**
	 * @param raw
	 * @param charset
	 * @param map
	 * @return
	 * @throws Exception
	 */
	public final String convert(final String raw,
								final String charset,
								final Object map) {
		return this.convert(null,
							raw,
							charset,
							map);
	}

	/**
	 * @param templateId
	 * @param raw
	 * @param charset
	 * @param map
	 * @return
	 * @throws Exception
	 */
	public String convert(	final String templateId,
							final String raw,
							final String charset,
							final Object map) {
		final TemplateInformation information = this.create(templateId,
															raw,
															charset);

		return information.convert(map);
	}

	/**
	 * @param id
	 * @param in
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public final TemplateInformation create(final String templateId,
											final String raw,
											final String charset) {
		return this.lookup().create(templateId,
									raw,
									charset);
	}

	/**
	 * @return
	 */
	public final TemplateProcessor lookup() {
		final Object id = ThreadAttribute.getAttribute(TEMPLATE_PROCESSOR_NAME);
		return this.lookup(id == null	? null
										: id.toString());
	}

	/**
	 * @param id
	 * @return
	 */
	public final TemplateProcessor lookup(final String id) {
		final TemplateProcessor processor = this.search(id);

		if (ECHO) {
			System.out.println("choose template processor:" + processor.getName() + "/" + processor.getClass().getName());
		}

		if (processor == null) {
			ExceptionGW.filterMessage(	"cannot.find.processor",
										String.valueOf(id));
		}

		return processor;
	}

	/**
	 * @param id
	 * @return
	 */
	public final TemplateProcessor search(final String id) {
		return id == null	? this.defaultTemplateProcessor
							: this.processorMap.get(id);
	}

	/**
	 * @param processorId
	 * @param templateId
	 * @return
	 */
	public final TemplateInformation lookupTemplate(final String processorId,
													final String templateId) {
		return this.lookup(processorId).lookup(templateId);
	}
}
