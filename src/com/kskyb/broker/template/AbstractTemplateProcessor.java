package com.kskyb.broker.template;

import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;

import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.util.ArrayMap;


/**
 * <AUTHOR>
 * 
 */
public abstract class AbstractTemplateProcessor
												implements
												TemplateProcessor {
	/**
	 * 
	 */
	private static final TemplateInformation BLANK = new TemplateInformation() {
		/**
		 * @see com.kskyb.broker.template.TemplateInformation#isValidTemplate()
		 */
		@Override
		public boolean isValidTemplate() {
			return false;
		}

		/**
		 * @see com.kskyb.broker.template.TemplateInformation#convert(java.lang.Object)
		 */
		@Override
		public final String convert(final Object map) {
			return null;
		}

		/**
		 * @see com.kskyb.broker.template.TemplateInformation#process(java.lang.Object, java.io.OutputStream)
		 */
		@Override
		public final void process(	final Object map,
									final OutputStream out) throws Exception {
			//
		}

		/**
		 * @see com.kskyb.broker.template.TemplateInformation#process(java.lang.Object, java.io.Writer)
		 */
		@Override
		public final void process(	final Object map,
									final Writer out) throws Exception {
			//

		}

		/**
		 * @see com.kskyb.broker.lang.Destroyable#destroy()
		 */
		public final void destroy() {
			//
		}
	};

	/**
	 * 
	 */
	final ArrayMap<String, TemplateInformation> map = new ArrayMap<String, TemplateInformation>();

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#lookup(java.lang.String)
	 */
	public final TemplateInformation lookup(final String templateId) {
		return this.map.get(templateId);
	}

	/**
	 * @param name
	 * @param info
	 * @return
	 */
	private final TemplateInformation regist(	final String name,
												final TemplateInformation info) {
		this.map.put(	name,
						info);
		return info;
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#clearTemplateCache()
	 */
	public void clearTemplateCache() {
		ObjectFinalizer.finalizeMap(this.map);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#removeTemplateCache(java.lang.String)
	 */
	public void removeTemplateCache(String name) {
		ObjectFinalizer.destroy(this.map.remove(name));
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make()
	 */
	public final TemplateInformation make() {
		return BLANK;
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.io.InputStream, java.lang.String)
	 */
	public final TemplateInformation make(	final InputStream in,
											final String charset) {
		return this.make(	null,
							in,
							charset);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.lang.String, java.lang.String)
	 */
	public final TemplateInformation make(	final String raw,
											final String charset) {
		return this.make(	null,
							raw,
							charset);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.io.Reader, java.lang.String)
	 */
	public final TemplateInformation make(	final Reader reader,
											final String charset) {
		return this.make(	null,
							reader,
							charset);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String)
	 */
	public TemplateInformation create(final String name) {
		return this.create(	name,
							false);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String, java.io.InputStream, java.lang.String)
	 */
	public TemplateInformation create(	final String name,
										final InputStream in,
										final String charset) {
		return this.create(	name,
							in,
							charset,
							false);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String, java.lang.String, java.lang.String)
	 */
	public TemplateInformation create(	final String name,
										final String raw,
										final String charset) {
		return this.create(	name,
							raw,
							charset,
							false);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String, java.io.Reader, java.lang.String)
	 */
	public TemplateInformation create(	final String name,
										final Reader reader,
										final String charset) {
		return this.create(	name,
							reader,
							charset,
							false);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String, boolean)
	 */
	public TemplateInformation create(	final String name,
										final boolean force) {
		if (!force) {
			final TemplateInformation cache = this.lookup(name);
			if (cache != null) {
				return cache;
			}
		}
		return this.regist(	name,
							this.make());
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String, java.io.InputStream, java.lang.String)
	 */
	public final TemplateInformation create(final String name,
											final InputStream in,
											final String charset,
											final boolean force) {
		if (!force && name != null) {
			final TemplateInformation cache = this.lookup(name);
			if (cache != null) {
				return cache;
			}
		}
		return this.regist(	name,
							this.make(	name,
										in,
										charset));
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String, java.io.Reader, java.lang.String)
	 */
	public final TemplateInformation create(final String name,
											final Reader reader,
											final String charset,
											final boolean force) {
		if (!force && name != null) {
			final TemplateInformation cache = this.lookup(name);
			if (cache != null) {
				return cache;
			}
		}
		return this.regist(	name,
							this.make(	name,
										reader,
										charset));
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#create(java.lang.String, java.lang.String, java.lang.String)
	 */
	public final TemplateInformation create(final String name,
											final String raw,
											final String charset,
											final boolean force) {
		if (!force && name != null) {
			final TemplateInformation cache = this.lookup(name);
			if (cache != null) {
				return cache;
			}
		}
		return this.regist(	name,
							this.make(	name,
										raw,
										charset));
	}
}
