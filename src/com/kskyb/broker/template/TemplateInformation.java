package com.kskyb.broker.template;

import java.io.OutputStream;
import java.io.Writer;

import com.kskyb.broker.lang.Destroyable;


/**
 * <AUTHOR>
 * 
 */
public abstract class TemplateInformation
											implements
											Destroyable {
	/**
	 * @return
	 */
	public boolean isValidTemplate() {
		return true;
	}

	/**
	 * @param map
	 * @return
	 * @throws Exception
	 */
	public abstract String convert(final Object map);

	/**
	 * @param map
	 * @param out
	 * @throws Exception
	 */
	public abstract void process(	final Object map,
									final OutputStream out) throws Exception;

	/**
	 * @param map
	 * @param out
	 * @throws Exception
	 */
	public abstract void process(	final Object map,
									final Writer out) throws Exception;
}
