package com.kskyb.broker.template;

import java.io.InputStream;
import java.io.Reader;

/**
 * <AUTHOR>
 * 
 */
public interface TemplateProcessor {
	/**
	 * @return
	 */
	public abstract String getName();

	/**
	 * @param templateId
	 * @return
	 */
	public abstract TemplateInformation lookup(final String templateId);

	/**
	 * 
	 */
	public abstract void clearTemplateCache();

	/**
	 * @param name
	 */
	public abstract void removeTemplateCache(String name);

	/**
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation make() throws Exception;

	/**
	 * @param in
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation make(	final InputStream in,
												final String charset);

	/**
	 * @param raw
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation make(	final String raw,
												final String charset);

	/**
	 * @param reader
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation make(	final Reader reader,
												final String charset);

	/**
	 * @param in
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation make(	final String name,
												final InputStream in,
												final String charset);

	/**
	 * @param raw
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation make(	final String name,
												final String raw,
												final String charset);

	/**
	 * @param reader
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation make(	final String name,
												final Reader reader,
												final String charset);

	/**
	 * @param name
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(final String name);

	/**
	 * @param name
	 * @param in
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(	final String name,
												final InputStream in,
												final String charset);

	/**
	 * @param name
	 * @param raw
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(	final String name,
												final String raw,
												final String charset);

	/**
	 * @param name
	 * @param reader
	 * @param charset
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(	final String name,
												final Reader reader,
												final String charset);

	/**
	 * @param name
	 * @param force
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(	final String name,
												final boolean force);

	/**
	 * @param name
	 * @param in
	 * @param charset
	 * @param force
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(	final String name,
												final InputStream in,
												final String charset,
												final boolean force);

	/**
	 * @param name
	 * @param raw
	 * @param charset
	 * @param force
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(	final String name,
												final String raw,
												final String charset,
												final boolean force);

	/**
	 * @param name
	 * @param reader
	 * @param charset
	 * @param force
	 * @return
	 * @throws Exception
	 */
	public abstract TemplateInformation create(	final String name,
												final Reader reader,
												final String charset,
												final boolean force);

}
