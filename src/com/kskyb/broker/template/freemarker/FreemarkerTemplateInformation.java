package com.kskyb.broker.template.freemarker;

import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;

import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.template.TemplateInformation;
import com.kskyb.broker.util.CommonConfigure;
import com.kskyb.broker.util.Descripter;

import freemarker.template.Template;
import freemarker.template.TemplateException;

/**
 * <AUTHOR>
 * 
 */
public final class FreemarkerTemplateInformation
												extends
												TemplateInformation {
	static {
		CommonConfigure.regist(	"echo.freemarker.template.process",
								"false",
								"echo.freemarker.template.process.desc");
	}

	/**
	 * 
	 */
	final Template template;

	/**
	 * @param t
	 */
	FreemarkerTemplateInformation(Template t) {
		this.template = t;
	}

	/**
	 * @see com.kskyb.broker.template.TemplateInformation#convert(java.lang.Object)
	 */
	@Override
	public final String convert(final Object mapping) {
		StringWriter out = null;
		try {
			out = new StringWriter();
			this.process(	mapping,
							out);
			return out.toString();
		}
		finally {
			ObjectFinalizer.close(out);
		}
	}

	/**
	 * @see com.kskyb.broker.template.TemplateInformation#process(java.lang.Object, java.io.OutputStream)
	 */
	@Override
	public final void process(	final Object mapping,
								final OutputStream out) throws Exception {
		Writer writer = null;
		try {
			writer = new StreamWriterWrapper(out);
			this.process(	mapping,
							writer);
			writer.flush();
		}
		finally {
			ObjectFinalizer.close(writer);
		}
	}

	/**
	 * @see com.kskyb.broker.lang.Destroyable#destroy()
	 */
	public void destroy() {
		//
	}

	/**
	 * @see com.kskyb.broker.template.TemplateInformation#process(java.lang.Object, java.io.Writer)
	 */
	@Override
	public final void process(	final Object mapping,
								final Writer writer) {
		final boolean ECHO = CommonConfigure.getBoolean("echo.freemarker.template.process");
		try {
			if (ECHO) {
				System.out.println("template:" + this.template.toString());
				System.out.println("mapping:" + Descripter.makeDescription(mapping));
			}
			this.template.process(	mapping,
									writer);
		}
		catch (TemplateException e) {
			throw new RuntimeException(	ResourceContext.getBundledMessage(	"template.process.template.error",
																			this.template.toString()),
										e);
		}
		catch (IOException e) {
			throw new RuntimeException(	ResourceContext.getBundledMessage(	"template.process.io.error",
																			this.template.toString()),
										e);
		}
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	static class StreamWriterWrapper
									extends
									Writer {
		/**
		 * 
		 */
		final OutputStream out;

		/**
		 * @param _out
		 */
		StreamWriterWrapper(OutputStream _out) {
			this.out = _out;
		}

		/**
		 * @see java.io.Writer#close()
		 */
		@Override
		public void close() throws IOException {
			//
		}

		/**
		 * @see java.io.Writer#flush()
		 */
		@Override
		public void flush() throws IOException {
			this.out.flush();
		}

		/**
		 * @see java.io.Writer#write(char[], int, int)
		 */
		@Override
		public void write(	char[] cbuf,
							int off,
							int len) throws IOException {
			String src = new String(cbuf,
									off,
									len);
			this.out.write(src.getBytes());
		}

		/**
		 * @see java.io.Writer#write(java.lang.String)
		 */
		@Override
		public void write(String str) throws IOException {
			if (str == null) {
				return;
			}
			this.out.write(str.getBytes());
		}
	}
}
