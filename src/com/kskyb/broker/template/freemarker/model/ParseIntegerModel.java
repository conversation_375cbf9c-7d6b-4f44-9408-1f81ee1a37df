package com.kskyb.broker.template.freemarker.model;

import java.util.List;

import com.kskyb.broker.util.NumberUtil;

import freemarker.core.Environment;
import freemarker.template.SimpleNumber;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;

/**
 * <AUTHOR>
 * 
 */
public final class ParseIntegerModel
									implements
									TemplateMethodModelMod {

	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateMethodModelMod#exec(freemarker.core.Environment, java.util.List)
	 */
	@Override
	public final TemplateModel exec(final Environment env,
									final List<String> arguments) throws TemplateModelException {
		final int ln = arguments.size();

		if (ln < 1 || ln > 2) {
			throw new RuntimeException("invalid arg count");
		}

		final String number = arguments.get(0);

		switch (ln) {
			case 1: {
				return new SimpleNumber(NumberUtil.convertNumeric(	number,
																	false));
			}

			case 2: {
				final int radix = NumberUtil.convertInt(arguments.get(1));
				return new SimpleNumber(Integer.parseInt(	number,
															radix));
			}
		}
		return new SimpleNumber(0);
	}
}
