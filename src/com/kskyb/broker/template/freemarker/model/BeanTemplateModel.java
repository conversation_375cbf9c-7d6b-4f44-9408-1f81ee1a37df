package com.kskyb.broker.template.freemarker.model;

import java.util.Map;

import com.kskyb.broker.util.HashedObjectContainer;

import freemarker.core.Environment;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;

/**
 * <AUTHOR>
 * 
 */
public final class BeanTemplateModel
									extends
									TemplateDelegateModel.Impl {
	/**
	 * <AUTHOR>
	 * 
	 */
	static final class InitializeErrorModel
											implements
											TemplateModel {
		/**
		 * 
		 */
		final String message;

		/**
		 * 
		 */
		final Throwable thw;

		/**
		 * @param message
		 * @param thw
		 */
		InitializeErrorModel(	String message,
								Throwable thw) {
			this.message = message;
			this.thw = thw;
		}
	}

	/**
	 * 
	 */
	static final HashedObjectContainer<String, TemplateModel> container = new HashedObjectContainer<String, TemplateModel>() {
		/**
		 * @see com.kskyb.broker.util.HashedObjectContainer#make(java.lang.Object)
		 */
		@Override
		protected TemplateModel make(String key) {
			try {
				final Class clazz = Class.forName(key);
				final Object instance = clazz.newInstance();
				if (instance instanceof TemplateModel) {
					return (TemplateModel) instance;
				}
				return new InitializeErrorModel(key + " is not template model",
												null);
			}
			catch (Throwable e) {
				return new InitializeErrorModel("load template model error",
												e);
			}
		}
	};

	/**
	 * 
	 */
	public BeanTemplateModel() {
		super();
	}

	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateDelegateModel.Impl#delegate(freemarker.core.Environment, java.util.Map)
	 */
	@Override
	protected final void delegate(	final Environment env,
									final Map<String, String> args) throws TemplateException {
		final String var = args.get("var");
		final String name = args.get("clazz");

		final TemplateModel model = container.getInstance(name);

		if (model instanceof InitializeErrorModel) {
			final InitializeErrorModel elm = (InitializeErrorModel) model;
			throw new TemplateException(elm.message,
										elm.thw,
										env);
		}
		env.setGlobalVariable(	var,
								model);
	}
}
