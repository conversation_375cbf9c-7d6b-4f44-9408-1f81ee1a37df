package com.kskyb.broker.template.freemarker.model;

import freemarker.template.TemplateModel;

/**
 * <AUTHOR>
 * 
 */
public interface TemplateNestedObjectModel
											extends
											TemplateModel {

	/**
	 * @return
	 */
	abstract Object getNestedObject();

	/**
	 * <AUTHOR>
	 * 
	 */
	public static abstract class Impl
										implements
										TemplateNestedObjectModel {
		/**
		 * 
		 */
		final Object nestedObject;

		/**
		 * @param nestedObject
		 */
		public Impl(Object nestedObject) {
			super();
			this.nestedObject = nestedObject;
		}

		/**
		 * @see freemarker.template.TemplateModel#getNestedObject()
		 */
		@Override
		public final Object getNestedObject() {
			return nestedObject instanceof TemplateNestedObjectModel ? ((TemplateNestedObjectModel) nestedObject).getNestedObject()
																	: nestedObject;
		}
	}
}
