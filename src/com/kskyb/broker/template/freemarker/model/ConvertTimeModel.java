package com.kskyb.broker.template.freemarker.model;

import java.util.Map;

import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.util.DateUtil;

import freemarker.core.Environment;
import freemarker.template.TemplateException;

/**
 * <AUTHOR>
 * 
 */
public final class ConvertTimeModel
									extends
									TemplateDelegateModel.Impl {
	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateDelegateModel.Impl#delegate(freemarker.core.Environment, java.util.Map)
	 */
	@Override
	protected final void delegate(	final Environment env,
									final Map<String, String> args) throws TemplateException {
		final String time = args.get("time");
		final String from = args.get("from");
		final String to = args.get("to");

		final String source = this.extractSource(	time,
													from,
													to);

		final String format = args.get("format");

		final String convert = DateUtil.getFormatDate(	format,
														source);

		try {
			env.getOut().write(convert);
		}
		catch (Throwable thw) {
			ExceptionGW.ignore(	thw,
								"ConvertTimeModel.template.echo.error");
		}
	}

	/**
	 * @param time
	 * @param from
	 * @param to
	 * @return
	 */
	private final String extractSource(	final String time,
										final String from,
										final String to) {
		if (from == null && to == null) {
			return time;
		}

		final String convert = to == null	? time.substring(Integer.parseInt(from))
											: time.substring(	Integer.parseInt(from),
																Integer.parseInt(to));
		return convert;
	}
}
