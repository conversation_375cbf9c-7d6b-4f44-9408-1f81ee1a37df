package com.kskyb.broker.template.freemarker.model;

import java.util.Map;

import com.kskyb.broker.lang.ExceptionGW;

import freemarker.core.Environment;
import freemarker.template.TemplateException;

/**
 * <AUTHOR>
 * 
 */
public final class SubStringModel
									extends
									TemplateDelegateModel.Impl {
	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateDelegateModel.Impl#delegate(freemarker.core.Environment, java.util.Map)
	 */
	@Override
	protected final void delegate(	final Environment env,
									final Map<String, String> args) throws TemplateException {
		final String source = args.get("source");
		final String from = args.get("from");
		final String to = args.get("to");
		try {
			final String convert = to == null	? source.substring(Integer.parseInt(from))
												: source.substring(	Integer.parseInt(from),
																	Integer.parseInt(to));
			env.getOut().write(convert);
		}
		catch (Throwable thw) {
			ExceptionGW.ignore(	thw,
								"SubStringModel.template.echo.error");
		}
	}

}
