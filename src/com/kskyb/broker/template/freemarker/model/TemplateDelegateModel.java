package com.kskyb.broker.template.freemarker.model;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

import freemarker.core.Environment;
import freemarker.template.TemplateDirectiveBody;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;

/**
 * <AUTHOR>
 * 
 */
public interface TemplateDelegateModel
										extends
										TemplateModel {
	/**
	 * 
	 */
	public static final String KEY_EVALUATE_BODY = TemplateDelegateModel.class.getName().concat("-eval");

	/**
	 * @param env
	 * @param params
	 * @param loopVars
	 * @param body
	 * @throws TemplateException
	 * @throws IOException
	 */
	public abstract void delegate(	Environment env,
									Map<String, String> attrs,
									TemplateModel[] loopVars,
									TemplateDirectiveBody body)	throws TemplateException,
																IOException;

	/**
	 * <AUTHOR>
	 * 
	 */
	public abstract class Impl
								implements
								TemplateDelegateModel {
		/**
		 * @return
		 */
		protected final String getCurrentKey() {
			return getCurrentKey(String.valueOf(Thread.currentThread()));
		}

		/**
		 * @return
		 */
		protected final String getCurrentKey(String addon) {
			return TemplateDelegateModel.class.getName().concat("-").concat(addon);
		}

		/**
		 * 유져 디파인 테그의 메인 구현 메소드<br>
		 * 포함된 하부 테그를 처리하고 결과를 하위 구현 메소드로 전달한다.
		 * 
		 * @see com.kskyb.broker.template.freemarker.model.TemplateDelegateModel#delegate(freemarker.core.Environment, java.util.Map,
		 *      freemarker.template.TemplateModel[], freemarker.template.TemplateDirectiveBody)
		 */
		@Override
		public final void delegate(	final Environment env,
									final Map<String, String> attrs,
									final TemplateModel[] loopVars,
									final TemplateDirectiveBody body)	throws TemplateException,
																		IOException {
			if (loopVars == null && body == null) {
				delegate(	env,
							attrs);
				return;
			}

			final String bodyResult = this.processDirectiveBody(env,
																body);

			attrs.put(	KEY_EVALUATE_BODY,
						bodyResult);

			delegate(	env,
						attrs);
		}

		/**
		 * @param body
		 * @return
		 * @throws TemplateException
		 * @throws IOException
		 */
		private final String processDirectiveBody(	final Environment env,
													final TemplateDirectiveBody body)	throws TemplateException,
																						IOException {
			if (body == null) {
				return null;
			}
			try {
				this.bodyRenderStart(env);
				StringWriter writer = new StringWriter();
				body.render(writer);
				final String bodyResult = writer.toString();
				writer.close();
				return bodyResult;
			}
			finally {
				this.bodyRenderFinish(env);
			}
		}

		/**
		 * 템플릿 바디 렌더링 시작
		 * 
		 * @param env
		 */
		protected void bodyRenderStart(final Environment env) {
			//
		}

		/**
		 * 템플릿 바디 렌더링 종료
		 * 
		 * @param env
		 */
		protected void bodyRenderFinish(final Environment env) {
			//
		}

		/**
		 * 테그의 구현 본체
		 * 
		 * @param env
		 * @param args
		 *            유져 테그에 선언된 attr의 헤쉬값
		 * @throws TemplateException
		 */
		protected abstract void delegate(	Environment env,
											Map<String, String> attrs) throws TemplateException;
	}
}
