package com.kskyb.broker.template.freemarker.model;

import java.util.List;
import java.util.Map;

import freemarker.core.Environment;
import freemarker.template.TemplateBooleanModel;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;

/**
 * <AUTHOR>
 * 
 */
public final class ContainKeyModel
									implements
									TemplateMethodModelMod {
	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateMethodModelMod#exec(freemarker.core.Environment, java.util.List)
	 */
	public final TemplateModel exec(final Environment env,
									final List<String> arguments) throws TemplateModelException {
		if (arguments == null || arguments.size() == 0) {
			return TemplateBooleanModel.FALSE;
		}

		final String path = String.valueOf(arguments.get(0));

		final Object current = env.getRootMapping();

		if (current instanceof Map) {
			return ((Map) current).containsKey(path) ? TemplateBooleanModel.TRUE
													: TemplateBooleanModel.FALSE;
		}

		return TemplateBooleanModel.FALSE;
	}
}
