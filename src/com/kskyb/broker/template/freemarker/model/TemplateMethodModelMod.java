package com.kskyb.broker.template.freemarker.model;

import java.util.List;

import freemarker.core.Environment;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;

/**
 * <AUTHOR>
 * 
 */
public interface TemplateMethodModelMod
										extends
										TemplateModel {
	/**
	 * @param arguments
	 * @return
	 * @throws TemplateModelException
	 */
	public TemplateModel exec(	Environment env,
								List<String> arguments) throws TemplateModelException;
}
