package com.kskyb.broker.template.freemarker.model;

import com.kskyb.broker.util.Descripter;

import freemarker.template.TemplateModelException;
import freemarker.template.TemplateScalarModel;

/**
 * <AUTHOR>
 * 
 */
public interface TemplateExceptionModel
										extends
										TemplateScalarModel,
										TemplateNestedObjectModel {
	/**
	 * <AUTHOR>
	 * 
	 */
	public static class TemplateExceptionModelImpl
													extends
													TemplateNestedObjectModel.Impl
																					implements
																					TemplateExceptionModel {

		/**
		 * @param thw
		 */
		public TemplateExceptionModelImpl(Throwable thw) {
			super(thw);
		}

		/**
		 * @see freemarker.template.TemplateScalarModel#getAsString()
		 */
		@Override
		public final String getAsString() throws TemplateModelException {
			return Descripter.makeDescription(getNestedObject());
		}
	}
}
