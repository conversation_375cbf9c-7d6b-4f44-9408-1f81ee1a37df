package com.kskyb.broker.template.freemarker.model;

import java.util.Map;

import freemarker.core.Environment;
import freemarker.template.TemplateException;

/**
 * <AUTHOR>
 * 
 */
public final class AssignTemplateModel
										extends
										TemplateDelegateModel.Impl {
	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateDelegateModel.Impl#delegate(freemarker.core.Environment, java.util.Map)
	 */
	@Override
	protected final void delegate(	final Environment env,
									final Map<String, String> args) throws TemplateException {
		final Map<String, Object> map = env.getRootMapping();
		for (final Map.Entry<String, String> entry : args.entrySet()) {
			map.put(entry.getKey(),
					entry.getValue());
		}
	}
}
