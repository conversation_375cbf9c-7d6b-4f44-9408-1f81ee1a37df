package com.kskyb.broker.template.freemarker;

import java.lang.reflect.Modifier;

import com.kskyb.broker.event.BroadcastEventBroker;
import com.kskyb.broker.template.freemarker.codec.TemplateModelAttributeCodec;
import com.kskyb.broker.template.freemarker.codec.TemplateModelBooleanCodec;
import com.kskyb.broker.template.freemarker.codec.TemplateModelCollectionCodec;
import com.kskyb.broker.template.freemarker.codec.TemplateModelExceptionCodec;
import com.kskyb.broker.template.freemarker.codec.TemplateModelHashCodec;
import com.kskyb.broker.template.freemarker.codec.TemplateModelNumberCodec;
import com.kskyb.broker.template.freemarker.codec.TemplateModelSelectorCodec;
import com.kskyb.broker.util.ArrayMap;

import freemarker.template.ObjectWrapperAdapter;
import freemarker.template.SimpleScalar;
import freemarker.template.TemplateModel;

/**
 * <AUTHOR>
 * 
 */
public final class TemplateModelWrapper
										extends
										ObjectWrapperAdapter {
	/**
	 * 
	 */
	private static TemplateModelWrapper wrapper = null;

	/**
	 * @return the wrapper
	 */
	public static final TemplateModelWrapper getWrapper() {
		if (wrapper == null) {
			wrapper = new TemplateModelWrapper();
			wrapper.regist(new TemplateModelHashCodec());
			wrapper.regist(new TemplateModelCollectionCodec());
			wrapper.regist(new TemplateModelSelectorCodec());
			wrapper.regist(new TemplateModelBooleanCodec());
			wrapper.regist(new TemplateModelNumberCodec());
			wrapper.regist(new TemplateModelAttributeCodec());
			wrapper.regist(new TemplateModelExceptionCodec());
		}
		return wrapper;
	}

	/**
	 * @param obj
	 * @return
	 */
	public static final TemplateModel convert(final Object obj) {
		return getWrapper().wrap(obj);
	}

	/**
	 * 
	 */
	private final ArrayMap<Class<?>, TemplateModelCodecSupport> mapClass = new ArrayMap<Class<?>, TemplateModelCodecSupport>();

	/**
	 * 
	 */
	private final ArrayMap<Class<?>, TemplateModelCodecSupport> mapInterface = new ArrayMap<Class<?>, TemplateModelCodecSupport>();

	/**
	 * 
	 */
	private TemplateModelWrapper() {
		super();
	}

	/**
	 * @param obj
	 * @return
	 */
	public final TemplateModel wrap(final Object obj) {
		if (obj == null) {
			return null;
		}

		if (obj instanceof TemplateModel) {
			return (TemplateModel) obj;
		}

		final Class<?> clazz = obj.getClass();
		final TemplateModelCodecSupport codec = this.lookupInterfaceCodec(clazz);
		return codec == null ? new SimpleScalar(String.valueOf(obj))
							: codec.decode(obj);
	}

	/**
	 * @param codec
	 */
	public synchronized final void remove(final TemplateModelCodecSupport codec) {
		int index = this.mapInterface.getValueIndex(codec);
		while (index >= 0) {
			final Class clazz = this.mapInterface.getKeyByIndex(index);

			this.eventRemove(	codec,
								clazz);

			this.mapInterface.removeByIndex(index);
			index = this.mapInterface.getValueIndex(codec);
		}

		index = this.mapClass.getValueIndex(codec);
		while (index >= 0) {
			final Class clazz = this.mapClass.getKeyByIndex(index);

			this.eventRemove(	codec,
								clazz);
			this.mapClass.removeByIndex(index);
			index = this.mapClass.getValueIndex(codec);
		}
	}

	/**
	 * @param codec
	 * @param clazz
	 */
	private final void eventRemove(	final TemplateModelCodecSupport codec,
									final Class clazz) {
		if (BroadcastEventBroker.getInstance().isAcceptable(TemplateModelCodecEvent.class)) {
			final TemplateModelCodecEvent event = new TemplateModelCodecEvent(TemplateModelCodecEvent.CODEC_REMOVE_EVENT);
			event.setAttribute(	TemplateModelCodecEvent.CODEC_SUPPORT,
								codec);
			event.setAttribute(	TemplateModelCodecEvent.CODEC_TYPE,
								clazz);
			BroadcastEventBroker.getInstance().broadcast(event);
		}
	}

	/**
	 * @param codec
	 */
	public synchronized final void regist(final TemplateModelCodecSupport codec) {
		final Class[] array = codec.getTypeArray();
		final int length = array.length;

		for (int i = 0; i < length; i++) {
			final Class clazz = array[i];

			final int mod = clazz.getModifiers();
			TemplateModelCodecSupport support = null;
			if (Modifier.isFinal(mod)) {
				support = this.mapClass.put(clazz,
											codec);
			}
			else {
				support = this.mapInterface.put(clazz,
												codec);
			}

			if (TemplateModelCodecEvent.isAvailable()) {
				if (support == null) {
					final TemplateModelCodecEvent event = new TemplateModelCodecEvent(TemplateModelCodecEvent.CODEC_REGIST_EVENT);
					event.setAttribute(	TemplateModelCodecEvent.CODEC_SUPPORT,
										codec);
					event.setAttribute(	TemplateModelCodecEvent.CODEC_TYPE,
										clazz);
					BroadcastEventBroker.getInstance().broadcast(event);
				}
				else {
					final TemplateModelCodecEvent event = new TemplateModelCodecEvent(TemplateModelCodecEvent.CODEC_CHANGE_EVENT);
					event.setAttribute(	TemplateModelCodecEvent.CODEC_CHANGE_BEFORE_CODEC,
										support);
					event.setAttribute(	TemplateModelCodecEvent.CODEC_CHANGE_CHANGE_CODEC,
										codec);
					event.setAttribute(	TemplateModelCodecEvent.CODEC_TYPE,
										clazz);
					BroadcastEventBroker.getInstance().broadcast(event);
				}
			}
		}
	}

	/**
	 * @param clazz
	 * @return
	 */
	private synchronized final TemplateModelCodecSupport lookupInterfaceCodec(final Class clazz) {
		TemplateModelCodecSupport codec = this.mapClass.get(clazz);

		if (codec != null) {
			return codec;
		}
		final ArrayMap<Class<?>, TemplateModelCodecSupport> map = this.mapInterface;
		final int length = map.size();

		for (int i = 0; i < length; i++) {
			final Class inf = map.getKeyByIndex(i);

			if (inf.isAssignableFrom(clazz)) {
				codec = map.getValueByIndex(i);
				this.mapClass.put(	clazz,
									codec);
				return codec;
			}
		}
		return null;
	}
}
