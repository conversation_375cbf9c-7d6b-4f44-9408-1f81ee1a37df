package com.kskyb.broker.template.freemarker;

import com.kskyb.broker.event.BroadcastEvent;
import com.kskyb.broker.event.BroadcastEventBroker;
import com.kskyb.broker.event.BroadcastEventListenerAdapter;
import com.kskyb.broker.util.ClassConstantsMap;

/**
 * <AUTHOR>
 * 
 */
public final class TemplateModelCodecEvent
											extends
											BroadcastEvent {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2001691296784748141L;

	/**
	 * 
	 */
	public static final int CODEC_REGIST_EVENT = 1;

	/**
	 * 
	 */
	public static final int CODEC_REMOVE_EVENT = 2;

	/**
	 * 
	 */
	public static final int CODEC_CHANGE_EVENT = 3;

	/**
	 * 
	 */
	public static final ClassConstantsMap CONSTANT_MAP = new ClassConstantsMap(	TemplateModelCodecEvent.class,
																				"CODEC_");

	/**
	 * @param caseValue
	 * @return
	 */
	public static final String getEventName(final int caseValue) {
		return CONSTANT_MAP.getConstantName(caseValue);
	}

	/**
	 * @see com.kskyb.broker.event.BroadcastEvent#getEventName()
	 */
	@Override
	public String getEventName() {
		return getEventName(this.getType());
	}

	/**
	 * @return
	 */
	public static final boolean isAvailable() {
		return BroadcastEventBroker.getInstance().isAcceptable(TemplateModelCodecEvent.class);
	}

	/**
	 * 
	 */
	public static final String CODEC_TYPE = "CODEC_TYPE";

	/**
	 * 
	 */
	public static final String CODEC_SUPPORT = "CODEC_SUPPORT";

	/**
	 * 
	 */
	public static final String CODEC_CHANGE_BEFORE_CODEC = "CODEC_CHANGE_BEFORE_CODEC";

	/**
	 * 
	 */
	public static final String CODEC_CHANGE_CHANGE_CODEC = "CODEC_CHANGE_CHANGE_CODEC";

	/**
	 * @param type
	 */
	public TemplateModelCodecEvent(int type) {
		super(type);
	}

	/**
	 * @see com.kskyb.broker.event.BroadcastEvent#getEventClassType()
	 */
	@Override
	protected Class<? extends BroadcastEvent> getEventClassType() {
		return TemplateModelCodecEvent.class;
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public static abstract class Listener
											extends
											BroadcastEventListenerAdapter<TemplateModelCodecEvent> {
		/**
		 * 
		 */
		protected Listener() {
			this(false);
		}

		/**
		 * @param auto
		 */
		protected Listener(boolean auto) {
			super(	TemplateModelCodecEvent.class,
					auto);
		}
	}
}
