package com.kskyb.broker.template.freemarker;

import freemarker.template.TemplateModel;

/**
 * <AUTHOR>
 * 
 */
public abstract class TemplateModelCodecSupport {
	/**
	 * 
	 */
	public static final int PRIMITIVE_TYPE_BY_CLASS = 1;

	/**
	 * 
	 */
	public static final int PRIMITIVE_TYPE_BY_INTERFACE = 2;

	/**
	 * 
	 */
	public static final int PRIMITIVE_TYPE_BY_INSTANCE = 3;

	/**
	 * 
	 */
	private final Class[] typeArray;

	/**
	 * @param type
	 * @param array
	 */
	protected TemplateModelCodecSupport(final Class[] array) {
		super();
		this.typeArray = array;
	}

	/**
	 * @return the typeArray
	 */
	public final Class[] getTypeArray() {
		return this.typeArray;
	}

	/**
	 * @param obj
	 * @return
	 * @throws XMLDigestFailException
	 */
	public abstract TemplateModel decode(Object obj);
}
