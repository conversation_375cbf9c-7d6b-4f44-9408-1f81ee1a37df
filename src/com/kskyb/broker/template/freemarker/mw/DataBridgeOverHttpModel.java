package com.kskyb.broker.template.freemarker.mw;

import java.util.Map;

import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.template.freemarker.model.TemplateDelegateModel;
import com.kskyb.broker.util.ClassConstantsMap;
import com.kskyb.broker.util.StringUtils;

import freemarker.core.Environment;
import freemarker.template.TemplateException;

/**
 * <AUTHOR>
 * 
 */
public abstract class DataBridgeOverHttpModel
												extends
												TemplateDelegateModel.Impl {
	/**
	 * 
	 */
	public static final int TYPE_STREAM = 1;

	/**
	 * 
	 */
	public static final int TYPE_XML = TYPE_STREAM + 1;

	/**
	 * 
	 */
	public static final int TYPE_XML_PLAIN = TYPE_XML + 1;

	/**
	 * 
	 */
	public static final ClassConstantsMap MAP = new ClassConstantsMap(	DataBridgeOverHttpModel.class,
																		"TYPE_");

	/**
	 * @see world.dragon.template.freemarker.model.BodySupportModel#bodyRenderStart(freemarker.core.Environment)
	 */
	@Override
	protected void bodyRenderStart(final Environment env) {
		env.getRootMapping().remove(this.getCurrentKey());
	}

	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateDelegateModel.Impl#delegate(freemarker.core.Environment, java.util.Map)
	 */
	@Override
	protected final void delegate(	final Environment env,
									final Map<String, String> attrs) throws TemplateException {
		final String to = attrs.get("to");

		if (to == null || StringUtils.isNull(	to,
												true)) {
			throw new TemplateException(ResourceContext.getBundledMessage("to.attribute.is.missing"),
										env);
		}

		final Map<String, Object> param = (Map<String, Object>) env.getRootMapping().remove(this.getCurrentKey());

		final Object object = this.bridge(	env,
											attrs,
											param);

		if (object == null) {
			return;
		}

		env.getRootMapping().put(	to,
									object);
	}

	/**
	 * @param env
	 * @param command
	 * @param params
	 * @param args
	 * @return
	 */
	protected abstract Object bridge(	final Environment env,
										final Map<String, String> attr,
										final Map<String, Object> param) throws TemplateException;
}
