package com.kskyb.broker.template.freemarker;

import java.util.Map;

import com.kskyb.broker.lang.Constant;
import com.kskyb.broker.lang.Reflector;
import com.kskyb.broker.template.freemarker.model.TemplateNestedObjectModel;

import freemarker.template.TemplateException;
import freemarker.template.TemplateHashModel;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import freemarker.template.TemplateScalarModel;

/**
 * <AUTHOR>
 * 
 */
public final class TemplateUtilities {
	/**
	 * @param attrs
	 * @param key
	 * @param robust
	 * @return
	 * @throws TemplateException
	 */
	public static final String getAttribute(final Map<String, String> attrs,
											final String key,
											final boolean robust) throws TemplateException {
		final String value = attrs.get(key);

		if (value == null && robust) {
			throw new TemplateModelException(key + " is missing!");
		}

		return value == null ? Constant.BLANK_STRING
							: value;
	}

	/**
	 * @param source
	 * @param name
	 * @return
	 */
	public static final Object getAttribute(final Object source,
											final String name) throws TemplateException {
		if (source instanceof TemplateHashModel) {
			return _getAttribute(	(TemplateHashModel) source,
									name);
		}
		else if (source instanceof TemplateNestedObjectModel) {
			return Reflector.getInstanceAttribute(	((TemplateNestedObjectModel) source).getNestedObject(),
													name);
		}

		return Reflector.getInstanceAttribute(	source,
												name);
	}

	/**
	 * @param source
	 * @param name
	 * @return
	 * @throws TemplateException
	 */
	private static final Object _getAttribute(	final TemplateHashModel source,
												final String name) throws TemplateException {
		final TemplateModel template = source.get(name);

		if (template instanceof TemplateNestedObjectModel) {
			return ((TemplateNestedObjectModel) template).getNestedObject();
		}
		else if (template instanceof TemplateScalarModel) {
			return ((TemplateScalarModel) template).getAsString();
		}
		return template.toString();
	}
}
