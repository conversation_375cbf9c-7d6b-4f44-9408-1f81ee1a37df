package com.kskyb.broker.template.freemarker.codec;

import java.io.Serializable;
import java.util.Map;

import com.kskyb.broker.template.freemarker.TemplateModelCodecSupport;
import com.kskyb.broker.template.freemarker.TemplateModelWrapper;
import com.kskyb.broker.template.freemarker.model.TemplateNestedObjectModel;

import freemarker.template.TemplateHashModelSupport;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;

/**
 * <AUTHOR>
 * 
 */
public final class TemplateModelHashCodec
											extends
											TemplateModelCodecSupport {

	/**
	 * 
	 */
	public TemplateModelHashCodec() {
		super(new Class[] {
			Map.class
		});
	}

	/**
	 * @see com.kskyb.broker.template.freemarker.TemplateModelCodecSupport#decode(java.lang.Object)
	 */
	@Override
	public final TemplateModel decode(final Object node) {
		return new MapTemplateModel((Map) node);
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public static class MapTemplateModel
										extends
										TemplateNestedObjectModel.Impl
																		implements
																		TemplateHashModelSupport,
																		Serializable {
		/**
		 * 
		 */
		final Map<String, Object> map;

		/**
		 * @param map
		 */
		public MapTemplateModel(final Map<String, Object> map) {
			super(map);
			this.map = map;
		}

		/**
		 * @see freemarker.template.TemplateHashModel#get(java.lang.String)
		 */
		public final TemplateModel get(final String key) throws TemplateModelException {
			return TemplateModelWrapper.convert(this.map.get(key));
		}

		/**
		 * @see freemarker.template.TemplateHashModel#isEmpty()
		 */
		public final boolean isEmpty() throws TemplateModelException {
			return false;
		}

		/**
		 * @see freemarker.template.TemplateHashModelSupport#put(java.lang.String, java.lang.Object)
		 */
		public void put(String key,
						Object value) {
			this.map.put(	key,
							value);
		}

	}
}
