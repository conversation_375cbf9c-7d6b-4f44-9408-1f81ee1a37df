package com.kskyb.broker.template.freemarker.codec;

import java.io.IOException;
import java.io.Serializable;

import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.template.freemarker.TemplateModelCodecSupport;
import com.kskyb.broker.template.freemarker.TemplateModelWrapper;
import com.kskyb.broker.template.freemarker.model.TemplateNestedObjectModel;
import com.kskyb.broker.util.ISelector;
import com.kskyb.broker.util.SelectorWrapper;
import com.kskyb.broker.util.param.CommonDataSheet;

import freemarker.template.TemplateCacheModel;
import freemarker.template.TemplateCollectionModel;
import freemarker.template.TemplateHashModel;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import freemarker.template.TemplateModelIterator;

/**
 * <AUTHOR>
 * 
 */
public final class TemplateModelSelectorCodec
												extends
												TemplateModelCodecSupport {

	/**
	 * 
	 */
	public TemplateModelSelectorCodec() {
		super(new Class[] {
			ISelector.class,
			CommonDataSheet.class
		});
	}

	/**
	 * @see com.kskyb.broker.template.freemarker.TemplateModelCodecSupport#decode(java.lang.Object)
	 */
	@Override
	public final TemplateModel decode(final Object node) {
		return new SelectableTemplateModel(node);
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public static class SelectableTemplateModel
												extends
												TemplateNestedObjectModel.Impl
																				implements
																				TemplateCollectionModel,
																				TemplateModelIterator,
																				TemplateHashModel,
																				TemplateCacheModel,
																				Serializable {
		/**
		 * 
		 */
		private static final long serialVersionUID = -7822230408342308919L;

		/**
		 * 
		 */
		final ISelector sheet;

		/**
		 * 
		 */
		final InnerMapModel record = new InnerMapModel();

		/**
		 * 
		 */
		boolean open = false;

		/**
		 * @param sheet
		 */
		public SelectableTemplateModel(final Object sheet) {
			super(sheet);
			this.sheet = SelectorWrapper.wrap(sheet);
		}

		/**
		 * 
		 */
		private static final int MODE_UNKNOWN = 0;

		/**
		 * 
		 */
		private static final int MODE_ITERATOR = 1;

		/**
		 * 
		 */
		private static final int MODE_MAP = 2;

		/**
		 * 
		 */
		int mode = MODE_UNKNOWN;

		/**
		 * @see freemarker.template.TemplateCollectionModel#iterator()
		 */
		public TemplateModelIterator iterator() throws TemplateModelException {
			switch (this.mode) {
				case MODE_UNKNOWN: {
					this.mode = MODE_ITERATOR;
					try {
						this.sheet.open();
					}
					catch (Exception e) {
						throw new TemplateModelException(	"Sheet open error!",
															e);
					}
					break;
				}

				case MODE_ITERATOR:
				case MODE_MAP: {
					/**
					 * Map model 에서 iterator 모드로는 넘어올수 있다.
					 */
					this.mode = MODE_ITERATOR;
					try {
						this.sheet.rewind();
					}
					catch (Exception e) {
						throw new TemplateModelException(	"Sheet open error!",
															e);
					}
					break;
				}

				default: {
					ExceptionGW.filterMessage(	-1,
												"invalid");
				}
			}

			return this;
		}

		/**
		 * @see freemarker.template.TemplateModelIterator#next()
		 */
		public TemplateModel next() throws TemplateModelException {
			try {
				this.sheet.next();
			}
			catch (IOException e) {
				// e.printStackTrace();
			}
			return this.record;
		}

		/**
		 * @see freemarker.template.TemplateModelIterator#hasNext()
		 */
		public final boolean hasNext() throws TemplateModelException {
			try {
				if (this.sheet.hasNext()) {
					return true;
				}
			}
			catch (IOException e) {
				// e.printStackTrace();
			}
			/**
			 * patch가 끝났다면 어떤 상태로든 전환이 가능하다.
			 */
			this.mode = MODE_UNKNOWN;
			return false;
		}

		/**
		 * @see freemarker.template.TemplateHashModel#get(java.lang.String)
		 */
		public TemplateModel get(String key) throws TemplateModelException {
			switch (this.mode) {
				case MODE_UNKNOWN: {
					this.iterator();
					this.next();
					this.mode = MODE_MAP;
					break;
				}
				case MODE_MAP: {
					break;
				}
				case MODE_ITERATOR: {
					ExceptionGW.filterMessage(	-1,
												"cannot switch [iterate mode] to [map mode]");
					return null;
				}
			}
			final Object obj = this.sheet.getObject(key);
			return TemplateModelWrapper.convert(obj);
		}

		/**
		 * @see freemarker.template.TemplateHashModel#isEmpty()
		 */
		public boolean isEmpty() throws TemplateModelException {
			return false;
		}

		/**
		 * @see com.kskyb.broker.lang.Destroyable#destroy()
		 */
		public final void destroy() {
			ObjectFinalizer.close(this.sheet);
		}

		/**
		 * <AUTHOR>
		 * 
		 */
		public class InnerMapModel
									implements
									TemplateHashModel {

			/**
			 * @see freemarker.template.TemplateHashModel#get(java.lang.String)
			 */
			public TemplateModel get(String key) throws TemplateModelException {
				final Object obj = SelectableTemplateModel.this.sheet.getObject(key);
				return TemplateModelWrapper.convert(obj);
			}

			/**
			 * @see freemarker.template.TemplateHashModel#isEmpty()
			 */
			public boolean isEmpty() throws TemplateModelException {
				return false;
			}
		}
	}
}
