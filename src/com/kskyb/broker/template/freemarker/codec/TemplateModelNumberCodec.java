package com.kskyb.broker.template.freemarker.codec;

import com.kskyb.broker.template.freemarker.TemplateModelCodecSupport;
import com.kskyb.broker.template.freemarker.model.TemplateNestedObjectModel;

import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import freemarker.template.TemplateNumberModel;

/**
 * <AUTHOR>
 * 
 */
public class TemplateModelNumberCodec
										extends
										TemplateModelCodecSupport {
	/**
	 * 
	 */
	public TemplateModelNumberCodec() {
		super(new Class[] {
			Byte.class,
			Byte.TYPE,
			Short.class,
			Short.TYPE,
			Integer.class,
			Integer.TYPE,
			Long.class,
			Long.TYPE,
			Float.class,
			Float.TYPE,
			Double.class,
			Double.TYPE
		});
	}

	/**
	 * @see com.kskyb.broker.template.freemarker.TemplateModelCodecSupport#decode(java.lang.Object)
	 */
	@Override
	public final TemplateModel decode(final Object obj) {
		final Number number = (Number) obj;
		return new NumberModel(number);
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	final class NumberModel
							extends
							TemplateNestedObjectModel.Impl
															implements
															TemplateNumberModel {
		/**
		 * 
		 */
		final Number number;

		/**
		 * @param number
		 */
		public NumberModel(final Number number) {
			super(number);
			this.number = number;
		}

		/**
		 * @see freemarker.template.TemplateNumberModel#getAsNumber()
		 */
		@Override
		public Number getAsNumber() throws TemplateModelException {
			return this.number;
		}
	}
}
