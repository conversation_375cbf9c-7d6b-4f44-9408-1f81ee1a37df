package com.kskyb.broker.template.freemarker.codec;

import java.io.Serializable;
import java.util.List;

import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.template.freemarker.TemplateModelCodecSupport;
import com.kskyb.broker.template.freemarker.TemplateModelWrapper;
import com.kskyb.broker.template.freemarker.model.TemplateNestedObjectModel;

import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import freemarker.template.TemplateSequenceModel;

/**
 * <AUTHOR>
 * 
 */
public final class TemplateModelCollectionCodec
												extends
												TemplateModelCodecSupport {

	public TemplateModelCollectionCodec() {
		super(new Class<?>[] {
			Iterable.class
		});
	}

	/**
	 * @see com.kskyb.broker.template.freemarker.TemplateModelCodecSupport#decode(java.lang.Object)
	 */
	@Override
	public TemplateModel decode(Object node) {
		return new CollectionTemplateModel((Iterable) node);
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public static class CollectionTemplateModel
												extends
												TemplateNestedObjectModel.Impl
																				implements
																				TemplateSequenceModel,
																				Serializable {
		/**
		 * 
		 */
		private static final long serialVersionUID = 6188521961797766303L;

		/**
		 * 
		 */
		final Object[] array;

		/**
		 * 
		 */
		final int length;

		/**
		 * @param iterable
		 */
		public CollectionTemplateModel(Iterable iterable) {
			super(iterable);
			List<Object> list = null;
			try {
				list = ResourceContext.getList();

				for (final Object obj : iterable) {
					list.add(obj);
				}
				this.array = list.toArray();
				this.length = this.array.length;
			}
			finally {
				ResourceContext.recycleList(list);
			}
		}

		/**
		 * @param objs
		 */
		public CollectionTemplateModel(final Object[] objs) {
			super(objs);
			this.length = objs.length;
			this.array = new Object[this.length];
			System.arraycopy(	objs,
								0,
								this.array,
								0,
								this.length);
		}

		/**
		 * @see freemarker.template.TemplateSequenceModel#get(int)
		 */
		public TemplateModel get(int index) throws TemplateModelException {
			final Object obj = this.array[index];
			return obj instanceof TemplateModel	? (TemplateModel) obj
												: TemplateModelWrapper.convert(obj);
		}

		/**
		 * @see freemarker.template.TemplateSequenceModel#size()
		 */
		public int size() throws TemplateModelException {
			return this.length;
		}
	}
}
