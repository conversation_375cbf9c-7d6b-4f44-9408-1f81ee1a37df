package com.kskyb.broker.template.freemarker;

import java.util.List;

import com.kskyb.broker.template.freemarker.model.TemplateMethodModelMod;
import com.kskyb.broker.util.ArrayMap;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.broker.util.UniqueKey;

import freemarker.core.Environment;
import freemarker.template.SimpleNumber;
import freemarker.template.SimpleScalar;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;

/**
 * <AUTHOR>
 * 
 */
public final class TemplateMethodBrokeModel
											implements
											TemplateMethodModelMod {
	/**
	 * 
	 */
	private static final TemplateMethodBrokeModel model = new TemplateMethodBrokeModel();

	/**
	 * @return the model
	 */
	public static final TemplateMethodBrokeModel getModel() {
		return model;
	}

	/**
	 * 
	 */
	final ArrayMap<String, TemplateMethodBrokeNode> map = new ArrayMap<String, TemplateMethodBrokeNode>();

	/**
	 * 
	 */
	private TemplateMethodBrokeModel() {
		super();
		this.map.put(	"unique",
						new TemplateMethodBrokeNode() {
							/**
							 * @see com.kskyb.broker.template.freemarker.TemplateMethodBrokeNode#exec(java.util.List)
							 */
							public final TemplateModel exec(final Environment env,
															final List<String> arguments) throws Exception {
								return new SimpleScalar(UniqueKey.getUniqueKey());
							}
						});
		this.map.put(	"iNow",
						new TemplateMethodBrokeNode() {

							/**
							 * @see com.kskyb.broker.template.freemarker.TemplateMethodBrokeNode#exec(java.util.List)
							 */
							public final TemplateModel exec(final Environment env,
															final List<String> arguments) throws Exception {
								return new SimpleNumber(System.currentTimeMillis());
							}
						});
		this.map.put(	"sNow",
						new TemplateMethodBrokeNode() {

							/**
							 * @see com.kskyb.broker.template.freemarker.TemplateMethodBrokeNode#exec(java.util.List)
							 */
							public final TemplateModel exec(final Environment env,
															final List<String> arguments) throws Exception {
								return new SimpleScalar(DateUtil.getSerialTime());
							}
						});
		this.map.put(	"time",
						new TemplateMethodBrokeNode() {

							/**
							 * @see com.kskyb.broker.template.freemarker.TemplateMethodBrokeNode#exec(java.util.List)
							 */
							public final TemplateModel exec(final Environment env,
															final List<String> arguments) throws Exception {
								if (arguments.size() < 2) {
									throw new TemplateModelException("argument is null or size zero");
								}

								final String format = arguments.get(1).toString();

								return new SimpleScalar(DateUtil.getFormatDate(format));
							}
						});
	}

	/**
	 * @param name
	 * @param node
	 */
	public synchronized final void registBrokeNode(	final String name,
													final TemplateMethodBrokeNode node) {
		if (this.map.containsKey(name)) {
			throw new RuntimeException("already regist node name:" + name);
		}

		this.map.put(	name,
						node);
	}

	/**
	 * @see com.kskyb.broker.template.freemarker.model.TemplateMethodModelMod#exec(freemarker.core.Environment, java.util.List)
	 */
	public final TemplateModel exec(final Environment env,
									final List<String> arguments) throws TemplateModelException {
		if (arguments == null || arguments.size() < 1) {
			throw new TemplateModelException("argument is null or size zero");
		}

		final String key = arguments.get(0).toString();

		final TemplateMethodBrokeNode node = this.map.get(key);

		if (node == null) {
			throw new TemplateModelException("cannot find sub node:" + key);
		}

		try {
			return node.exec(	env,
								arguments);
		}
		catch (Exception e) {
			throw new TemplateModelException(	"process sub node error:" + key,
												e);
		}
	}

}
