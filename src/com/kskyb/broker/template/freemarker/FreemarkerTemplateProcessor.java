package com.kskyb.broker.template.freemarker;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.util.jar.Attributes;

import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.template.AbstractTemplateProcessor;
import com.kskyb.broker.template.TemplateInformation;
import com.kskyb.broker.template.TemplateProcessorFactory;
import com.kskyb.broker.template.freemarker.model.AssignTemplateModel;
import com.kskyb.broker.template.freemarker.model.BeanTemplateModel;
import com.kskyb.broker.template.freemarker.model.ContainKeyModel;
import com.kskyb.broker.template.freemarker.model.ConvertTimeModel;
import com.kskyb.broker.template.freemarker.model.ParseIntegerModel;
import com.kskyb.broker.template.freemarker.model.SubStringModel;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;

/**
 * <AUTHOR>
 * 
 */
/**
 * <AUTHOR>
 * 
 */
public final class FreemarkerTemplateProcessor
												extends
												AbstractTemplateProcessor {

	/**
	 * 
	 */
	public static final String ID = "freemarker";

	/**
	 * 
	 */
	private static final FreemarkerTemplateProcessor processor = new FreemarkerTemplateProcessor();

	/**
	 * @return the processor
	 */
	public static final FreemarkerTemplateProcessor getProcessor() {
		return processor;
	}

	/**
	 * @param attr
	 */
	public static final void load(final Attributes attr) {
		getProcessor();
	}

	/**
	 * 
	 */
	private final Configuration configuration;

	/**
	 * 
	 */
	private FreemarkerTemplateProcessor() {
		this.configuration = this.getDefaultConfiguration();

		TemplateProcessorFactory.getFactory().registProcessor(this);
	}

	public final Configuration getDefaultConfiguration() {
		final Configuration conf = new Configuration();
		conf.setObjectWrapper(TemplateModelWrapper.getWrapper());
		conf.setSharedVariable(	"method",
								TemplateMethodBrokeModel.getModel());
		conf.setSharedVariable(	"containKey",
								new ContainKeyModel());
		conf.setSharedVariable(	"formatDate",
								new ConvertTimeModel());
		conf.setSharedVariable(	"substring",
								new SubStringModel());
		conf.setSharedVariable(	"assign",
								new AssignTemplateModel());
		conf.setSharedVariable(	"load",
								new BeanTemplateModel());
		conf.setSharedVariable(	"int",
								new ParseIntegerModel());
		conf.setClassicCompatible(true);
		return conf;
	}

	/**
	 * @param name
	 * @param tm
	 */
	public final void setSharedVariable(final String name,
										final TemplateModel tm) {
		this.configuration.setSharedVariable(	name,
												tm);
	}

	/**
	 * @param name
	 * @param tm
	 */
	public final void setSharedVariable(final String name,
										final Object tm) throws TemplateModelException {
		this.configuration.setSharedVariable(	name,
												tm);
	}

	/**
	 * @param name
	 */
	public final void removeSharedVariable(String name) {
		this.configuration.removeSharedVariable(name);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#getName()
	 */
	public String getName() {
		return ID;
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.lang.String, java.io.InputStream, java.lang.String)
	 */
	public final TemplateInformation make(	final String name,
											final InputStream in,
											final String charset) {
		Reader reader = null;
		try {
			reader = new InputStreamReader(	in,
											charset);
			return this.make(	name,
								reader,
								charset);
		}
		catch (UnsupportedEncodingException e) {
			throw new RuntimeException(	ResourceContext.getBundledMessage(	"charactorset.error",
																			charset),
										e);
		}
		finally {
			ObjectFinalizer.close(reader);
		}
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.lang.String, java.lang.String, java.lang.String)
	 */
	public final TemplateInformation make(	final String name,
											final String raw,
											final String charset) {
		return this.make(	name,
							raw,
							this.configuration,
							charset);
	}

	/**
	 * @param name
	 * @param raw
	 * @param conf
	 * @param charset
	 * @return
	 */
	public final TemplateInformation make(	final String name,
											final String raw,
											final Configuration conf,
											final String charset) {
		Reader reader = null;
		try {
			reader = new StringReader(raw);
			return this.make(	name,
								reader,
								conf == null ? this.configuration
											: conf,
								charset);
		}
		finally {
			ObjectFinalizer.close(reader);
		}
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.lang.String, java.io.Reader, java.lang.String)
	 */
	public final TemplateInformation make(	final String name,
											final Reader reader,
											final String charset) {
		return this.make(	name,
							reader,
							this.configuration,
							charset);
	}

	/**
	 * @param name
	 * @param reader
	 * @param conf
	 * @param charset
	 * @return
	 */
	public final TemplateInformation make(	final String name,
											final Reader reader,
											final Configuration conf,
											final String charset) {
		Template template = null;
		try {
			template = new Template(name,
									reader,
									conf == null ? this.configuration
												: conf,
									charset);
		}
		catch (IOException e) {
			throw new RuntimeException(	ResourceContext.getBundledMessage("occur.io.exception.during.create.template"),
										e);
		}
		finally {
			ObjectFinalizer.close(reader);
		}

		return new FreemarkerTemplateInformation(template);
	}
}
