package com.kskyb.broker.template;

import java.io.IOException;
import java.util.Map;
import java.util.Properties;

import com.kskyb.broker.lang.Constant;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.lang.ThreadAttribute;
import com.kskyb.broker.util.ISelector;


/**
 * <AUTHOR>
 * 
 */
public final class CommonTemplateUtil {

	/**
	 * @param src
	 * @param map
	 * @param start
	 * @param end
	 * @param empty
	 * @return
	 */
	public static final String getConvertString(final String src,
												final Object map,
												final String start,
												final String end,
												final boolean empty) {
		StringBuffer buffer = null;
		try {
			buffer = ResourceContext.getAppendable();
			ThreadAttribute.setAttribute(	BUFFER_LISTENER_KEY,
											buffer);
			transferString(	BUFFER_LISTENER,
							src,
							map,
							start,
							end,
							empty);
			return buffer.toString();
		}
		catch (IOException e) {
			return src;
		}
		finally {
			ThreadAttribute.removeAttribute(BUFFER_LISTENER_KEY);
			ResourceContext.recycleAppendable(buffer);
		}
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	static final class StringBufferTemplateProcessListener
															implements
															TemplateProcessListener {
		/**
		 * 
		 */
		final StringBuffer buffer;

		/**
		 * @param buffer
		 */
		public StringBufferTemplateProcessListener(StringBuffer buffer) {
			this.buffer = buffer;
		}

		/**
		 * @see com.kskyb.broker.template.TemplateProcessListener#startProcess()
		 */
		public void startProcess() {
			this.buffer.setLength(0);
		}

		/**
		 * @see com.kskyb.broker.template.TemplateProcessListener#occurAppendElm(java.lang.String)
		 */
		public void occurAppendElm(String elm) {
			this.buffer.append(elm);

		}

		/**
		 * @see com.kskyb.broker.template.TemplateProcessListener#finishProcess()
		 */
		public void finishProcess() {
			//
		}

	}

	/**
	 * @param buffer
	 * @return
	 */
	public static final TemplateProcessListener createStringBufferTemplateProcessListener(final StringBuffer buffer) {
		return new StringBufferTemplateProcessListener(buffer);
	}

	/**
	 * @param buffer
	 * @param src
	 * @param map
	 * @param start
	 * @param end
	 * @param empty
	 * @throws IOException
	 */
	public static final void transferString(final StringBuffer buffer,
											final String src,
											final Object map,
											final String start,
											final String end,
											final boolean empty) throws IOException {
		transferString(	new StringBufferTemplateProcessListener(buffer),
						src,
						map,
						start,
						end,
						empty);
	}

	/**
	 * @param buffer
	 * @param src
	 * @param map
	 * @param start
	 * @param end
	 * @param empty
	 * @throws IOException
	 */
	public static final void transferString(final TemplateProcessListener buffer,
											final String src,
											final Object map,
											final String start,
											final String end,
											final boolean empty) throws IOException {
		buffer.startProcess();
		if (src == null) {
			buffer.finishProcess();
			return;
		}

		int idx1 = 0;
		int idx2 = 0;
		int idx3 = 0;

		String key = null;
		Object value = null;

		while (true) {
			idx1 = src.indexOf(	start,
								idx3);
			if (idx1 < 0) {
				break;
			}

			idx2 = src.indexOf(	end,
								idx1 + start.length());
			if (idx2 < 0) {
				break;
			}

			key = src.substring(idx1 + start.length(),
								idx2);
			value = extract(map,
							key);

			buffer.occurAppendElm(src.substring(idx3,
												idx1));
			if (value == null) {
				if (empty) {
					buffer.occurAppendElm(start);
					buffer.occurAppendElm(key);
					buffer.occurAppendElm(end);
				}
			}
			else {
				buffer.occurAppendElm(value.toString());
			}

			idx3 = idx2 + end.length();
		}

		buffer.occurAppendElm(src.substring(idx3));
		buffer.finishProcess();
		return;
	}

	/**
	 * @param target
	 * @param index
	 * @return
	 */
	private static final Object extract(final Object target,
										final String index) {
		if (target instanceof Properties) {
			return ((Properties) target).getProperty(index);
		}
		else if (target instanceof Map) {
			return ((Map) target).get(index);
		}
		else if (target instanceof ISelector) {
			return ((ISelector) target).getObject(index);
		}
		else {
			return null;
		}
	}

	/**
	 * 
	 */
	private static final String BUFFER_LISTENER_KEY = CommonTemplateUtil.class.getName().concat("-stringbuffer");

	static {
		ThreadAttribute.regist(	BUFFER_LISTENER_KEY,
								"thread.attribute.index.template.util.buffer.listener.key");
	}

	/**
	 * 
	 */
	private static final TemplateProcessListener BUFFER_LISTENER = new TemplateProcessListener() {

		/**
		 * @see com.kskyb.broker.template.TemplateProcessListener#startProcess()
		 */
		public void startProcess() {
			//
		}

		/**
		 * @see com.kskyb.broker.template.TemplateProcessListener#occurAppendElm(java.lang.String)
		 */
		public void occurAppendElm(String elm) {
			final StringBuffer buffer = ThreadAttribute.getTypedAttribute(	BUFFER_LISTENER_KEY,
																			StringBuffer.class);
			if (buffer == null) {
				ExceptionGW.filterMessage(	-1,
											"current.thread.did.not.have.buffer.attribute");
				return;
			}
			buffer.append(elm);
		}

		/**
		 * @see com.kskyb.broker.template.TemplateProcessListener#finishProcess()
		 */
		public void finishProcess() {
			//
		}
	};

	/**
	 * @param source
	 * @return
	 */
	public static final String striptComment(	final String body,
												final boolean serialize) {
		if (body == null) {
			return Constant.BLANK_STRING;
		}

		final int length = body.length();

		StringBuffer buffer = null;
		try {
			buffer = ResourceContext.getAppendable();

			boolean commentLine = false;
			boolean commentBlock = false;
			for (int i = 0; i < length; i++) {
				final char elm = body.charAt(i);
				boolean append = true;
				switch (elm) {
					case '\r':
					case '\n': {
						if (serialize) {
							if (!commentBlock && !commentLine && buffer.length() > 0 && buffer.charAt(buffer.length() - 1) != ' ') {
								buffer.append(' ');
							}
							append = false;
						}
						commentLine = false;
						break;
					}
					case ' ':
					case '\t': {
						if (serialize) {
							if (!commentBlock && !commentLine && buffer.length() > 0 && buffer.charAt(buffer.length() - 1) != ' ') {
								buffer.append(' ');
							}
							append = false;
						}
						break;
					}
					case '-': {
						if (body.charAt(i + 1) == '-') {
							commentLine = true;
						}
						break;
					}
					case '/': {
						if (body.charAt(i + 1) == '*') {
							commentBlock = true;
						}
						else if (body.charAt(i - 1) == '*') {
							commentBlock = false;
							append = false;
						}
						break;
					}
					default: {
						break;
					}
				}
				if (commentLine || commentBlock) {
					continue;
				}

				if (append) {
					buffer.append(elm);
				}
			}

			return buffer.toString();
		}
		finally {
			ResourceContext.recycleAppendable(buffer);
		}
	}
}
