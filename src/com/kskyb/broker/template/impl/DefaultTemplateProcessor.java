package com.kskyb.broker.template.impl;

import java.io.InputStream;
import java.io.Reader;
import java.util.jar.Attributes;

import com.kskyb.broker.io.IOUtil;
import com.kskyb.broker.template.AbstractTemplateProcessor;
import com.kskyb.broker.template.TemplateInformation;
import com.kskyb.broker.template.TemplateProcessorFactory;


/**
 * <AUTHOR>
 * 
 */
public final class DefaultTemplateProcessor
											extends
											AbstractTemplateProcessor {
	/**
	 * @param attr
	 */
	public static final void load(final Attributes attr) {
		DefaultTemplateProcessor processor = new DefaultTemplateProcessor();
		TemplateProcessorFactory.getFactory().setDefaultTemplateProcessor(processor);
	}

	/**
	 * 
	 */
	private DefaultTemplateProcessor() {
		//
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#getName()
	 */
	public final String getName() {
		return System.getProperty(	"system.default.template.processor.name",
									"inner.processor");
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.lang.String, java.io.InputStream, java.lang.String)
	 */
	public final TemplateInformation make(	final String name,
											final InputStream in,
											final String charset) {
		return this.make(	IOUtil.readFullyString(	in,
													charset),
							charset);
	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.lang.String, java.io.Reader, java.lang.String)
	 */
	public final TemplateInformation make(	final String name,
											final Reader reader,
											final String charset) {
		return this.make(	IOUtil.readFullyString(reader),
							charset);

	}

	/**
	 * @see com.kskyb.broker.template.TemplateProcessor#make(java.lang.String, java.lang.String, java.lang.String)
	 */
	public final TemplateInformation make(	final String name,
											final String raw,
											final String charset) {
		return new DefaultTemplateInformation(	raw,
												charset);
	}

}
