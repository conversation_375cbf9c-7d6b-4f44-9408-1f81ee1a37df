package com.kskyb.broker.lang;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.w3c.dom.Document;
import org.w3c.dom.Element;

import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.template.CommonTemplateUtil;
import com.kskyb.broker.util.ArrayMap;
import com.kskyb.broker.util.SequenceMap;
import com.kskyb.broker.util.StringDigestDelegator;
import com.kskyb.broker.util.StringUtils;
import com.kskyb.broker.util.xml.XMLBuilderDelegator;
import com.kskyb.broker.util.xml.XMLUtil;


/**
 * <AUTHOR>
 * 
 */
public final class ResourceContext {
	/**
	 * 
	 */
	private static final ArrayMap<String, LocaleEntry> providerMap = new ArrayMap<String, LocaleEntry>();

	static {
		// -- load resources -- //
		{
			InputStream in = null;
			try {
				in = ClassLoader.getSystemResourceAsStream("resource.xml");
				if (in == null) {
					CommonLogGW.warn("resource.xml is not found in system path");
				}
				else {
					ResourceContext.registResourceProvider(ResourceContext.getXmlProvider(in));
				}
			}
			catch (Throwable e) {
				e.printStackTrace();
				CommonLogGW.error(	"resource.xml load error",
									e);
			}
			finally {
				ObjectFinalizer.close(in);
			}
		}

	}

	/**
	 * @param provider
	 */
	public static final void registResourceProvider(final ResourceProvider provider) {
		final String[] locales = provider.getLocale();
		if (locales == null) {
			ExceptionGW.filterMessage(	-1,
										"locale.name.cannot.be.null");
			return;
		}
		final int length = locales.length;
		synchronized (providerMap) {
			for (int i = 0; i < length; i++) {
				final String locale = locales[i];
				LocaleEntry entry = providerMap.get(locale);
				if (entry == null) {
					entry = new LocaleEntry(locale);
					providerMap.put(locale,
									entry);
					ResourceContextEvent.fireLocaleCreate(	locale,
															entry);
				}
				provider.publish(entry);
			}
		}
	}

	/**
	 * @param template
	 * @return
	 */
	public static final String getBundledMessage(final String template) {
		return getBundledMessage(	template,
									Constant.BLANK_STRING_ARRAY);
	}

	/**
	 * @param template
	 * @param strings
	 * @return
	 */
	public static final String getBundledMessage(	final String template,
													final String... strings) {
		final String locale = ThreadAttribute.getLocale();

		final LocaleEntry entry = providerMap.get(locale);

		if (entry == null) {
			return StringUtils.getSequenceMappedMessage(template,
														strings);
		}

		final String source = entry.get(template);

		return source == null	? StringUtils.getSequenceMappedMessage(	template,
																		strings)
								: getMappedMessage(	source,
													strings);
	}

	/**
	 * @param source
	 * @param args
	 * @return
	 */
	public static final String getMappedMessage(final String source,
												final String... args) {
		if (args == null || args.length == 0) {
			return source;
		}

		StringBuffer buffer = null;
		try {
			buffer = ResourceContext.getAppendable();

			StringUtils.transferString(	buffer,
										source,
										args,
										"{",
										"}");

			return buffer.toString();
		}
		catch (IOException e) {
			throw new RuntimeException(	"convert.error",
										e);
		}
		finally {
			ResourceContext.recycleAppendable(buffer);
		}
	}

	/**
	 * @return
	 */
	public static final SequenceMap getSequenceMap() {
		return new ArrayMap();
	}

	/**
	 * @param map
	 */
	public static final void recycleSequenceMap(SequenceMap map) {
		//
	}

	/**
	 * @return
	 */
	public static final List getList() {
		return new ArrayList();
	}

	/**
	 * @param map
	 */
	public static final void recycleList(List list) {
		if (list == null) {
			return;
		}
	}

	/**
	 * @param source
	 * @return
	 */
	public static final ByteArrayInputStream getByteArrayInputStream(final byte[] source) {
		return new ByteArrayInputStream(source);
	}

	/**
	 * @return
	 */
	public static final ByteArrayOutputStream getByteArrayOutputStream() {
		return new ByteArrayOutputStream();
	}

	/**
	 * @param out
	 */
	public static final void recycleByteArrayOutputStream(final ByteArrayOutputStream out) {
		ObjectFinalizer.close(out);
	}

	/**
	 * @return
	 */
	public static final byte[] getStreamCopyBuffer() {
		return new byte[8096];
	}

	/**
	 * @param depth
	 * @return
	 */
	public static final byte[] getByteArray(int depth) {
		return new byte[1 << depth];
	}

	/**
	 * @param array
	 */
	public static final void recycleByteArray(byte[] array) {
		//
	}

	/**
	 * @param depth
	 * @return
	 */
	public static final char[] getCharArray(int depth) {
		return new char[1 << depth];
	}

	/**
	 * @param array
	 */
	public static final void recycleCharArray(char[] array) {
		//
	}

	/**
	 * @param depth
	 * @return
	 */
	public static final String[] getStringArray(int depth) {
		return new String[1 << depth];
	}

	/**
	 * @param array
	 */
	public static final void recycleStringArray(String[] array) {
		//
	}

	/**
	 * @return
	 */
	public static final char[] getReaderCopyBuffer() {
		return new char[1024];
	}

	/**
	 * @param array
	 */
	public static final void recycleArray(char[] array) {
		//
	}

	/**
	 * @return
	 */
	public static StringBuffer getAppendable() {
		return new StringBuffer();
	}

	/**
	 * @param array
	 */
	public static final void recycleAppendable(StringBuffer appendable) {
		//
	}

	/**
	 * @param depth
	 * @return
	 */
	public static final Object[] getObjectArray(int depth) {
		return new Object[1 << depth];
	}

	/**
	 * @param array
	 */
	public static final void recycleObjectArray(Object[] array) {
		//
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public interface ResourceProvider {
		/**
		 * @return
		 */
		public abstract String[] getLocale();

		/**
		 * @param entry
		 */
		public abstract void publish(LocaleEntry entry);
	}

	/**
	 * @param in
	 * @return
	 */
	public static final ResourceProvider getXmlProvider(final InputStream in) throws Exception {
		return new XmlResourceProviderImpl(in);
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	static final class XmlResourceProviderImpl
												implements
												ResourceProvider {
		/**
		 * 
		 */
		final ArrayMap<String, ArrayMap<String, String>> map = new ArrayMap<String, ArrayMap<String, String>>();

		/**
		 * @param in
		 */
		XmlResourceProviderImpl(InputStream in) throws Exception {
			final Document doc = XMLBuilderDelegator.createDocument(in,
																	false);
			final Element elm = XMLUtil.getFirstElementFromDocument(doc);

			this.processPackageElement(elm);

			final Element[] packageElms = XMLUtil.getNamedChildElements(elm,
																		"package");
			final int length = packageElms.length;
			for (int i = 0; i < length; i++) {
				final Element entry = packageElms[i];

				this.processPackageElement(entry);
			}
		}

		/**
		 * @param entry
		 */
		private final void processPackageElement(final Element elm) {
			final Element[] childElms = XMLUtil.getNamedChildElements(	elm,
																		"entry");
			final int length = childElms.length;
			for (int i = 0; i < length; i++) {
				final Element entry = childElms[i];

				this.processResourceElement(entry);
			}
		}

		/**
		 * @param entry
		 */
		private final void processResourceElement(final Element entry) {
			final String key = XMLUtil.getAttribute(entry,
													"key");
			final Element[] resourceList = XMLUtil.getNamedChildElements(	entry,
																			"resource");
			final int size = resourceList.length;

			for (int i = 0; i < size; i++) {
				final Element resource = resourceList[i];
				final String locale = XMLUtil.getAttribute(	resource,
															"locale");
				final String value = XMLUtil.getStringValue(resource);

				ArrayMap<String, String> tmp = this.map.get(locale);

				if (tmp == null) {
					tmp = new ArrayMap<String, String>();
					this.map.put(	locale,
									tmp);
				}

				tmp.put(key,
						value);
			}
		}

		/**
		 * @see com.kskyb.broker.lang.ResourceContext.ResourceProvider#getLocale()
		 */
		public final String[] getLocale() {
			return this.map.getKeyList().toArray(new String[this.map.size()]);
		}

		/**
		 * @see com.kskyb.broker.lang.ResourceContext.ResourceProvider#publish(com.kskyb.broker.lang.ResourceContext.LocaleEntry)
		 */
		public final void publish(final LocaleEntry entry) {
			final String name = entry.getLocale();
			final ArrayMap<String, String> tmpMap = this.map.get(name);
			final int length = tmpMap.size();

			for (int i = 0; i < length; i++) {
				final String key = tmpMap.getKeyByIndex(i);
				final String value = tmpMap.getValueByIndex(i);
				entry.regist(	key,
								value);
			}
		}

	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public static final class LocaleEntry {
		/**
		 * 
		 */
		final String locale;

		/**
		 * @param locale
		 */
		public LocaleEntry(String locale) {
			this.locale = locale;
		}

		/**
		 * @return the locale
		 */
		public String getLocale() {
			return this.locale;
		}

		/**
		 * 
		 */
		final HashMap<String, String> hash = new HashMap<String, String>();

		/**
		 * @param key
		 * @param value
		 */
		public final void regist(	final String key,
									final String value) {
			final String convert = StringDigestDelegator.getLineDigestedString(CommonTemplateUtil.getConvertString(	value,
																													this.hash,
																													"${",
																													"}",
																													true));
			this.hash.put(	key,
							convert);
		}

		/**
		 * @param key
		 * @return
		 */
		public final String get(final String key) {
			return this.hash.get(key);
		}
	}

}
