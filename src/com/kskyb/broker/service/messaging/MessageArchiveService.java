package com.kskyb.broker.service.messaging;

import com.kskyb.broker.kernel.AbstractMasterKernelService;
import com.kskyb.broker.kernel.KernelService;
import com.kskyb.broker.lang.ResourceContext;

/**
 * <AUTHOR>
 * @deprecated
 * 
 */
@Deprecated
public final class MessageArchiveService
										extends
										AbstractMasterKernelService {
	/**
	 * 
	 */
	public static final String SERVICE_NAME = "archive";

	/**
	 * 
	 */
	private static final Class<? extends KernelService>[] SERVICE_CHILD_TYPES = new Class[] {
		MessageArchiveChildService.class
	};

	/**
	 * 
	 */
	private static final MessageArchiveService service = new MessageArchiveService();

	/**
	 * @return the service
	 */
	public static final MessageArchiveService getService() {
		return service;
	}

	/**
	 * 
	 */
	private MessageArchiveService() {
		super(	null,
				SERVICE_NAME,
				ResourceContext.getBundledMessage("message.archive.master.service.desc"),
				SERVICE_CHILD_TYPES);
	}
}
