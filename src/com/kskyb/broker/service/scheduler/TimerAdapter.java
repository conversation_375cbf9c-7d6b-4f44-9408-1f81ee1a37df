package com.kskyb.broker.service.scheduler;

import java.util.Calendar;
import java.util.Map;

import com.kskyb.broker.common.digest.DigestUtil;
import com.kskyb.broker.io.SerializeCodecs;
import com.kskyb.broker.lang.ErrorCode;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.util.ArrayMap;
import com.kskyb.broker.util.ISelector;
import com.kskyb.broker.util.StringUtils;


/**
 * <AUTHOR>
 * 
 */
public abstract class TimerAdapter
									implements
									ITimer {
	/**
	 * 
	 */
	protected static final Calendar calendar = Calendar.getInstance();

	/**
	 * 
	 */
	public static final int CRON_TYPE_PERIOD = 0;

	/**
	 * 
	 */
	public static final int CRON_TYPE_INTERVAL = 1;

	/**
	 * 
	 */
	private ArrayMap<String, Object> attr = ArrayMap.getInstance();

	/**
	 * @return the attr
	 */
	public final Map<String, Object> getAttr() {
		return this.attr;
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#getAttribute(java.lang.String)
	 */
	public final Object getAttribute(final String key) {
		return this.attr.get(key);
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#setAttribute(java.lang.String, java.lang.Object)
	 */
	public final void setAttribute(	final String key,
									final Object value) {
		this.attr.put(	key,
						value);
	}

	/**
	 * @param selector
	 * @return
	 */
	public static final ITimer createTimerBySelector(final ISelector selector) {
		final String sCronType = selector.getParameter("CRON_TYPE");
		final int iCronType = Integer.parseInt(sCronType);
		ITimer timer = null;
		switch (iCronType) {
			case CRON_TYPE_PERIOD: {
				timer = createCronTimer(selector);
				break;
			}

			case CRON_TYPE_INTERVAL: {
				timer = createIntervalTimer(selector);
				break;
			}

			default: {
				break;
			}
		}

		if (timer == null) {
			ExceptionGW.filterMessage(	ErrorCode.CODE_DATA_CONSTRAINT_COMMON,
										ResourceContext.getBundledMessage(	"unregist.cron.type",
																			sCronType));
			return null;
		}

		SerializeCodecs.decodeMap(	timer.getAttr(),
									selector.getParameter("CRON_PROP"));

		return timer;
	}

	/**
	 * @param selector
	 * @return
	 */
	private static final ITimer createCronTimer(final ISelector selector) {
		final String sCronMonth = selector.getParameter("CRON_MONTH");
		final String sCronWeek = selector.getParameter("CRON_WEEK");
		final String sCronDay = selector.getParameter("CRON_DAY");
		final String sCronHour = selector.getParameter("CRON_HOUR");
		final String sCronMin = selector.getParameter("CRON_MIN");

		final PeriodTimer timer = new PeriodTimer();

		DigestUtil.executeInitializeMethods(timer);

		timer.setMonthStr(sCronMonth);
		timer.setWeekStr(sCronWeek);
		timer.setDayStr(sCronDay);
		timer.setHourStr(sCronHour);
		timer.setMinuteStr(sCronMin);

		DigestUtil.executeValidationMethods(timer);

		return timer;
	}

	/**
	 * @param selector
	 * @return
	 */
	private static final ITimer createIntervalTimer(final ISelector selector) {

		final HeartBeatTimer timer = new HeartBeatTimer();

		DigestUtil.executeInitializeMethods(timer);

		try {
			final String sCronSec = StringUtils.trim(selector.getParameter("CRON_SEC"));

			if (sCronSec.length() < 1 || sCronSec.equals("*")) {
				timer.setPeriod(1000);
			}
			else {
				final long lCronSec = Long.parseLong(sCronSec) * 1000;
				timer.setPeriod(lCronSec);
			}

		}
		catch (Throwable thw) {
			timer.setPeriod(10000L);
		}
		DigestUtil.executeValidationMethods(timer);

		return timer;
	}

	/**
	 * 
	 */
	byte flag = 0;

	/**
	 * 
	 */
	protected long currentTime = 0;

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#commit(long)
	 */
	public void commit(long current) {
		this.currentTime = current;
		this.offBitwize(SUSPEND_FLAG);
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#getCurrentTime()
	 */
	public long getCurrentTime() {
		return this.currentTime;
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#getType()
	 */
	public final short getType() {
		return this.isMarked(PATTERN_FLAG)	? DATETIME_PATTERN
											: PERIOD_PATTERN;
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#isSuspend()
	 */
	public boolean isSuspend() {
		return this.isMarked(SUSPEND_FLAG);
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#resume()
	 */
	public void resume() {
		this.offBitwize(SUSPEND_FLAG);

		synchronized (this) {
			this.notifyAll();
		}
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#suspend()
	 */
	public void suspend() {
		this.onBitwize(SUSPEND_FLAG);

		synchronized (this) {
			this.notifyAll();
		}
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#stop()
	 */
	public final void stop() {
		this.onBitwize(STOPED_FLAG);

		synchronized (this) {
			this.notifyAll();
		}
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#isStopped(long)
	 */
	public boolean isStopped(long current) {
		return this.isMarked(STOPED_FLAG);
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#activate()
	 */
	public void activate() {
		if (this.isMarked(EXECUTE_FLAG)) {
			throw new RuntimeException(ResourceContext.getBundledMessage("aready.activated.timer"));
		}
		this.onBitwize(EXECUTE_FLAG);
		synchronized (this) {
			this.notifyAll();
		}

	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#deActivate()
	 */
	public void deActivate() {
		if (!this.isMarked(EXECUTE_FLAG)) {
			throw new RuntimeException(ResourceContext.getBundledMessage("not.activated.timer"));
		}
		this.offBitwize(EXECUTE_FLAG);
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITimer#isActivated()
	 */
	public boolean isActivated() {
		return this.isMarked(EXECUTE_FLAG);
	}

	/**
	 * @param source
	 */
	protected void onBitwize(byte source) {
		this.flag |= source;
	}

	/**
	 * @param source
	 */
	protected void offBitwize(byte source) {
		this.flag &= (source ^ ~0);
	}

	/**
	 * @param source
	 * @return
	 */
	protected boolean isMarked(byte source) {
		return (this.flag & source) > 0;
	}
}
