package com.kskyb.broker.service.scheduler.monitoring.command;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import com.kskyb.broker.io.SerializeCodecs;
import com.kskyb.broker.service.scheduler.monitoring.ScheduleCommandResult;


/**
 * <AUTHOR>
 * 
 */
public final class StatusMonitorResult
										extends
										ScheduleCommandResult {
	/**
	 * @see com.kskyb.broker.service.scheduler.monitoring.ScheduleCommandResult#_deserialize(java.io.InputStream)
	 */
	@Override
	protected final void _deserialize(final InputStream in) throws IOException {
		this.statusDesc = SerializeCodecs.decodeString(in);
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.monitoring.ScheduleCommandResult#_serialize(java.io.OutputStream)
	 */
	@Override
	protected void _serialize(OutputStream out) throws IOException {
		SerializeCodecs.encodeString(	this.statusDesc,
										out);
	}

	/**
	 * 
	 */
	private String statusDesc = null;

	/**
	 * @return the statusDesc
	 */
	public final String getStatusDesc() {
		return this.statusDesc;
	}

	/**
	 * @param statusDesc
	 *            the statusDesc to set
	 */
	public final void setStatusDesc(String statusDesc) {
		this.statusDesc = statusDesc;
	}

}
