package com.kskyb.broker.service.scheduler;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.async.AsyncExecutorPool;
import com.kskyb.broker.async.ExecutorPool;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.kernel.KernelService;
import com.kskyb.broker.kernel.KernelServiceImpl;
import com.kskyb.broker.kernel.serivce.AsyncExecService;
import com.kskyb.broker.lang.ErrorCode;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.Reflector;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.util.ControledThread;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.broker.util.IdentityStruct;

/**
 * <AUTHOR>
 * 
 */
public abstract class ScheduleContainerService
												extends
												KernelServiceImpl {
	/**
	 * 
	 */
	protected final IdentityStruct<ITask> struct = new IdentityStruct<ITask>();

	/**
	 * 
	 */
	protected final Object lock = new Object();

	/**
	 * 
	 */
	private final ControledThread taskWatcher;

	/**
	 * 
	 */
	protected ScheduleContainerService() {
		this(	false,
				null,
				null,
				null,
				null);
	}

	/**
	 * @param permanent
	 * @param name
	 * @param desc
	 * @param word
	 * @param note
	 */
	protected ScheduleContainerService(	final boolean permanent,
										final String name,
										final String desc,
										final String word,
										final String note) {
		super(	permanent,
				name,
				desc,
				word,
				note);
		this.taskWatcher = new ControledThread(true) {
			/**
			 * @see com.kskyb.broker.util.ControledThread#doStart()
			 */
			@Override
			protected void doStart() {
				//
			}

			/**
			 * @see com.kskyb.broker.util.ControledThread#doStop()
			 */
			@Override
			protected void doStop() {
				//
			}

			/**
			 * @see com.kskyb.broker.util.ControledThread#execute()
			 */
			@Override
			protected void execute() throws Throwable {
				final String name = this.getName();
				try {
					this.setName(ResourceContext.getBundledMessage(	"runtime.thread.name",
																	name,
																	DateUtil.getCommonTime()));
					ScheduleContainerService.this.monitor();
				}
				finally {
					this.setName(name);
				}
			}
		};
		this.taskWatcher.setDeamon(true);
		this.taskWatcher.setTransaction(false);
	}

	// -------------------------------------------- Service relate Methods

	/**
	 * @see com.kskyb.broker.kernel.KernelServiceImpl#doStart()
	 */
	@Override
	protected void doStart() throws Throwable {
		this.taskWatcher.setName("TaskWatcher:" + this.getServiceName());

		/**
		 * 서비스 로딩중이라면 여타 서비스가 모두 오픈된 다음에 open bind 할수 있도록 async로 시작시킨다.
		 */
		Runnable asyncRunnable = new Runnable() {
			/**
			 * @see java.lang.Runnable#run()
			 */
			@Override
			public void run() {
				ScheduleContainerService.this.taskWatcher.start();
			}
		};

		AsyncExecService.getService().execAsync(asyncRunnable);

		super.doStart();
	}

	/**
	 * @see com.kskyb.broker.kernel.KernelServiceImpl#doStop()
	 */
	@Override
	protected void doStop() throws Throwable {
		this.taskWatcher.stop();

		synchronized (this.lock) {
			this.lock.notifyAll();
		}

		super.doStop();
	}

	// ------------------------------------- protected Task Management Methods
	/**
	 * @return
	 */
	protected boolean isDefaultService() {
		return false;
	}

	/**
	 * @param task
	 */
	public abstract void regist(ITask task);

	/**
	 * @param task
	 */
	public abstract void remove(String taskId);

	/**
	 * @param id
	 * @return
	 */
	public final ITask searchTask(final String id) {
		synchronized (this.lock) {
			return this.struct.search(id);
		}
	}

	/**
	 * @param task
	 */
	protected final void registTask(ITask task) {
		synchronized (this.lock) {
			this.struct.regist(task);
			this.lock.notifyAll();
		}
	}

	/**
	 * @param id
	 */
	protected final void removeTask(String id) {
		ITask task = null;

		synchronized (this.lock) {
			task = this.struct.remove(id);
			if (task == null) {
				return;
			}
		}
		if (task.getId().equals(id)) {
			task.destroy();
			return;
		}

		ExceptionGW.filterMessage(	ErrorCode.CODE_DATA_CONSTRAINT_COMMON,
									ResourceContext.getBundledMessage(	"cannot.find.target.task",
																		task.getId(),
																		id));
	}

	/**
	 * @return
	 */
	public final ITask[] getTaskList() {
		return this.struct.getList().toArray(BLANK_TASK_ARRAY);
	}

	// ---------------------------------------------- private management Methods
	private static final ITask[] BLANK_TASK_ARRAY = new ITask[0];

	/**
	 * @param _current
	 * @throws Exception
	 */
	private void check(final long _current) throws Exception {
		final long current = _current < 0	? DateUtil.currentMinite(System.currentTimeMillis())
											: _current;

		if (DebugEchoEvent.isEnable()) {
			DebugEchoEvent.fire("monitor.check.start");
		}

		final ITask[] tasks = this.struct.getList().toArray(BLANK_TASK_ARRAY);
		final int length = tasks.length;
		for (int i = 0; i < length; i++) {
			try {
				final ITask task = tasks[i];

				if (this.executeCheck(	task,
										current)) {
					CommonLogGW.info(ResourceContext.getBundledMessage(	"monitor.check.task.end.reject",
																		task.getDesc()));
					this.removeTask(task.getId());
				}
			}
			catch (Throwable thw) {
				CommonLogGW.error(	"task.monitor.error",
									thw);
			}
		}
	}

	/**
	 * @param task
	 * @param current
	 * @return
	 */
	private final boolean executeCheck(	final ITask task,
										final long current) {
		if (task.isAssignTask()) {
			return false;
		}

		ITimer timer = task.getTimer();
		if (task.isFinishTask() || timer.isStopped(current) || timer.isFinish(current)) {
			return true;
		}

		if (timer.isSuspend()) {
			if (DebugEchoEvent.isEnable()) {
				DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"suspended.task.skip",
																		task.getDesc()));
			}
		}
		else if (timer.isActivated()) {
			if (DebugEchoEvent.isEnable()) {
				DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"working.task.skip",
																		task.getDesc()));
			}
		}
		else if (timer.isOnTime(current)) {
			// -- invoke 되었음을 마킹해야함.
			// worker.execute(task);
			task.assignInvoke();
			final int status = this.executorPool.dispatch(task);
			switch (status) {
				case AsyncExecutorPool.DISPATCH_SUCCESS_IN_NEW:
				case AsyncExecutorPool.DISPATCH_SUCCESS_IN_QUEUE:
				case AsyncExecutorPool.DISPATCH_SUCCESS_IN_RECYCLE: {
					timer.commit(current);
					CommonLogGW.info(ResourceContext.getBundledMessage(	"schedule.task.dispatch.success",
																		String.valueOf(task)));
					break;
				}

				default: {
					task.releaseInvoke();
					CommonLogGW.info(ResourceContext.getBundledMessage(	"schedule.task.dispatch.fail",
																		String.valueOf(task),
																		String.valueOf(status)));
					break;
				}
			}

		}

		return false;
	}

	/**
	 * 
	 */
	private final void monitor() {
		long current = System.currentTimeMillis();
		long next = -1;
		while (ScheduleContainerService.this.getStatus() == KernelService.STATUS_START) {
			current = System.currentTimeMillis();
			long curmin = DateUtil.currentMinite(current);
			if (DebugEchoEvent.isEnable()) {
				DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"time.check",
																		DateUtil.getFormatDate(	DateUtil.full_time,
																								current),
																		DateUtil.getFormatDate(	DateUtil.full_time,
																								curmin)));
			}

			try {
				this.check(curmin);
			}
			catch (Throwable e) {
				CommonLogGW.error(	"task.monitor.error",
									e);
			}

			// 다음 다음 1초가 되는 유효시간을 계산한다.
			current = System.currentTimeMillis();
			next = DateUtil.nextMinite(current);

			if (DebugEchoEvent.isEnable()) {
				DebugEchoEvent.fire(ResourceContext.getBundledMessage(	"sleep.time.check",
																		DateUtil.getFormatDate(	DateUtil.full_time,
																								current),
																		DateUtil.getFormatDate(	DateUtil.full_time,
																								next),
																		String.valueOf(next - current)));
			}

			// -- common wait -- //
			Reflector.delay(this.lock,
							next - current);
		}
	}

	/**
	 * 
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validateScheduleContainerService() {
		this.executorPool.setName(this.getServiceName());
		this.executorPool.open();
		ScheduleMasterService.getService().registChildService(this);
	}

	/**
	 * 
	 */
	@MemberField(elementName = "executor-pool", nullable = false)
	ExecutorPool executorPool = null;
}
