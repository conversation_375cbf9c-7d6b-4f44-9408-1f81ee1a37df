package com.kskyb.broker.service.scheduler;

import java.util.Map;

/**
 * <AUTHOR>
 * 
 */
public interface ITimer {
	/**
	 * 
	 */
	public static final byte PATTERN_FLAG = 1 << 0;

	/**
	 * 
	 */
	public static final byte EXECUTE_FLAG = 1 << 1;

	/**
	 * 
	 */
	public static final byte SUSPEND_FLAG = 1 << 2;

	/**
	 * 
	 */
	public static final byte STOPED_FLAG = 1 << 3;

	/**
	 * 
	 */
	public static final short PERIOD_PATTERN = 0;

	/**
	 * 
	 */
	public static final short DATETIME_PATTERN = 1;

	/**
	 * 
	 */
	public static final short ONETIME_PATTERN = 2;

	/**
	 * 타이머가 실행된 마지막 시간을 체크한다.<br>
	 * 동일한 시간에 두번 체크되는것을 방지한다.
	 * 
	 * @param current
	 *            스케쥴러 시간
	 */
	public abstract void commit(long current);

	/**
	 * 
	 * @return
	 */
	public abstract long getCurrentTime();

	/**
	 * 실행시간여부를 반환한다.
	 * 
	 * @return
	 */
	public abstract boolean isOnTime(long current);

	/**
	 * 더이상 실행되지 않을 경우 true를 반환한다.
	 * 
	 * @return
	 */
	public abstract boolean isFinish(long current);

	/**
	 * @return the end
	 */
	public abstract long getEndTime();

	/**
	 * @return the start
	 */
	public abstract long getStartTime();

	/**
	 * 
	 * @return
	 */
	public abstract short getType();

	/**
	 * return value of next execute time gab
	 * 
	 * @return
	 */
	public abstract long getNextTime();

	/**
	 * 
	 */
	public abstract void stop();

	/**
	 * 
	 */
	public abstract void suspend();

	/**
	 * 
	 */
	public abstract void resume();

	/**
	 * 
	 * @return
	 */
	public abstract boolean isSuspend();

	/**
	 * 
	 */
	public abstract void activate();

	/**
	 * 
	 */
	public abstract void deActivate();

	/**
	 * 
	 * @return
	 */
	public abstract boolean isActivated();

	/**
	 * 
	 * @param current
	 * @return
	 */
	public abstract boolean isStopped(long current);

	/**
	 * @return
	 */
	public abstract Map<String, Object> getAttr();

	/**
	 * @param key
	 * @return
	 */
	public abstract Object getAttribute(String key);

	/**
	 * @param key
	 * @param value
	 */
	public abstract void setAttribute(	String key,
										Object value);

}