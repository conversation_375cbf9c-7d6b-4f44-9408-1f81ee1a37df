package com.kskyb.broker.service.scheduler;

/**
 * <AUTHOR>
 * 
 */
public abstract class AbstractOneTimeTask
											extends
											AbstractTask {

	/**
	 * 
	 */
	protected static final ITimer timer = new OneTimeTimer();

	/**
	 * @see com.kskyb.broker.service.scheduler.ITask#startTask()
	 */
	public void startTask() {
		//
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITask#endTask()
	 */
	public void endTask() {
		// taskManager.finishOneTimeTask(this);
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.AbstractTask#ready()
	 */
	@Override
	protected boolean ready() {
		return true;
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITask#isFinishTask()
	 */
	public boolean isFinishTask() {
		return true;
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITask#getTimer()
	 */
	public ITimer getTimer() {
		return timer;
	}

	/**
	 * @see com.kskyb.broker.service.scheduler.ITask#setTimer(com.kskyb.broker.service.scheduler.ITimer)
	 */
	public void setTimer(ITimer timer) {
		throw new RuntimeException("one time task is not assignable for timer instance");
	}

}
