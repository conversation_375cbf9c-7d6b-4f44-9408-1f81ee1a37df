package com.kskyb.broker.service.jmx;

import java.io.Closeable;
import java.lang.management.ThreadMXBean;

import javax.management.MBeanServerConnection;

import com.kskyb.broker.lang.Identity;


/**
 * <AUTHOR>
 * 
 */
public interface JMXConnectionEntry
									extends
									Identity,
									Closeable {
	/**
	 * @return
	 */
	public abstract MBeanServerConnection getServerConnection() throws Exception;

	/**
	 * @return
	 * @throws Throwable
	 */
	public abstract ThreadMXBean getThreadMXBean() throws Throwable;

	/**
	 * @throws Throwable
	 */
	public abstract void validateConnection() throws Throwable;

	/**
	 * @return
	 */
	public abstract String description();

	/**
	 * 
	 */
	public abstract void clearConnection();
}
