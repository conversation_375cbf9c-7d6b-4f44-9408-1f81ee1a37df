package com.kskyb.broker.service.net.filter.impl;

import java.io.InputStream;
import java.io.OutputStream;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.io.BufferedRandomAccessStream;
import com.kskyb.broker.io.IOUtil;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.service.net.core.CommunicationBuffer;
import com.kskyb.broker.service.net.core.IoSession;
import com.kskyb.broker.service.net.filter.AbstractSerialIoFilter;
import com.kskyb.broker.util.BitsUtil;


/**
 * PDA(C++)에서 블록 압축 필터 1.0
 * 
 * <AUTHOR>
 * 
 */
public final class CompressIoFilterV1
										extends
										AbstractSerialIoFilter {
	/**
	 * 
	 */
	private static final int DEFAULT_SIZE_ASSIGN_BLOCK_SIZE = 8;

	/**
	 * 
	 */
	private static final int DEFAULT_MAXIMUM_SIZE = 1024 * 1024;

	/**
	 * 
	 */
	int blockArrayDepth = -1;

	/**
	 * @see com.kskyb.broker.service.net.filter.IoFilter#copyIncomingContents(java.io.InputStream, java.io.OutputStream, int)
	 */
	public final void copyIncomingContents(	final InputStream in,
											final OutputStream out,
											final int length) throws Exception {
		if (length > this.maximumSize) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"maximum.stream.length.exceed",
																			String.valueOf(length),
																			String.valueOf(this.maximumSize)));
		}
		GzipCompressorV1.decompressBlockStream(	in,
												out,
												length);
	}

	/**
	 * @see com.kskyb.broker.service.net.filter.IoFilter#copyOutgoingContents(java.io.InputStream, java.io.OutputStream, int)
	 */
	public final void copyOutgoingContents(	final InputStream in,
											final OutputStream out,
											final int length) throws Exception {
		if (length > this.maximumSize) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"maximum.stream.length.exceed",
																			String.valueOf(length),
																			String.valueOf(this.maximumSize)));
		}
		BufferedRandomAccessStream stream = null;
		InputStream copy = null;
		try {
			stream = BufferedRandomAccessStream.getInstance(12);
			stream.reset();

			GzipCompressorV1.compressBlockStream(	in,
													stream,
													this.compressLevel,
													length,
													this.blockSize);

			copy = stream.getInputStream();
			IOUtil.copyStream(	copy,
								out);
		}
		finally {
			ObjectFinalizer.close(copy);
			ObjectFinalizer.close(stream);
		}
	}

	/**
	 * @see com.kskyb.broker.service.net.filter.AbstractSerialIoFilter#getRequireSize(com.kskyb.broker.service.net.core.IoSession,
	 *      com.kskyb.broker.io.BufferedRandomAccessStream)
	 */
	@Override
	protected final int getRequireSize(	final IoSession session,
										final BufferedRandomAccessStream stream) throws Exception {
		final int size = this.peekHeadSize(	session,
											stream);
		return size > 0	? size + DEFAULT_SIZE_ASSIGN_BLOCK_SIZE
						: -1;
	}

	/**
	 * @see com.kskyb.broker.service.net.filter.AbstractSerialIoFilter#getRequireSize(java.io.InputStream, byte[],
	 *      com.kskyb.broker.service.net.core.CommunicationBuffer)
	 */
	@Override
	protected final int getRequireSize(	InputStream in,
										byte[] buffer,
										CommunicationBuffer array) throws Exception {
		final int size = getRequireSizeHeader4Bytes(in,
													buffer,
													array);
		return size + DEFAULT_SIZE_ASSIGN_BLOCK_SIZE - 4;
	}

	// -- implements filter -- //
	/**
	 * 
	 */
	@ValidationMethod
	public final void validationCompressIoFilter() {
		if (this.compressLevel < 1 || this.compressLevel > 9) {
			throw new RuntimeException("invalid compress level:" + String.valueOf(this.compressLevel));
		}

		this.blockArrayDepth = BitsUtil.getDepth(this.blockSize);
	}

	/**
	 * 
	 */
	@MemberField
	int maximumSize = DEFAULT_MAXIMUM_SIZE; // default 1MB

	/**
	 * @return the maximumSize
	 */
	public final int getMaximumSize() {
		return this.maximumSize;
	}

	/**
	 * @param maximumSize
	 *            the maximumSize to set
	 */
	public final void setMaximumSize(int maximumSize) {
		this.maximumSize = maximumSize;
	}

	/**
	 * 
	 */
	@MemberField
	private int blockSize = GzipCompressorV1.DEFAULT_BLOCK_SIZE; // default 56K

	/**
	 * 
	 */
	@MemberField
	private int compressLevel = GzipCompressorV1.DEFAULT_COMPRESS_LEVEL;
}
