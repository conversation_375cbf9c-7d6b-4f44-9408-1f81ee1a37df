package com.kskyb.broker.service.net.filter.impl;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.io.BufferedRandomAccessStream;
import com.kskyb.broker.io.IOUtil;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.service.net.core.CommunicationBuffer;
import com.kskyb.broker.service.net.core.IoSession;
import com.kskyb.broker.service.net.filter.IoFilterChainAdapter;
import com.kskyb.broker.util.DateUtil;


/**
 * <AUTHOR>
 * 
 */
public final class SessionDumpFilterChain
											extends
											IoFilterChainAdapter {
	/**
	 * 
	 */
	File base = null;

	/**
	 * 
	 */
	SimpleDateFormat format = null;

	/**
	 * 
	 */
	public SessionDumpFilterChain() {
		super();
	}

	/**
	 * @see com.kskyb.broker.service.net.filter.IoFilterChainAdapter#fireIncomming(com.kskyb.broker.service.net.core.IoSession,
	 *      com.kskyb.broker.service.net.core.CommunicationBuffer)
	 */
	@Override
	public final void fireIncomming(final IoSession session,
									final CommunicationBuffer array) throws Exception {
		File target = null;
		InputStream in = null;
		OutputStream out = null;
		long currentReadPoint = -1;
		final BufferedRandomAccessStream stream = array.getStream();

		// -- berore dump -- //
		try {

			target = IOUtil.getPathValidateFile(this.base,
												DateUtil.getFormatDate(this.format)
													+ ".in.00."
													+ String.valueOf(session.hashCode()));
			out = new FileOutputStream(target);

			currentReadPoint = stream.getCurrentReadPoint();

			in = stream.getInputStream();

			IOUtil.copyStream(	in,
								out);
		}
		finally {
			ObjectFinalizer.close(out);
			ObjectFinalizer.close(in);
			if (currentReadPoint >= 0) {
				stream.setReadPoint(currentReadPoint);
			}
		}

		super.fireIncomming(session,
							array);

		if (array.getRemain() == 0) {
			return;
		}

		// -- result dump -- //
		try {

			target = IOUtil.getPathValidateFile(this.base,
												DateUtil.getFormatDate(this.format)
													+ ".in.99."
													+ String.valueOf(session.hashCode()));
			out = new FileOutputStream(target);

			currentReadPoint = stream.getCurrentReadPoint();

			in = stream.getInputStream();

			IOUtil.copyStream(	in,
								out);
		}
		finally {
			ObjectFinalizer.close(out);
			ObjectFinalizer.close(in);
			if (currentReadPoint >= 0) {
				stream.setReadPoint(currentReadPoint);
			}
		}
	}

	/**
	 * @see com.kskyb.broker.service.net.filter.IoFilterChainAdapter#fireOutgoing(com.kskyb.broker.service.net.core.IoSession,
	 *      com.kskyb.broker.service.net.core.CommunicationBuffer, boolean)
	 */
	@Override
	public final void fireOutgoing(	final IoSession session,
									final CommunicationBuffer array,
									final boolean flush) throws Exception {
		if (flush) {
			File target = null;
			InputStream in = null;
			OutputStream out = null;
			long currentReadPoint = -1;
			final BufferedRandomAccessStream stream = array.getStream();
			try {

				target = IOUtil.getPathValidateFile(this.base,
													DateUtil.getFormatDate(this.format)
														+ ".out.00."
														+ String.valueOf(session.hashCode()));
				out = new FileOutputStream(target);

				currentReadPoint = stream.getCurrentReadPoint();

				in = stream.getInputStream();

				IOUtil.copyStream(	in,
									out);
			}
			finally {
				ObjectFinalizer.close(out);
				ObjectFinalizer.close(in);
				if (currentReadPoint >= 0) {
					stream.setReadPoint(currentReadPoint);
				}
			}
		}

		super.fireOutgoing(	session,
							array,
							flush);
		if (flush) {
			File target = null;
			InputStream in = null;
			OutputStream out = null;
			long currentReadPoint = -1;
			final BufferedRandomAccessStream stream = array.getStream();
			try {

				target = IOUtil.getPathValidateFile(this.base,
													DateUtil.getFormatDate(this.format)
														+ ".out.99."
														+ String.valueOf(session.hashCode()));
				out = new FileOutputStream(target);

				currentReadPoint = stream.getCurrentReadPoint();

				in = stream.getInputStream();

				IOUtil.copyStream(	in,
									out);
			}
			finally {
				ObjectFinalizer.close(out);
				ObjectFinalizer.close(in);
				if (currentReadPoint >= 0) {
					stream.setReadPoint(currentReadPoint);
				}
			}
		}
	}

	/**
	 * 
	 */
	@ValidationMethod
	public final void validateSessionDumpFilterChain() {
		this.base = IOUtil.getMustDirectory(this.dumpBaseDirectory);
		this.format = new SimpleDateFormat(this.dumpFilePrefixFormat);
	}

	/**
	 * 
	 */
	@MemberField(elementName = "dump-base-directory", nullable = false)
	String dumpBaseDirectory = null;

	/**
	 * 
	 */
	@MemberField(elementName = "dump-file-prefix-format", nullable = false)
	String dumpFilePrefixFormat = null;
}
