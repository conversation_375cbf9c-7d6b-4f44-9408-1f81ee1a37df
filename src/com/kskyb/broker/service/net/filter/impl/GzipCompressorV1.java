package com.kskyb.broker.service.net.filter.impl;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

import com.kskyb.broker.io.BufferedRandomAccessStream;
import com.kskyb.broker.io.BufferedRandomAccessStreamUtil;
import com.kskyb.broker.io.SerializeCodecs;
import com.kskyb.broker.lang.CloseAdapter;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.util.ArrayContainer;
import com.kskyb.broker.util.BitsUtil;


/**
 * <AUTHOR>
 * 
 */
public final class GzipCompressorV1
									extends
									CloseAdapter {
	/**
	 * 
	 */
	private static final boolean INNER_DEBUG = /* for debug start */false/* for debug end */;

	/**
	 * 
	 */
	public static final int DEFAULT_BLOCK_SIZE = (56 * 1024);

	/**
	 * 
	 */
	public static final int DEFAULT_SIZE_ASSIGN_BLOCK_SIZE = 8;

	/**
	 * 
	 */
	public static final int DEFAULT_COMPRESS_LEVEL = Deflater.BEST_COMPRESSION;

	/**
	 * FIXME : remove
	 * 
	 * @return
	 */
	public static final GzipCompressorV1 getInstance() {
		return new GzipCompressorV1();
	}

	/**
	 * @param stream
	 * @return
	 */
	public static final boolean isAbleToDecompress(final BufferedRandomAccessStream stream) {
		try {
			final int totalCompressedSize = BufferedRandomAccessStreamUtil.getInteger(	stream,
																						stream.getCurrentReadPoint());
			if (stream.getRemain() == (totalCompressedSize + DEFAULT_SIZE_ASSIGN_BLOCK_SIZE)) {
				return true;
			}
		}
		catch (Throwable thw) {
			//
		}
		return false;
	}

	/**
	 * 
	 * @param source
	 * @param model
	 * @param length
	 * @throws Exception
	 */
	public static final void decompressBlockStream(	final InputStream in,
													final OutputStream out,
													final long length) throws Exception {
		int processPoint = 0;

		final int totalCompressedSize = SerializeCodecs.decodeInteger(in);

		if (length > 0 && length != (totalCompressedSize + DEFAULT_SIZE_ASSIGN_BLOCK_SIZE)) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"different.total.size",
																			String.valueOf(length),
																			String.valueOf(totalCompressedSize)));
		}

		int totalContentsSize = SerializeCodecs.decodeInteger(in);

		int remain = totalCompressedSize;

		while (remain > 0) {
			if (INNER_DEBUG) {
				in.mark(0);
			}
			int compressSize = SerializeCodecs.decodeInteger(in);
			int originalSize = SerializeCodecs.decodeInteger(in);

			remain -= (compressSize + DEFAULT_SIZE_ASSIGN_BLOCK_SIZE);
			processPoint += originalSize;

			copyStream(	in,
						out,
						false,
						0,
						compressSize,
						originalSize);

			if (INNER_DEBUG) {
				CommonLogGW.debug(ResourceContext.getBundledMessage("in-block-complete",
																	String.valueOf(processPoint),
																	String.valueOf(compressSize - 4),
																	String.valueOf(originalSize)));
			}
		}
		if (INNER_DEBUG) {
			CommonLogGW.debug(ResourceContext.getBundledMessage("in-finish",
																String.valueOf(processPoint)));
		}

		if (totalContentsSize != processPoint) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"different.size",
																			String.valueOf(processPoint),
																			String.valueOf(totalContentsSize)));
		}
	}

	/**
	 * 
	 * @param source
	 * @param model
	 * @param level
	 * @param length
	 * @throws Exception
	 */
	public static final void compressBlockStream(	final InputStream in,
													final BufferedRandomAccessStream stream,
													final int compressLevel,
													final int length,
													final int blockSize) throws Exception {
		OutputStream out = null;
		int remain = length;
		long writeStartPoint = -1;
		long writeEndPoint = -1;

		byte[] number = null;
		try {
			out = stream.getOutputStream();

			number = ArrayContainer.getByteArray(2);
			// write dummy for total compress size
			SerializeCodecs.encodeInteger(	0,
											out);

			// write total uncompress size
			SerializeCodecs.encodeInteger(	length,
											out);

			if (INNER_DEBUG) {
				CommonLogGW.debug(ResourceContext.getBundledMessage("compress.source.length",
																	String.valueOf(length)));
			}

			while (remain > 0) {
				int originalSize = Math.min(remain,
											blockSize);

				writeStartPoint = stream.getCurrentWritePoint();

				// -- write dummy for compress size -- //
				SerializeCodecs.encodeInteger(	0,
												out);
				// -- write original size -- //
				SerializeCodecs.encodeInteger(	originalSize,
												out);

				copyStream(	in,
							out,
							true,
							compressLevel,
							originalSize,
							-1);

				writeEndPoint = stream.getCurrentWritePoint();

				int compressSize = (int) ((writeEndPoint - writeStartPoint) & 0xFFFFFFFF) - DEFAULT_SIZE_ASSIGN_BLOCK_SIZE;

				BitsUtil.putInt(number,
								0,
								compressSize);

				BufferedRandomAccessStreamUtil.writeRandomPosition(	stream,
																	writeStartPoint,
																	number,
																	0,
																	4);

				if (INNER_DEBUG) {
					CommonLogGW.debug(ResourceContext.getBundledMessage("block.compress.result",
																		String.valueOf(originalSize),
																		String.valueOf(compressSize)));
				}

				remain -= originalSize;
			}

			// -- calculate total size
			final int totalCompressSize = (BitsUtil.getInteger(stream.getCurrentWritePoint()) - DEFAULT_SIZE_ASSIGN_BLOCK_SIZE);
			BitsUtil.putInt(number,
							0,
							totalCompressSize);
			BufferedRandomAccessStreamUtil.writeRandomPosition(	stream,
																0,
																number,
																0,
																4);
			if (INNER_DEBUG) {
				CommonLogGW.debug(ResourceContext.getBundledMessage("total.compress.result",
																	String.valueOf(length),
																	String.valueOf(totalCompressSize)));
			}
		}
		finally {
			ObjectFinalizer.close(out);

			ArrayContainer.recycleByteArray(number);
		}
	}

	/**
	 * @param source
	 * @param target
	 * @param compress
	 * @param level
	 * @param length
	 * @param orginal
	 * @throws Exception
	 */
	private static final void copyStream(	final InputStream source,
											final OutputStream target,
											final boolean compress,
											final int level,
											final long length,
											final long orginal) throws Exception {
		long remain = length;
		GzipCompressorV1 compressor = null;
		byte[] buffer = null;
		long copy = 0;
		BufferedRandomAccessStream stream = null;
		OutputStream out = null;
		try {
			stream = BufferedRandomAccessStream.getInstance(12);
			out = stream.getOutputStream();
			buffer = ArrayContainer.getStreamCopyBuffer();

			compressor = getInstance();
			if (compress) {
				compressor.openCompress(out,
										level);
			}
			else {
				compressor.openDecompress(out);
			}

			final int bufferLength = buffer.length;

			while (remain > 0) {
				final int size = source.read(	buffer,
												0,
												(int) Math.min(	bufferLength,
																remain));

				if (size < 0) {
					break;
				}

				compressor.write(	buffer,
									0,
									size);

				copy += size;
				remain -= size;

				if (remain == 0) {
					break;
				}
			}
			compressor.flush();

			// -- check -- //
			final long tempSize = stream.remain();
			if (orginal > 0 && orginal != tempSize) {
				ExceptionGW.filterMessage(ResourceContext.getBundledMessage("block.uncompress.size.different",
																			String.valueOf(orginal),
																			String.valueOf(tempSize)));
				return;
			}
			// -- dump -- //
			ObjectFinalizer.close(out);
			out = null;

			stream.dump(target);
		}
		finally {
			ObjectFinalizer.close(out);
			ObjectFinalizer.close(stream);
			ObjectFinalizer._finalize(compressor);
			ArrayContainer.recycleByteArray(buffer);
		}

		if (length != copy) {
			ExceptionGW.filterMessage(ResourceContext.getBundledMessage("copy.stream.remain",
																		String.valueOf(length),
																		String.valueOf(copy)));
		}
	}

	/**
	 * 
	 */
	public static final boolean COMPRESS_NOWRAP = false;

	/**
	 * 
	 */
	protected static final int BUFFER_DEPTH = 12;

	/**
	 * 
	 */
	protected static final byte[] BLANK_BUFFER = ArrayContainer.getByteArray(BUFFER_DEPTH);

	/**
	 * 
	 */
	protected static final int bufsize = BLANK_BUFFER.length;

	static {
		for (int i = 0; i < bufsize; i++) {
			BLANK_BUFFER[i] = (byte) (0 & 0xFF);
		}
	}

	/**
	 * 
	 */
	protected byte[] buf = null;

	/**
	 * 
	 */
	protected boolean compress;

	/**
	 * 
	 */
	protected OutputStream out;

	/**
	 * 
	 */
	protected final Deflater compresser = new Deflater(	Deflater.BEST_COMPRESSION,
														COMPRESS_NOWRAP);

	/**
	 * 
	 */
	protected final Inflater decompresser = new Inflater(COMPRESS_NOWRAP);

	/**
	 * 
	 */
	public GzipCompressorV1() {
		super();
		this.buf = ArrayContainer.getByteArray(BUFFER_DEPTH);
	}

	/**
	 * 
	 */
	final void initBuffer() {
		System.arraycopy(	BLANK_BUFFER,
							0,
							this.buf,
							0,
							bufsize);
	}

	/**
	 * @param _out
	 */
	public final void openCompress(OutputStream _out) {
		this.openCompress(	_out,
							Deflater.BEST_COMPRESSION);
	}

	/**
	 * @param _out
	 * @param level
	 */
	public final void openCompress(	OutputStream _out,
									int level) {
		this.compress = true;
		this.out = _out;
		this.compresser.reset();
		this.compresser.setLevel(level);
	}

	/**
	 * @param _out
	 */
	public final void openDecompress(OutputStream _out) {
		this.compress = false;
		this.out = _out;
		this.decompresser.reset();
	}

	/**
	 * @param b
	 * @throws IOException
	 * @throws DataFormatException
	 */
	public void write(byte b[])	throws IOException,
								DataFormatException {
		this.write(	b,
					0,
					b.length);
	}

	/**
	 * @param b
	 * @param off
	 * @param len
	 * @throws IOException
	 * @throws DataFormatException
	 */
	public void write(	byte b[],
						int off,
						int len) throws IOException,
								DataFormatException {
		if (len == 0) {
			return;
		}

		if (this.compress) {
			this.compress(	b,
							off,
							len);
		}
		else {
			this.decompress(b,
							off,
							len);
		}
	}

	/**
	 * @throws IOException
	 */
	public final void flush()	throws IOException,
								DataFormatException {
		if (this.compress) {
			this.flushCompress();
		}
		else {
			this.flushDecompress();
		}

		this.out.flush();
		this.out = null;
	}

	/**
	 * @param b
	 * @param off
	 * @param len
	 * @throws IOException
	 */
	private final void compress(final byte b[],
								final int off,
								final int len) throws IOException {
		this.compresser.setInput(	b,
									off,
									len);

		this.initBuffer();
		int compressedDataLength = -1;
		while ((compressedDataLength = this.compresser.deflate(this.buf)) > 0) {
			this.out.write(	this.buf,
							0,
							compressedDataLength);
		}
	}

	/**
	 * @throws IOException
	 */
	private final void flushCompress() throws IOException {
		this.compresser.finish();

		this.initBuffer();
		int compressedDataLength = -1;
		while ((compressedDataLength = this.compresser.deflate(this.buf)) > 0) {
			this.out.write(	this.buf,
							0,
							compressedDataLength);
		}
		this.out.flush();
	}

	/**
	 * @param b
	 * @param off
	 * @param len
	 * @throws IOException
	 * @throws DataFormatException
	 */
	private final void decompress(	byte b[],
									int off,
									int len) throws IOException,
											DataFormatException {
		this.decompresser.setInput(	b,
									off,
									len);

		this.initBuffer();
		int compressedDataLength = -1;
		while ((compressedDataLength = this.decompresser.inflate(this.buf)) > 0) {
			this.out.write(	this.buf,
							0,
							compressedDataLength);
		}
	}

	/**
	 * @throws IOException
	 */
	private final void flushDecompress() throws IOException,
										DataFormatException {
		this.initBuffer();
		int compressedDataLength = -1;
		while ((compressedDataLength = this.decompresser.inflate(this.buf)) > 0) {
			this.out.write(	this.buf,
							0,
							compressedDataLength);
		}
		this.out.flush();
	}

	/**
	 * @see com.kskyb.broker.lang.CloseAdapter#innerClose()
	 */
	@Override
	protected void innerClose() {
		ArrayContainer.recycleByteArray(this.buf);
		this.buf = null;
	}
}
