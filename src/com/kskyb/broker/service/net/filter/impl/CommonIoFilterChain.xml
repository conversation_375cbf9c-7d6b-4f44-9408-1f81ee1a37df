<?xml version="1.0" encoding="euc-kr"?>
<!--
<#if io_filter_chain_compress_ver??><#else><#assign io_filter_chain_compress_ver="2"></#if>
<#if io_filter_chain_crypt_key??><#assign tcp_service_use_crypt="true"><#else><#assign tcp_service_use_crypt="false"></#if>
<#if io_filter_chain_compress_maximum_size??><#else><#assign io_filter_chain_compress_maximum_size="10240000"></#if>
<#if io_filter_chain_compress_block_size??><#else><#assign io_filter_chain_compress_block_size="57344"></#if>
<#if io_filter_chain_compress_level??><#else><#assign io_filter_chain_compress_level="9"></#if>
<#if io_filter_chain_block_stream_depth??><#else><#assign io_filter_chain_block_stream_depth="12"></#if>
<#if io_filter_chain_block_compress??><#else><#assign io_filter_chain_block_compress="true"></#if>
-->
<io-filter-chain clazz="com.kskyb.broker.service.net.filter.impl.CommonIoFilterChain" name="basic-io-filter-chain">
	<filter>
<!--
<#if  tcp_service_use_crypt=="true">
-->
	<filter-list
		clazz="com.kskyb.broker.service.net.filter.impl.CryptIoFilter"
		name="basic-encrypt-filter"
		key="${io_filter_chain_crypt_key}" />
<!--
</#if> 
<#switch io_filter_chain_compress_ver>
	<#case "2">
-->
	<filter-list
		clazz="com.kskyb.broker.service.net.filter.impl.CompressIoFilterV2"
		name="basic-compress-v2-filter"
		compressLevel="${io_filter_chain_compress_level}" />
<!--
		<#break>
	<#case "1">
-->
	<filter-list
		clazz="com.kskyb.broker.service.net.filter.impl.CompressIoFilterV1"
		name="basic-compress-v1-filter"
		maximumSize="${io_filter_chain_compress_maximum_size}"
		blockSize="${io_filter_chain_compress_block_size}"
		compressLevel="${io_filter_chain_compress_level}"
		blockStreamDepth="${io_filter_chain_block_stream_depth}"
		blockCompress="${io_filter_chain_block_compress}" />
<!--
		<#break>
	<#case "0">
-->
	<filter-list
		clazz="com.kskyb.broker.service.net.filter.impl.BypassIoFilter"
		name="bypass-filter" />
<!--
		<#break>
	<#default>
		<#stop "unknown compress version">
</#switch> 
-->
	</filter>
	<disconnect-when-idle-detect>true</disconnect-when-idle-detect>
</io-filter-chain>
