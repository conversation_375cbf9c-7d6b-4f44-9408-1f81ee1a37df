package com.kskyb.broker.service;

import com.kskyb.broker.kernel.AbstractMasterKernelService;
import com.kskyb.broker.kernel.KernelService;
import com.kskyb.broker.lang.ResourceContext;

/**
 * <AUTHOR>
 * 
 */
public final class DataSourceMasterService
											extends
											AbstractMasterKernelService {

	/**
	 * 
	 */
	public static final String SERVICE_NAME = "datasource";

	/**
	 * 
	 */
	private static final Class<? extends KernelService>[] SERVICE_CHILD_TYPES = new Class[] {
		DataSourceService.class
	};

	/**
	 * 
	 */
	private static final DataSourceMasterService service = new DataSourceMasterService();

	/**
	 * @return the service
	 */
	public static DataSourceMasterService getService() {
		return service;
	}

	/**
	 * 
	 */
	private DataSourceMasterService() {
		super(	null,
				SERVICE_NAME,
				ResourceContext.getBundledMessage("datasource.master.service.desc"),
				SERVICE_CHILD_TYPES);
	}
}
