package com.kskyb.broker.service.bpel.connector;

import java.lang.reflect.UndeclaredThrowableException;
import java.util.List;
import java.util.Map;

import javax.management.Notification;
import javax.management.NotificationListener;
import javax.management.ObjectName;

import com.kskyb.broker.event.BroadcastEvent;
import com.kskyb.broker.event.BroadcastEventBroker;
import com.kskyb.broker.event.BroadcastEventListener;
import com.kskyb.broker.inf.InformationNode;
import com.kskyb.broker.inf.InformationNodeComparator;
import com.kskyb.broker.jmx.JMXConnectionDelegateBroker;
import com.kskyb.broker.jmx.JMXConnectionDelegator;
import com.kskyb.broker.jmx.JMXEvent;
import com.kskyb.broker.kernel.Credential;
import com.kskyb.broker.kernel.KernelServiceAttributeReference;
import com.kskyb.broker.kernel.KernelServiceMethodReference;
import com.kskyb.broker.kernel.KernelServiceReference;
import com.kskyb.broker.kernel.jmx.BroadcastEventNotifyMBean;
import com.kskyb.broker.kernel.jmx.KernelServiceManageMBean;
import com.kskyb.broker.lang.CloseAdapter;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.lang.ThreadAttribute;
import com.kskyb.broker.service.bpel.BpelEventConstant;
import com.kskyb.broker.service.bpel.BpelRuntimeEvent;
import com.kskyb.broker.service.bpel.jmx.AdminMBean;
import com.kskyb.broker.service.bpel.param.ParameterRoot;
import com.kskyb.broker.service.bpel.process.CommonGroupNode;
import com.kskyb.broker.service.bpel.process.CommonRepositoryRoot;
import com.kskyb.broker.service.bpel.process.GroupNodeDeleteInfo;
import com.kskyb.broker.service.bpel.process.ScheduleInfoNode;
import com.kskyb.broker.util.ISheet;
import com.kskyb.broker.util.SequenceMap;
import com.kskyb.broker.util.TypeConvertUtil;
import com.kskyb.broker.util.param.CommonDataSheet;
import com.kskyb.broker.util.param.CommonRequest;
import com.kskyb.broker.util.param.CommonResponse;
import com.kskyb.broker.util.param.RequestInvokeException;


/**
 * <AUTHOR>
 * 
 */
/**
 * <AUTHOR>
 * 
 */
public final class MBeanConnector
									extends
									CloseAdapter {

	/**
	 * 
	 */
	static final String BPEL_EVENT_NAME = BpelRuntimeEvent.class.getName();

	/**
	 * 
	 */
	static final long BPEL_EVENT_AUTOCLOSE = 30000;

	/**
	 * 
	 */
	private static final String CYCLE_KEY = MBeanConnector.class.getName().concat("-cycle");

	static {
		ThreadAttribute.regist(	CYCLE_KEY,
								"thread.attribute.index.mbean.connector.cycle");
	}

	/**
	 * 
	 */
	private static final Object OBJECT = new Object();

	/**
	 * 
	 */
	private static final MBeanConnector instance = new MBeanConnector();

	/**
	 * @return
	 */
	public static MBeanConnector getDefault() {
		return instance;
	}

	/**
	 * @return
	 */
	public static MBeanConnector createInstance() {
		return new MBeanConnector();
	}

	/**
	 * z
	 */
	JMXConnectionDelegator connector = null;

	/**
	 * 
	 */
	Credential credential = null;

	/**
	 * 
	 */
	ReconnectAdviser reconnectAdviser = null;

	/**
	 * 
	 */
	private final BroadcastEventListener listener = new JMXEvent.Listener() {
		/**
		 * @see com.kskyb.broker.event.BroadcastEventListenerAdapter#fire(com.kskyb.broker.event.BroadcastEvent)
		 */
		@Override
		protected void fire(JMXEvent event) {
			MBeanConnector.this.fireJMXEvent(event);
		}
	};

	/**
	 * 
	 */
	private final NotificationListener[] notificationListener = new NotificationListener[] {
		new NotificationListener() {

			/**
			 * @see javax.management.NotificationListener#handleNotification(javax.management.Notification, java.lang.Object)
			 */
			public final void handleNotification(	final Notification notification,
													final Object handback) {
				if (BroadcastEventNotifyMBean.STOP_NOTIFY_NAME.equals(notification.getType())) {
					if (MBeanConnectorEvent.isAvailable()) {
						MBeanConnectorEvent.fireJMXConnector(	MBeanConnectorEvent.TYPE_JMX_CONNECTOR_EVENT_NOTIFICATION_STOP,
																MBeanConnector.this,
																null,
																null);
					}
					return;
				}
				if (BroadcastEventNotifyMBean.START_NOTIFY_NAME.equals(notification.getType())) {
					if (MBeanConnectorEvent.isAvailable()) {
						MBeanConnectorEvent.fireJMXConnector(	MBeanConnectorEvent.TYPE_JMX_CONNECTOR_EVENT_NOTIFICATION_START,
																MBeanConnector.this,
																null,
																null);
					}
					return;
				}
				if (BroadcastEventNotifyMBean.ALREADY_START_NOTIFY_NAME.equals(notification.getType())) {
					if (MBeanConnectorEvent.isAvailable()) {
						MBeanConnectorEvent.fireJMXConnector(	MBeanConnectorEvent.TYPE_JMX_CONNECTOR_EVENT_NOTIFICATION_ALREADY_START,
																MBeanConnector.this,
																null,
																null);
					}
					return;
				}

				final Object source = notification.getSource();

				if (source instanceof BroadcastEvent) {
					BroadcastEventBroker.getInstance().broadcast((BroadcastEvent) source);
				}
			}
		}
	};

	/**
	 * 
	 */
	private final JMXConnectionDelegateBroker broker = new JMXConnectionDelegateBroker() {
		/**
		 * @see com.kskyb.broker.jmx.JMXConnectionDelegateBroker#getNotificationListener(javax.management.ObjectName)
		 */
		public NotificationListener[] getNotificationListener(final ObjectName oname) {
			if (BroadcastEventNotifyMBean.BEAN_NAME.equals(oname)) {
				return MBeanConnector.this.notificationListener;
			}
			return null;
		}

		/**
		 * @see com.kskyb.broker.jmx.JMXConnectionDelegateBroker#initializeMBeanProxy(javax.management.ObjectName, java.lang.Object)
		 */
		public void initializeMBeanProxy(	ObjectName oname,
											Object instance) {
			if (BroadcastEventNotifyMBean.BEAN_NAME.equals(oname)) {
				final BroadcastEventNotifyMBean notifyProxy = (BroadcastEventNotifyMBean) instance;
				notifyProxy.addType(BPEL_EVENT_NAME,
									BpelEventConstant.EVENT_RUNTIME_INVOKE_SUCCESS);
				notifyProxy.addType(BPEL_EVENT_NAME,
									BpelEventConstant.EVENT_RUNTIME_INVOKE_ERROR);
			}

		}
	};

	/**
	 * 
	 */
	private MBeanConnector() {
		super(false);
		BroadcastEventBroker.getInstance().registMessageListener(this.listener);
	}

	// --------------------------------------------------------------------------------------------------- external service
	private String currentId = null;

	private String currentPasswd = null;

	/**
	 * @param ip
	 * @param port
	 * @param contextName
	 * @param id
	 * @param passwd
	 * @throws Throwable
	 */
	public final void connect(	final String ip,
								final int port,
								final String contextName,
								final String id,
								final String passwd) throws Throwable {
		ObjectFinalizer.close(this);
		this.innerOpen();

		this.connector = new JMXConnectionDelegator(ip,
													port,
													contextName,
													MBeanConnector.class.getClassLoader());
		this.connector.registConnectionDelegateBroker(this.broker);
		this.connector.connect(false);
		if (id != null && passwd != null) {
			this.login(	id,
						passwd);
		}
	}

	/**
	 * @param id
	 * @param passwd
	 */
	public final void login(final String id,
							final String passwd) {
		this.currentId = id;
		this.currentPasswd = passwd;

		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		this.credential = adminProxy.login(	this.currentId,
											this.currentPasswd);
	}

	/**
	 * @return
	 */
	public final boolean isLogin() {
		if (this.connector == null) {
			return false;
		}

		return this.credential != null && this.connector != null;
	}

	private static final RuntimeException RE = new RuntimeException(":");

	/**
	 * 
	 */
	public final boolean checkLogin(final boolean robust) {
		if (this.isLogin()) {
			return true;
		}
		if (robust) {
			throw new UndeclaredThrowableException(	RE,
													"login requred");
		}
		return false;
	}

	/**
	 * @param reconnectAdviser
	 *            the reconnectAdviser to set
	 */
	public void setReconnectAdviser(ReconnectAdviser reconnectAdviser) {
		this.reconnectAdviser = reconnectAdviser;
	}

	/**
	 * @return the connector
	 */
	public JMXConnectionDelegator getConnector() {
		this.connector.connect(false);
		return this.connector;
	}

	/**
	 * 
	 */
	public synchronized final void disconnect() {
		ObjectFinalizer.close(this.connector);
		this.connector = null;
		this.credential = null;
		if (MBeanConnectorEvent.isAvailable()) {
			MBeanConnectorEvent.fireJMXConnector(	MBeanConnectorEvent.TYPE_JMX_CONNECTOR_DISCONNECT,
													this,
													ResourceContext.getBundledMessage("connector.disconnect.successfully"),
													null);
		}
	}

	/**
	 * @return
	 */
	public final String getConnectInfo() {
		if (this.isLogin()) {
			return this.connector.toString();
		}

		return "login require";
	}

	/**
	 * 
	 */
	public final boolean reconnect(Throwable rootException) {
		if (ThreadAttribute.getAttribute(CYCLE_KEY) != null) {
			return false;
		}

		if (this.reconnectAdviser == null) {
			return false;
		}

		if (!this.reconnectAdviser.reconnectFor(rootException)) {
			return false;
		}

		try {
			ThreadAttribute.setAttribute(	CYCLE_KEY,
											OBJECT);
			if (this.connector.connect(true)) {
				return true;
			}
		}
		catch (Throwable thw) {
			if (MBeanConnectorEvent.isAvailable()) {
				MBeanConnectorEvent.fireJMXConnector(	MBeanConnectorEvent.TYPE_JMX_CONNECTOR_CONNECT_ERROR,
														this,
														null,
														thw);
			}
		}
		finally {
			ThreadAttribute.removeAttribute(CYCLE_KEY);
		}
		return false;
	}

	/**
	 * @return
	 */
	public final Credential getCredential() {
		if (this.credential == null) {
			throw new RuntimeException(ResourceContext.getBundledMessage("not.authorize.user"));
		}
		return this.credential;
	}

	/**
	 * @param pid
	 * @throws BpelInvokeException
	 */
	public final void reloadRepository(	final String pid,
										final boolean logic) throws RequestInvokeException {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		adminProxy.repositoryReload(this.credential,
									logic	? this.credential.getSingleRepositoryName()
											: this.credential.getCompositeRepositoryName(),
									pid);
	}

	/**
	 * @throws RequestInvokeException
	 */
	public final void refreshRepository(final boolean logic) throws RequestInvokeException {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		adminProxy.repositoryRefresh(	this.credential,
										logic	? this.credential.getSingleRepositoryName()
												: this.credential.getCompositeRepositoryName());
	}

	/**
	 * FIXME : 리로딩하고 결과를 리턴하도록 수정해야할듯
	 * 
	 * @param repositoryName
	 * @param pid
	 * @throws RequestInvokeException
	 */
	public final ParameterRoot reloadParameterMetaData(	final String pid,
														final boolean save) throws RequestInvokeException {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		CommonResponse response = null;
		ParameterRoot root = null;
		try {
			response = adminProxy.repositoryReloadLogicParameterMetaData(	this.credential,
																			this.credential.getSingleRepositoryName(),
																			pid);
			switch (response.getType()) {
				case CommonResponse.TYPE_OBJECT: {
					root = TypeConvertUtil.convert(	response.getObject(),
													ParameterRoot.class);
					break;
				}

				case CommonResponse.TYPE_COUNT:
				case CommonResponse.TYPE_SHEET: {
					return null;
				}
				case CommonResponse.TYPE_ERROR: {
					final RequestInvokeException thw = response.getErrorMessage();
					throw new RuntimeException(	ResourceContext.getBundledMessage(	"UniaPlusConnector.reload.parameter.error",
																					pid),
												thw);
				}
				default: {
					return null;
				}
			}
		}
		finally {
			ObjectFinalizer.close(response);
		}

		if (save) {
			try {
				response = adminProxy.repositoryUpdateLogicParameterRoot(	this.credential,
																			this.credential.getSingleRepositoryName(),
																			pid,
																			root);
				switch (response.getType()) {
					case CommonResponse.TYPE_ERROR: {
						final RequestInvokeException thw = response.getErrorMessage();
						throw new RuntimeException(	ResourceContext.getBundledMessage(	"UniaPlusConnector.save.parameter.error",
																						pid),
													thw);
					}

					default: {
						break;
					}
				}
			}
			finally {
				ObjectFinalizer.close(response);
			}
		}

		return root;
	}

	/**
	 * @return
	 */
	public final CommonRepositoryRoot getCommonRepositoryRoot(final boolean logic) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		final CommonRepositoryRoot root = adminProxy.repositoryGetLogicRepositoryRoot(	MBeanConnector.this.credential,
																						logic	? MBeanConnector.this.credential.getSingleRepositoryName()
																								: MBeanConnector.this.credential.getCompositeRepositoryName());
		root.assignGroupSorter(InformationNodeComparator.ID);
		return root;
	}

	/**
	 * @param pid
	 * @return
	 */
	public final ParameterRoot getLogicParameterRoot(final String pid) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.repositoryGetLogicParameterRoot(	this.credential,
															this.credential.getSingleRepositoryName(),
															pid);
	}

	/**
	 * @param pid
	 * @param root
	 */
	public final boolean updateLogicParameterRoot(	final String pid,
													final ParameterRoot root) {
		if (root == null) {
			return false;
		}
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		CommonResponse response = null;
		try {
			response = adminProxy.repositoryUpdateLogicParameterRoot(	this.credential,
																		this.credential.getSingleRepositoryName(),
																		pid,
																		root);
			switch (response.getType()) {
				case CommonResponse.TYPE_ERROR: {
					throw response.getErrorMessage();
				}
			}

			return true;
		}
		catch (Throwable thw) {
			throw new RuntimeException(	ResourceContext.getBundledMessage(	"UniaplusConnector.param.root.error",
																			pid),
										thw);

		}
		finally {
			ObjectFinalizer.close(response);
		}
	}

	/**
	 * @param pid
	 * @return
	 */
	public final CommonResponse deleteLogic(final String pid,
											final boolean logic) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.repositoryDeleteLogic(this.credential,
												logic	? this.credential.getSingleRepositoryName()
														: this.credential.getCompositeRepositoryName(),
												pid);
	}

	/**
	 * @param logicNode
	 * @return
	 */
	public final CommonResponse createLogic(final InformationNode logicNode,
											final boolean logic) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.repositoryCreateLogic(this.credential,
												logic	? this.credential.getSingleRepositoryName()
														: this.credential.getCompositeRepositoryName(),
												logicNode);
	}

	/**
	 * @param logicNode
	 * @return
	 */
	public final CommonResponse updateLogic(final InformationNode logicNode,
											final boolean logic) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.repositoryUpdateLogic(this.credential,
												logic	? this.credential.getSingleRepositoryName()
														: this.credential.getCompositeRepositoryName(),
												logicNode);
	}

	/**
	 * @param groupNode
	 * @param update
	 * @return
	 */
	public final CommonRepositoryRoot updateLogicGroup(	final CommonGroupNode groupNode,
														final boolean update) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return update	? adminProxy.repositoryUpdateLogicGroup(this.credential,
																this.credential.getSingleRepositoryName(),
																groupNode)
						: adminProxy.repositoryCreateLogicGroup(this.credential,
																this.credential.getSingleRepositoryName(),
																groupNode);
	}

	/**
	 * @param groupNode
	 * @return
	 */
	public final CommonRepositoryRoot deleteLogicGroup(final CommonGroupNode groupNode) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.repositoryDeleteLogicGroup(	this.credential,
														this.credential.getSingleRepositoryName(),
														groupNode);
	}

	/**
	 * @param pid
	 * @param logic
	 * @return
	 */
	public final CommonResponse scheduleReloadTimer(final String pid,
													final boolean logic) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		final String repositoryName = logic	? this.credential.getSingleRepositoryName()
											: this.credential.getCompositeRepositoryName();
		return adminProxy.scheduleReloadTimer(	MBeanConnector.this.credential,
												repositoryName,
												pid);
	}

	/**
	 * @param info
	 * @return
	 */
	public final String scheduleGetCurrentInfo(final String info) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.scheduleGetCurrentInfo(	MBeanConnector.this.credential,
													info);
	}

	/**
	 * @param info
	 * @return
	 */
	public final CommonResponse scheduleUpdateInfo(final ScheduleInfoNode info) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.scheduleUpdateInfo(	this.credential,
												info);
	}

	/**
	 * @param id
	 * @return
	 */
	public final ScheduleInfoNode scheduleCreateInfo(final String id) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.scheduleCreateInfo(	this.credential,
												id);
	}

	/**
	 * @param id
	 * @param logicId
	 * @param from
	 * @param to
	 * @param keyword
	 * @return
	 */
	public final ISheet searchProcessInvokeLog(	final String logicId,
												final String from,
												final String to,
												final List<String> keyword) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.searchProcessInvokeLog(	this.credential,
													logicId,
													from,
													to,
													keyword);
	}

	/**
	 * @param logicId
	 * @param from
	 * @param to
	 * @return
	 */
	public final ISheet searchProcessErrorLog(	final String logicId,
												final String from,
												final String to) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.searchProcessErrorLog(this.credential,
												logicId,
												from,
												to);
	}

	/**
	 * @param repositoryName
	 * @return
	 */
	public final String[] repositoryGetCreatorList(final boolean logic) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		final String repositoryName = logic	? this.credential.getSingleRepositoryName()
											: this.credential.getCompositeRepositoryName();
		return adminProxy.repositoryGetCreatorList(	MBeanConnector.this.credential,
													repositoryName);
	}

	/**
	 * @param logic
	 * @return
	 */
	public final SequenceMap<String, String[]> repositoryGetCreatorStruct(final boolean logic) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		final String repositoryName = logic	? this.credential.getSingleRepositoryName()
											: this.credential.getCompositeRepositoryName();
		return adminProxy.repositoryGetCreatorStruct(	this.credential,
														repositoryName);
	}

	/**
	 * @param credential
	 * @param groupId
	 * @return
	 */
	public final GroupNodeDeleteInfo repositoryGetGroupNodeDeleteInfo(final String groupId) {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		return adminProxy.repositoryGetGroupNodeDeleteInfo(	this.credential,
															groupId);
	}

	/**
	 * @param info
	 * @throws RequestInvokeException
	 */
	public final void repositoryProcessGroupNodeDeleteInfo(final GroupNodeDeleteInfo info) throws RequestInvokeException {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		adminProxy.repositoryProcessGroupNodeDeleteInfo(MBeanConnector.this.credential,
														info);
	}

	/**
	 * @param info
	 * @throws RequestInvokeException
	 */
	public final void repositoryChangeLogicGroupIndex(	final String targetId,
														final String changedGroupIndex) throws RequestInvokeException {
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		adminProxy.repositoryChangeGroupNode(	MBeanConnector.this.credential,
												targetId,
												changedGroupIndex);
	}

	/**
	 * @param context
	 * @param id
	 * @param param
	 * @param sheet
	 * @return
	 */
	public final CommonResponse invoke(	final String context,
										final String id,
										final Map<String, Object> param,
										final CommonDataSheet sheet) {
		return this.invokeTarget(	context,
									id,
									false,
									param,
									sheet);
	}

	/**
	 * @param id
	 * @param param
	 * @param listener
	 */
	public final CommonResponse invokeSingleLogic(	final String id,
													final Map<String, Object> param,
													final CommonDataSheet sheet) {
		return this.invokeTarget(	this.credential.getSingleRepositoryName(),
									id,
									false,
									param,
									sheet);
	}

	/**
	 * @param id
	 * @param param
	 * @param sheet
	 * @return
	 */
	public final CommonResponse invokeCompositeLogic(	final String id,
														final Map<String, Object> param,
														final CommonDataSheet sheet) {
		return this.invokeTarget(	this.credential.getCompositeRepositoryName(),
									id,
									false,
									param,
									sheet);
	}

	/**
	 * @param id
	 * @param param
	 * @param sheet
	 * @param listener
	 */
	public final CommonResponse simulateSingleLogic(final String id,
													final Map<String, Object> param,
													final CommonDataSheet sheet) {
		return this.invokeTarget(	this.credential.getSingleRepositoryName(),
									id,
									true,
									param,
									sheet);
	}

	/**
	 * @param id
	 * @param param
	 * @param sheet
	 * @param listener
	 */
	public final CommonResponse simulateCompositeLogic(	final String id,
														final Map<String, Object> param,
														final CommonDataSheet sheet) {
		return this.invokeTarget(	this.credential.getCompositeRepositoryName(),
									id,
									true,
									param,
									sheet);
	}

	/**
	 * @param type
	 * @param id
	 * @param param
	 * @param sheet
	 * @param listener
	 */
	private final CommonResponse invokeTarget(	final String context,
												final String id,
												final boolean simulate,
												final Map<String, Object> param,
												final CommonDataSheet sheet) {
		// TODO : 아래를 확인해서 flag를 가지고 진행상황을 보일수 있도록 하면 좋겠다.
		// ProgressBarExecutor.createInstance().execute( this.shell,
		// new ProgressFactory(source));
		final AdminMBean adminProxy = this.connector.lookupMBean(	AdminMBean.BEAN_NAME,
																	AdminMBean.class);
		CommonRequest source = null;

		try {
			source = new CommonRequest();
			if (param != null) {
				source.setMap(param);
			}
			if (sheet != null) {
				source.setSheet(sheet);
			}

			return adminProxy.invoke(	this.credential,
										context,
										id,
										source,
										simulate);
		}
		finally {
			ObjectFinalizer.close(source);
		}
	}

	// ----------------------------------------------------------------------------------------- notify bean proxy
	/**
	 * @return
	 */
	public final boolean isSupportNofityBpelEvent() {
		final BroadcastEventNotifyMBean notifyProxy = this.connector.lookupMBean(	BroadcastEventNotifyMBean.BEAN_NAME,
																					BroadcastEventNotifyMBean.class);
		return notifyProxy != null;
	}

	/**
	 * @param info
	 * @throws RequestInvokeException
	 */
	public final void notifyBpelEventStart() throws RequestInvokeException {
		final BroadcastEventNotifyMBean notifyProxy = this.connector.lookupMBean(	BroadcastEventNotifyMBean.BEAN_NAME,
																					BroadcastEventNotifyMBean.class);
		if (notifyProxy == null) {
			return;
		}
		notifyProxy.startEventBroadcast(BPEL_EVENT_NAME,
										BPEL_EVENT_AUTOCLOSE);
	}

	/**
	 * @throws RequestInvokeException
	 */
	public final void notifyBpelEventStop() throws RequestInvokeException {
		final BroadcastEventNotifyMBean notifyProxy = this.connector.lookupMBean(	BroadcastEventNotifyMBean.BEAN_NAME,
																					BroadcastEventNotifyMBean.class);
		if (notifyProxy == null) {
			return;
		}
		notifyProxy.stopEventBroadcast(BPEL_EVENT_NAME);
	}

	// ----------------------------------------------------------------------------------------- service bean proxy
	/**
	 * @return
	 */
	public final KernelServiceReference lookupKernelServiceReference(	final String urn,
																		final boolean child) {
		final KernelServiceManageMBean serviceProxy = this.connector.lookupMBean(	KernelServiceManageMBean.BEAN_NAME,
																					KernelServiceManageMBean.class);
		return serviceProxy.makeKernelServiceReference(	urn,
														child);
	}

	/**
	 * @param ref
	 */
	public final void updateKernelServiceReperence(final KernelServiceReference ref) {
		final KernelServiceReference copy = this.lookupKernelServiceReference(	ref.getStringAttribute(KernelServiceReference.KEY_SERVICE_URN),
																				false);
		ref.merge(copy);
	}

	/**
	 * @param credential
	 * @return
	 */
	public final String[] lookupAvailableExternalSystem() {
		final KernelServiceManageMBean serviceProxy = this.connector.lookupMBean(	KernelServiceManageMBean.BEAN_NAME,
																					KernelServiceManageMBean.class);
		return serviceProxy.lookupAvailableExternalSystem();
	}

	/**
	 * @param ref
	 */
	public final void startKernelService(final KernelServiceReference ref) {
		final String urn = ref.getStringAttribute(KernelServiceReference.KEY_SERVICE_URN);
		final KernelServiceManageMBean serviceProxy = MBeanConnector.this.connector.lookupMBean(KernelServiceManageMBean.BEAN_NAME,
																								KernelServiceManageMBean.class);
		final String result = serviceProxy.startKernelService(urn);
		if (result == null) {
			this.updateKernelServiceReperence(ref);
		}
	}

	/**
	 * @param urn
	 */
	public final void stopKernelService(final KernelServiceReference ref) {
		final String urn = ref.getStringAttribute(KernelServiceReference.KEY_SERVICE_URN);
		final KernelServiceManageMBean serviceProxy = this.connector.lookupMBean(	KernelServiceManageMBean.BEAN_NAME,
																					KernelServiceManageMBean.class);
		final String result = serviceProxy.stopKernelService(urn);
		if (result == null) {
			this.updateKernelServiceReperence(ref);
		}
	}

	/**
	 * @param ref
	 */
	public final void updateKernelServiceAttribute(	final KernelServiceAttributeReference ref,
													final Object obj) {
		final String urn = ref.getStringAttribute(KernelServiceAttributeReference.KEY_SERVICE_URN);
		final String attrName = ref.getStringAttribute(KernelServiceAttributeReference.KEY_FIELD_NAME);
		final KernelServiceManageMBean serviceProxy = this.connector.lookupMBean(	KernelServiceManageMBean.BEAN_NAME,
																					KernelServiceManageMBean.class);
		serviceProxy.updateKernelServiceAttribute(	urn,
													attrName,
													obj);
		ref.setAttribute(	KernelServiceAttributeReference.KEY_FIELD_CURRENT_VALUE,
							String.valueOf(obj));
	}

	/**
	 * @param ref
	 * @param obj
	 */
	public final Object invokeKernelServiceMethod(	final KernelServiceMethodReference ref,
													final Object[] args) {
		final String urn = ref.getStringAttribute(KernelServiceMethodReference.KEY_SERVICE_URN);
		final String methodName = ref.getStringAttribute(KernelServiceMethodReference.KEY_METHOD_NAME);
		final KernelServiceManageMBean serviceProxy = MBeanConnector.this.connector.lookupMBean(KernelServiceManageMBean.BEAN_NAME,
																								KernelServiceManageMBean.class);
		final Object result = serviceProxy.invokeKernelServiceMethod(	urn,
																		methodName,
																		args);
		return result;
	}

	/**
	 * @see com.kskyb.broker.lang.CloseAdapter#innerClose()
	 */
	@Override
	protected final void innerClose() {
		this.disconnect();
	}

	// ----------------------------------------------------------------------------------------- BroadcastEventListener

	/**
	 * @param event
	 */
	private final void fireJMXEvent(final JMXEvent event) {
		switch (event.getType()) {
			case JMXEvent.TYPE_CLIENT_CONNECT_SUCCESS: {
				final JMXConnectionDelegator dele = event.getTypedAttribute(JMXEvent.KEY_CONNECTION_DELEGATOR,
																			JMXConnectionDelegator.class);
				if (dele != this.connector) {
					return;
				}

				if (MBeanConnectorEvent.isAvailable()) {
					MBeanConnectorEvent.fireJMXConnector(	MBeanConnectorEvent.TYPE_JMX_CONNECTOR_CONNECT_SUCCESS,
															this,
															ResourceContext.getBundledMessage("connector.connect.server.error"),
															null);
				}
				break;
			}

			case JMXEvent.TYPE_CLIENT_CONNECT_ERROR: {
				final Throwable thw = event.getTypedAttribute(	JMXEvent.KEY_ERROR_STACK,
																Throwable.class);
				if (MBeanConnectorEvent.isAvailable()) {
					MBeanConnectorEvent.fireJMXConnector(	MBeanConnectorEvent.TYPE_JMX_CONNECTOR_CONNECT_ERROR,
															this,
															ResourceContext.getBundledMessage("connector.connect.server.error"),
															thw);
				}
				return;
			}

			default: {
				break;
			}
		}
	}

	// --------------------------------------------------------------------------------------------------- inner classes
	/**
	 * <AUTHOR>
	 * 
	 */
	public static interface ReconnectAdviser {
		/**
		 * @param thw
		 * @return
		 */
		public abstract boolean reconnectFor(Throwable thw);
	}
}
