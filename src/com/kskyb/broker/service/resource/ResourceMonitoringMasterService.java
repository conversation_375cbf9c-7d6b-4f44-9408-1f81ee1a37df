package com.kskyb.broker.service.resource;

import com.kskyb.broker.async.MonitorTarget;
import com.kskyb.broker.kernel.AbstractMasterKernelService;
import com.kskyb.broker.kernel.KernelService;
import com.kskyb.broker.kernel.KernelServiceContext;
import com.kskyb.broker.kernel.serivce.UtilityService;
import com.kskyb.broker.lang.ResourceContext;

/**
 * <AUTHOR>
 * 
 */
public final class ResourceMonitoringMasterService
													extends
													AbstractMasterKernelService
																				implements
																				UtilityService {
	/**
	 * 
	 */
	public static final String SERVICE_NAME = "resource";

	/**
	 * 
	 */
	private static final Class<? extends KernelService>[] SERVICE_CHILD_TYPES = new Class[] {
		ResourceMonitoringService.class
	};

	/**
	 * 
	 */
	private static ResourceMonitoringMasterService service = null;

	/**
	 * @return the service
	 */
	public static final ResourceMonitoringMasterService getService() {
		if (service == null) {
			final ResourceMonitoringService defaultService = new ResourceMonitoringService(	true,
																							"internalMonitor",
																							ResourceContext.getBundledMessage("internal.resource.monitor.service.desc"),
																							"resource-moitor",
																							ResourceContext.getBundledMessage("internal.resource.monitor.service.note"),
																							true) {
				//
			};

			service = new ResourceMonitoringMasterService(defaultService);
			service.start();

			service.registChildService(defaultService);
			defaultService.start();
		}
		return service;
	}

	/**
	 * 
	 */
	final ResourceMonitoringService defaultService;

	/**
	 * 
	 */
	private ResourceMonitoringMasterService(final ResourceMonitoringService arg1) {
		super(	KernelServiceContext.getServiceContext().getSystemUtilityService(),
				SERVICE_NAME,
				ResourceContext.getBundledMessage("resource.monitoring.master.service.desc"),
				null,
				null,
				SERVICE_CHILD_TYPES,
				false);
		this.defaultService = arg1;
	}

	/**
	 * @param resource
	 */
	public final void regist(final MonitorTarget resource) {
		this.regist(null,
					resource);
	}

	/**
	 * @param serviceName
	 * @param resource
	 */
	public final void regist(	final String serviceName,
								final MonitorTarget resource) {
		this.lookup(serviceName).add(resource);
	}

	/**
	 * @param resource
	 */
	public final void remove(final MonitorTarget resource) {
		this.remove(null,
					resource);
	}

	/**
	 * @param serviceName
	 * @param resource
	 */
	public final void remove(	final String serviceName,
								final MonitorTarget resource) {
		this.lookup(serviceName).remove(resource);
	}

	/**
	 * @param serviceName
	 * @return
	 */
	private final ResourceMonitoringService lookup(final String serviceName) {
		if (serviceName == null) {
			return this.defaultService;
		}

		final ResourceMonitoringService service = (ResourceMonitoringService) this.getChildService(serviceName);

		return service == null	? this.defaultService
								: service;
	}

}
