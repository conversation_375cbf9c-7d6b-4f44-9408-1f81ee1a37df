package com.kskyb.broker.service;

import com.kskyb.broker.model.ExecutableModel;
import com.kskyb.broker.model.Executor;
import com.kskyb.broker.util.ServiceContextContainer;

/**
 * ExecutorRuntimeService 과 대응하여 실행자를 저장하는 context
 * 
 * <AUTHOR>
 * 
 */
public final class ExecutorContext
									extends
									ServiceContextContainer<Executor> {
	/**
	 * 
	 */
	private static final ExecutorContext context = new ExecutorContext();

	/**
	 * @return the context
	 */
	public static final ExecutorContext getContext() {
		return context;
	}

	/**
	 * 
	 */
	private ExecutorContext() {
		super();
	}

	/**
	 * @see com.kskyb.broker.util.ServiceContextContainer#convertTargetName(java.lang.String)
	 */
	@Override
	protected final String convertTargetName(final String obj) {
		return obj.concat("Executor");
	}

	/**
	 * @param model
	 * @return
	 */
	public final Executor lookup(final ExecutableModel model) {
		return this.getInstance(model.getClass());
	}

	/**
	 * @see com.kskyb.broker.util.ServiceContextContainer#getTypeClass()
	 */
	@Override
	protected Class<Executor> getTypeClass() {
		return Executor.class;
	}
}
