package com.kskyb.magent.task;

import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kskyb.broker.adapter.jdbc.SQLExceptionFilter;
import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.BroadcastEventBroker;
import com.kskyb.broker.event.BroadcastEventListener;
import com.kskyb.broker.event.BroadcastEventListenerAdapter;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.util.ISelector;
import com.kskyb.broker.util.jdbc.DeligatePreparedStatementInfo;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.UtilDelegator;
import com.kskyb.magent.config.MagentConfig;
import com.kskyb.magent.config.ResourceTableManager;
import com.kskyb.magent.struct.BufferManager;
import com.kskyb.magent.struct.MMSReportStruct;
import com.kskyb.magent.struct.SMSReportBuffer;
import com.kskyb.magent.struct.SMSReportStruct;

/**
 * SMS 로그, 리포트 버퍼를 처리하는 Task
 */
public class SmsLogBufferMonitorTask
									extends
									AbstractDbTask_Direct {
	/**
	 * 
	 */
	private static final MessageResource resource = MessageResource.getResource(SmsLogBufferMonitorTask.class);

	/**
	 * 
	 */
	private static final Logger logger = Logger.getLogger("sms_report");

	/**
	 * log table mapping index <br>
	 * LOG테이블 매핑 인덱스.
	 */
	private static final Map.Entry<String, String> entry = new Map.Entry<String, String>() {
		String value = null;

		@Override
		public String getKey() {
			return "#TABLE_NAME#";
		}

		@Override
		public String getValue() {
			return this.value;
		}

		@Override
		public String setValue(String _value) {
			String old = this.value;
			this.value = _value;
			return old;
		}

	};

	/**
	 * execute info <br>
	 * 쿼리 실행정보.
	 */
	DeligatePreparedStatementInfo infoUpdateLog;

	/**
	 * execute Statement <br>
	 * 쿼리 실행 Statement
	 */
	Statement psUpdateLog = null;

	/**
	 * execute info <br>
	 * 쿼리 실행정보.
	 */
	DeligatePreparedStatementInfo infoUpdateResult;

	/**
	 * execute Statement <br>
	 * 쿼리 실행 Statement
	 */
	Statement psUpdateResult = null;
	
	//2017.03.21 DB 커넥션 유지를 위한 확인 쿼리
	DeligatePreparedStatementInfo infoSelectValidationQry = null;
	Statement psSelectValidationQry = null;

	/**
	 * execute query <br>
	 * 실행 쿼리 참조자
	 */
	protected String queryUpdateLog = null;

	/**
	 * execute query <br>
	 * 실행 쿼리 참조자
	 */
	protected String queryUpdateResult = null;
	
	protected String querySelectValidationQry = null;

	/**
	 * initialize flag <br>
	 * 초기화 플래그.
	 */
	boolean initFlag = false;

	/**
	 * 
	 */
	private final BroadcastEventListener<SMSReportStruct> listener = new BroadcastEventListenerAdapter<SMSReportStruct>(SMSReportStruct.class) {
		/**
		 * @see com.kskyb.broker.event.BroadcastEventListenerAdapter#fire(com.kskyb.broker.event.BroadcastEvent)
		 */
		@Override
		protected void fire(SMSReportStruct event) {
			SmsLogBufferMonitorTask.this._updateLog(event);
		}
	};

	/**
	 * 
	 */
	private static final boolean DEBUG = true;

	/**
	 * @param struct
	 * @return
	 */
	private final boolean _updateLog(SMSReportStruct struct) 
	{
		try 
		{
			this.initStatement();
			
			return this.updateLog(struct);
		}
		catch (Throwable thw) 
		{
			logger.error(	"sms report update error", thw);
			
			return false;
		}
		finally 
		{
			this.releaseStatement();
		}
	}

	/**
	 * @param struct
	 * @return
	 */
	private final boolean updateLog(SMSReportStruct struct) {
		ISelector selector = null;
		try {
			selector = struct.getSelector();
			
			/*
			//-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
			//MS-SQL DB 쿼리변경
			String reportDate_ = selector.getParameter(SMSReportStruct.KEY_TRAN_REPORTDATE);
			String rstlDate_ = selector.getParameter(SMSReportStruct.KEY_TRAN_RSLT);
			
			
			if(db_driver.equals("com.microsoft.sqlserver.jdbc.SQLServerDriver")){
				if(reportDate_ != null && reportDate_.length() == 14){
					reportDate_ = reportDate_.substring(0, 4) + "-" + reportDate_.substring(4, 6) + "-" + reportDate_.substring(6, 8) + " " + reportDate_.substring(8, 10) + ":" + reportDate_.substring(10, 12) + ":" + reportDate_.substring(12, 14);
					selector.setParameter(SMSReportStruct.KEY_TRAN_REPORTDATE, reportDate_);
				}
				
				if(rstlDate_ != null && rstlDate_.length() == 14){
					rstlDate_ = rstlDate_.substring(0, 4) + "-" + rstlDate_.substring(4, 6) + "-" + rstlDate_.substring(6, 8) + " " + rstlDate_.substring(8, 10) + ":" + rstlDate_.substring(10, 12) + ":" + rstlDate_.substring(12, 14);
					selector.setParameter(SMSReportStruct.KEY_TRAN_RSLT, rstlDate_);
				}
			}
			
			//-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
			*/
			
			int affect = 0;
			if (struct.isLog()) {
				if (DEBUG) {
					logger.info("update log >> " + struct.toString());
				}
				affect = DeligatePreparedStatementInfo.executeUpdate(	this.infoUpdateLog,
																		this.psUpdateLog,
																		selector);
			}
			else {
				if (DEBUG) {
					logger.info("update result >> " + struct.toString());
				}
				affect = DeligatePreparedStatementInfo.executeUpdate(	this.infoUpdateResult,
																		this.psUpdateResult,
																		selector);
			}

			if (affect == 1) {
				final String tranPr = selector.getParameter(SMSReportStruct.KEY_SERIAL);
				final String tranStatus = selector.getParameter(SMSReportStruct.KEY_TRAN_STATUS);
				final String tranResult = selector.getParameter(SMSReportStruct.KEY_TRAN_RSLT);
				final String tranTelco = selector.getParameter(SMSReportStruct.KEY_TRAN_END_TELCO);
				final String tranTime = selector.getParameter(SMSReportStruct.KEY_TRAN_REPORTDATE);
				logger.debug(resource.getBundle("log.update.one.record.correctly",
												tranPr,
												tranStatus,
												tranResult,
												tranTelco,
												tranTime));
			}
			else {
				final String tranPr = selector.getParameter(SMSReportStruct.KEY_SERIAL);
				final String tranStatus = selector.getParameter(SMSReportStruct.KEY_TRAN_STATUS);
				final String tranResult = selector.getParameter(SMSReportStruct.KEY_TRAN_RSLT);
				final String tranTelco = selector.getParameter(SMSReportStruct.KEY_TRAN_END_TELCO);
				final String tranTime = selector.getParameter(SMSReportStruct.KEY_TRAN_REPORTDATE);
				logger.error(resource.getBundle("log.update.affect.none",
												String.valueOf(affect),
												tranPr,
												tranStatus,
												tranResult,
												tranTelco,
												tranTime));
			}
		}
		catch (SQLException sqe) 
		{
			logger.error(	resource.getBundle(	"update.log.error", struct.toString()), sqe);
			
			switch (this.filter.analize(sqe)) 
			{
				case SQLExceptionFilter.CONNECT_ERROR: 
				{
					// 업데이트하지 못한 로그를 다시 버퍼에 넣고 종료
					BufferManager.getInstance().getSmsReportBuffer().pushBack(struct);
					
					try 
					{
						if (this.conn != null)				
						{
							logger.info("SQLException dbReconnect conn.close()");
							this.conn.close();						
						}
					} 
					catch (Exception e) 
					{}	
					
					this.conn = null;
					
					try 
					{
						logger.info("SQLException dbReconnect connect()");
						this.connect(db_driver, db_url, db_id, db_pass);
					} 
					catch (Throwable thw) 
					{
						// TODO Auto-generated catch block
						logger.error("SQLException dbReconnect connect() ex : ", thw);												
					}
					
					break;
				}
				default: 
				{
					break;
				}
			}						
			
			return false;
		}
		catch (Throwable thw) 
		{
			logger.error(	resource.getBundle("update.log.error"), thw);
			
			sleep();			
			
			return false;
		}	
		finally 
		{
			ObjectFinalizer.close(selector);
		}

		return true;
	}
	
	private void dbReconnectValidation(SQLException sqe) throws Throwable
	{								
		switch (this.filter.analize(sqe)) 
		{
			case SQLExceptionFilter.CONNECT_ERROR: 
			{
				
				try 
				{
					if (this.conn != null)				
					{
						logger.info("Validation SQLException dbReconnect conn.close()");
						this.conn.close();						
					}
				} 
				catch (Exception e) 
				{}	
				
				this.conn = null;
				
				try 
				{
					logger.info("Validation SQLException dbReconnect connect()");
					this.connect(db_driver, db_url, db_id, db_pass);
				} 
				catch (Throwable thw) 
				{
					// TODO Auto-generated catch block
					logger.error("Validation SQLException dbReconnect connect() ex : ", thw);
					
					throw thw;
				}						
				
				break;
			}
			default: 
			{
				break;
			}
		}
	}
	
	
	private void sleep()
	{
		try 
		{
			logger.info("SQLException Thread Sleep 3 Second");
			
			Thread.sleep(3000);
		} 
		catch (Exception e) 
		{
			// TODO: handle exception
		}
	}

	/**
	 * initailize connection and statement <br>
	 * 데이터베이스 연결 및 Statement 초기화.
	 * 
	 * @throws Exception
	 *             error occur
	 */
	private synchronized final void initStatement() throws Exception {
		if (this.initFlag) {
			return;
		}

		this.releaseStatement();

		//this.connect();

		final String tableName = ResourceTableManager.getInstance().getResourceName(this.resourceKey);
		entry.setValue(tableName);

		final String _queryUpdateLog = UtilDelegator.convertString(	this.queryUpdateLog,
																	entry);
		this.infoUpdateLog = DeligatePreparedStatementInfo.getInstance(_queryUpdateLog);
		this.psUpdateLog = DeligatePreparedStatementInfo.getStatement(	this.conn,
																		this.infoUpdateLog);

		final String _queryUpdateResult = UtilDelegator.convertString(	this.queryUpdateResult,
																		entry);
		this.infoUpdateResult = DeligatePreparedStatementInfo.getInstance(_queryUpdateResult);
		this.psUpdateResult = DeligatePreparedStatementInfo.getStatement(	this.conn,
																			this.infoUpdateResult);

		this.initFlag = true;
	}

	/**
	 * release connection and statement <br>
	 * 데이터베이스 연결 및 Statement 반환.
	 */
	private final void releaseStatement() {
		this.initFlag = false;
		if (DebugEchoEvent.isEnable()) {
			logger.debug(resource.getBundle("task.release.statement"));
		}
		if (this.psUpdateLog != null) {
			try {
				this.psUpdateLog.close();
			}
			catch (Throwable thw) {
				logger.error(	resource.getBundle("release.resource.error"),
								thw);
			}
			this.psUpdateLog = null;
		}
		if (this.psUpdateResult != null) {
			try {
				this.psUpdateResult.close();
			}
			catch (Throwable thw) {
				logger.error(	resource.getBundle("release.resource.error"),
								thw);
			}
			this.psUpdateResult = null;
		}
//		if (this.conn != null) {
//			try {
//				this.conn.close();
//			}
//			catch (Throwable thw) {
//				logger.error(	resource.getBundle("release.resource.error"),
//								thw);
//			}
//			this.conn = null;
//		}
	}

	/**
	 * main logic <br>
	 * 메인 로직.
	 */
	@Override
	public void processMain() throws Throwable 
	{
		logger.info("task.processMain.Start");
		
		SMSReportBuffer report = BufferManager.getInstance().getSmsReportBuffer();
		
		this.connect(db_driver, db_url, db_id, db_pass);
		
		while (true) 
		{
			try 
			{
				if (this.innerProcess(report) > 0) 
				{
					this.getTimer().commit(System.currentTimeMillis());
				}
			}			
			catch (Throwable thw) 
			{
				logger.error("task.processMain.unknown.error", thw);
				
				try 
				{
					if (this.conn != null)
					{
						this.conn.close();
					}
				} 
				catch (Exception e) 
				{
					// TODO Auto-generated catch block					
				}
				
				this.conn = null;
				
				return;
			}

			if (report.isEmpty()) {
				report.waitOnPush(this.emptyBufferWait);
			}
		}
	}

	/**
	 * inner main logic <br>
	 * 내부 단위 실행 로직.
	 * 
	 * @param report
	 *            log buffer
	 * @return process count
	 * @throws Throwable
	 *             error occur
	 */
	private int innerProcess(SMSReportBuffer report) throws Throwable 
	{
		if (report.size() == 0) 
		{
			try 
			{
				String validationFlag = MagentConfig.getProperty("sms.logbuffer.validation.query", "false");
				
				if(validationFlag.toLowerCase().equals("true")) 
				{
					//2017.03.21 DB 커넥션 유지를 위한 검증 쿼리				
					this.infoSelectValidationQry = DeligatePreparedStatementInfo.getInstance(this.querySelectValidationQry);
					this.psSelectValidationQry = DeligatePreparedStatementInfo.getStatement(this.conn,this.infoSelectValidationQry);
											
					this.psSelectValidationQry.executeQuery(this.querySelectValidationQry);											
					this.psSelectValidationQry.close();
					
					this.psSelectValidationQry = null;
				}								
				
				if (DebugEchoEvent.isEnable()) 
				{
					logger.debug(resource.getBundle("logbuffer.empty.skip"));
				}
			}
			catch (SQLException sqe) 
			{
				logger.error("Validation SQLException ex : " + sqe.getMessage() + ", ErrorCode : " + sqe.getErrorCode() );
				
				dbReconnectValidation(sqe);

				sleep();
			}
			catch (Throwable thw) 
			{
				logger.error("validationQry init error", thw);
			}
									
			return 0;
		}
		
		if (DebugEchoEvent.isEnable()) {
			logger.debug(resource.getBundle("process.execute.start",
											this.getDesc()));
		}

		try {
			this.initStatement();
			SMSReportStruct struct = null;

			int count = 0;
			while ((struct = report.pop(10000)) != null) {
				Thread.sleep(20);
				if (!this.updateLog(struct)) {
					return count;
				}
				count++;
			}
			if (count > 0) {
				logger.debug(resource.getBundle("buffer.patch.complete",
												String.valueOf(count)));
			}
			else {
				if (DebugEchoEvent.isEnable()) {
					logger.debug(resource.getBundle("buffer.patch.complete",
													String.valueOf(count)));
				}
			}

			return count;
		}
		catch (Throwable thw) {
			throw thw;
		}
		finally {
			this.releaseStatement();
		}
	}

	/**
	 * validation when startup <br>
	 * 초기화 유효성검증.
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validate_SmsSourceTableScanTask() {
		this.queryUpdateLog = this.sql.getSql("sms-query-update-log");
		this.queryUpdateResult = this.sql.getSql("sms-query-update-result");
		
		this.querySelectValidationQry = this.sql.getSql("sms-query-select-validationQuery");

		ResourceTableManager.getInstance().getResourceName(this.resourceKey);

		BroadcastEventBroker.getInstance().registMessageListener(this.listener);
	}

	/**
	 * log table resource id <br>
	 * 로그 리소스 아이디.
	 */
	@MemberField(elementName = "resource-target-id", nullable = false)
	String resourceKey = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Driver
	 */
	@MemberField(elementName = "resource-driver", nullable = false)
	String db_driver = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Url
	 */
	@MemberField(elementName = "resource-url", nullable = false)
	String db_url = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Id
	 */
	@MemberField(elementName = "resource-id", nullable = false)
	String db_id = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Password
	 */
	@MemberField(elementName = "resource-pass", nullable = false)
	String db_pass = null;
}
