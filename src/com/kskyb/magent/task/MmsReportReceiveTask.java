package com.kskyb.magent.task;

import java.io.EOFException;

import org.apache.log4j.Logger;


import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.magent.AuthFailException;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.ServerCommunicationException;
import com.kskyb.magent.adapter.MMSAdapter;
import com.kskyb.magent.struct.BufferManager;
import com.kskyb.magent.struct.MMSReportBuffer;

/**
 * MMS report receive Task
 */
public class MmsReportReceiveTask
									extends
									AbstractGateWayTask {
	/**
	 * 
	 */
	private static final MessageResource resource = MessageResource.getResource(MmsReportReceiveTask.class);

	/**
	 * 
	 */
	private static final Logger logger = Logger.getLogger("mms_report");

	/**
	 * main logic <br>
	 * 메인로직.
	 */
	@Override
	public void processMain() throws Throwable {
		MMSReportBuffer result = BufferManager.getInstance().getMmsReportBuffer();
		// int eofCount = 0;
		boolean serverError = false;
		while (true) {
			try {
				if (this.adapter.receiveReport(	result,
												this.eofWaitCount,
												this.eofWaitInterval)) {
					this.getTimer().commit(System.currentTimeMillis());
				}
			}
			catch (EOFException EOF) {
				logger.error(	resource.getBundle("report.listen.error"),
								EOF);
				this.adapter.releaseConnection();

				try {
					Thread.sleep(this.eofDelayTime);
				}
				catch (Throwable thw) {
					if (DebugEchoEvent.isEnable()) {
						logger.error(	"thread sleep error",
										thw);
					}
				}

				continue;
			}
			catch (AuthFailException afe) {
				this.adapter.releaseConnection();
				try {
					Thread.sleep(this.authErrorWaitInterval);
				}
				catch (Throwable thw) {
					logger.error(	resource.getBundle("report.eof.wait.error"),
									thw);
				}
				continue;
			}
			catch (ServerCommunicationException sce) {
				if (DebugEchoEvent.isEnable()) {
					CommonLogGW.debug(	">>> DEBUG MmsReportReceiveTask.processMain>>>",
										new Throwable("releaseConnection call"));
				}
				this.adapter.releaseConnection();

				if (serverError) {
					try {
						Thread.sleep(this.serverErrorWaitInterval);
					}
					catch (Throwable thw) {
						logger.error(	resource.getBundle("report.eof.wait.error"),
										thw);
					}
				}
				serverError = true;
				continue;
			}
			catch (Throwable thw) {
				logger.error(	resource.getBundle("report.receive.error"),
								thw);
				if (DebugEchoEvent.isEnable()) {
					CommonLogGW.debug(	">>> DEBUG MmsReportReceiveTask.processMain>>>",
										new Throwable("releaseConnection call"));
				}
				this.adapter.releaseConnection();
				continue;
			}
		}
	}

	/**
	 * validation when startup <br>
	 * 초기화 유효성 검증.
	 */
	@ValidationMethod
	public final void validate_MmsReportReceiveTask() {
		if (this.isValidTask()) {
			this.adapter.startMonitorThread();
		}
	}

	/**
	 * gw adapter <br>
	 * GW 서버 어답터.
	 */
	@MemberField(nullable = false, elementName = "mms-gate")
	MMSAdapter adapter = null;
}
