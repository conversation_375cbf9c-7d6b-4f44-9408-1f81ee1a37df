package com.kskyb.magent.task;

import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.magent.KskybException;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.UtilDelegator;
import com.kskyb.magent.config.MagentConfig;
import com.kskyb.magent.config.ResourceTableManager;

/**
 * SMS DB Move task
 */
public class SmsLogMigrationTask extends AbstractDbTask_Direct 
{
	/**
	 * 
	 */
	private static final MessageResource resource = MessageResource.getResource(SmsLogMigrationTask.class);

	/**
	 * 
	 */
	private static final Logger logger = Logger.getLogger("sms_dbmove");

	/**
	 * 
	 */
	private static final boolean SQL_DEBUG = MagentConfig.getProperty(	"logging.sql",
																		"false").equalsIgnoreCase("true");		
	//private static final boolean SQL_DEBUG = MagentConfig.getApplicationProperty(	"logging.sql",
	//																				"false").equalsIgnoreCase("true");

	/**
	 * source table mapping index <br>
	 * 소스 테이블 매핑 인덱스
	 */
	private static final MappingEntry sourceEntry = new MappingEntry("#SOURCE_TABLE_NAME#");

	/**
	 * target table mapping index <br>
	 * before 타겟 테이블 매핑 인덱스.
	 */
	private static final MappingEntry beforeTrgetEntry = new MappingEntry("#BEFORE_TARGET_TABLE_NAME#");

	/**
	 * target table mapping index <br>
	 * 타겟 테이블 매핑 인덱스.
	 */
	private static final MappingEntry targetEntry = new MappingEntry("#TARGET_TABLE_NAME#");

	/**
	 * target table mapping index <br>
	 * before 타겟 테이블 매핑 인덱스.
	 */
	private static final MappingEntry afterTrgetEntry = new MappingEntry("#AFTER_TARGET_TABLE_NAME#");

	/**
	 * target table mapping index <br>
	 * 타겟 테이블 매핑 인덱스.
	 */
	private static final MappingEntry stampEntry = new MappingEntry("#TIME_STAMP#");

	/**
	 * target table mapping index <br>
	 * before 타겟 테이블 매핑 인덱스.
	 */
	private static final MappingEntry afterStampEntry = new MappingEntry("#AFTER_TIME_STAMP#");

	/**
	 * array of table mapping index <br>
	 * 테이블 매핑 인덱스 배열
	 */
	private static final Map.Entry<String, String>[] entrys = new Map.Entry[] {
		sourceEntry,
		targetEntry,
		beforeTrgetEntry,
		afterTrgetEntry,
		stampEntry,
		afterStampEntry
	};

	/**
	 * execute query <br>
	 * 수행 쿼리 참조자.
	 */
	protected String queryUpdateMigrationLog = null;

	/**
	 * execute query <br>
	 * 수행 쿼리 참조자.
	 */
	protected String queryCopyMigrationLogBefore = null;

	/**
	 * 
	 */
	protected String queryCopyMigrationLogCurrent = null;

	/**
	 * 
	 */
	protected String queryCopyMigrationLogAfter = null;

	/**
	 * execute query <br>
	 * 수행 쿼리 참조자.
	 */
	protected String queryDeleteMigrationLog = null;

	/**
	 * 
	 */
	SimpleDateFormat format = null;

	/**
	 * main logic <br>
	 * 메인 로직.
	 */
	@Override
	public void processMain() throws Throwable 
	{
		if (DebugEchoEvent.isEnable()) 
		{
			logger.debug(resource.getBundle("process.execute.start",
											this.getDesc()));
		}
		// AsyncHashMap map = null;
		StringBuffer buffer = null;

		Statement stmt = null;
		try 
		{
			//this.connect();
			//트랜잭션 처리를 위해서 DB 직접연결로 변경
			this.connect(db_driver, db_url, db_id, db_pass);
			
			//오토커밋 해제 후 DB 작업이 끝나면 커밋 처리 실패 시 롤백
			conn.setAutoCommit(false);
			
			ResourceTableManager manager = ResourceTableManager.getInstance();

			final long current = System.currentTimeMillis();
			final String resourceSourceName = manager.getResourceName(this.resourceSourceKey);
			final String resourceTargetName = manager.getResourceName(this.resourceTargetKey);
			final String resourceBeforeTargetName = manager.getResourceName(this.resourceTargetKey,
																			DateUtil.beforeMonth(current));
			final String resourceAfterTargetName = manager.getResourceName(	this.resourceTargetKey,
																			DateUtil.afterMonth(current));

			sourceEntry.setValue(resourceSourceName);
			targetEntry.setValue(resourceTargetName);
			beforeTrgetEntry.setValue(resourceBeforeTargetName);
			afterTrgetEntry.setValue(resourceAfterTargetName);

			final String timeStamp = DateUtil.getFormatDate(this.format, current);
			final String afterTimeStamp = DateUtil.afterMonth(	this.format, current);

			stampEntry.setValue(timeStamp);
			afterStampEntry.setValue(afterTimeStamp);

			// -- migrate log -- //
			// map = AsyncHashMap.getInstance();
			buffer = new StringBuffer();
			stmt = this.conn.createStatement();

			int targetCount = 0;
			buffer.setLength(0);
			UtilDelegator.convertString(buffer, this.queryUpdateMigrationLog, entrys);
			
			if (SQL_DEBUG) 
			{
				logger.debug("execute >> " + buffer.toString());
			}
			
			targetCount = stmt.executeUpdate(buffer.toString());

			if (targetCount == 0) 
			{
				if (DebugEchoEvent.isEnable()) 
				{
					logger.debug(resource.getBundle("migration.target.empty"));
				}
				
				return;
			}

			int copyCount = 0;
			buffer.setLength(0);
			
			UtilDelegator.convertString(buffer, this.queryCopyMigrationLogBefore, entrys);
			
			if (SQL_DEBUG) 
			{
				logger.debug("execute >> " + buffer.toString());
			}
			
			copyCount += stmt.executeUpdate(buffer.toString());

			buffer.setLength(0);
			
			UtilDelegator.convertString(buffer, this.queryCopyMigrationLogCurrent, entrys);
			
			if (SQL_DEBUG) 
			{
				logger.debug("execute >> " + buffer.toString());
			}
			
			copyCount += stmt.executeUpdate(buffer.toString());

			buffer.setLength(0);
			UtilDelegator.convertString(buffer, this.queryCopyMigrationLogAfter, entrys);
			
			if (SQL_DEBUG) 
			{
				logger.debug("execute >> " + buffer.toString());
			}
			
			copyCount += stmt.executeUpdate(buffer.toString());

			int deleteCount = 0;
			buffer.setLength(0);
			
			UtilDelegator.convertString(buffer, this.queryDeleteMigrationLog, entrys);
			
			if (SQL_DEBUG) 
			{
				logger.debug("execute >> " + buffer.toString());
			}
			
			deleteCount = stmt.executeUpdate(buffer.toString());

			if ((targetCount == copyCount) && (copyCount == deleteCount)) 
			{
				logger.info(resource.getBundle(	"success.migration.log", String.valueOf(targetCount)));
				
				conn.commit();
			}
			else 
			{
				conn.rollback();
				
				throw new KskybException(resource.getBundle("migration.result.count.different",
															String.valueOf(targetCount),
															String.valueOf(deleteCount),
															String.valueOf(copyCount)));								
			}
		}
		catch (Throwable thw) 
		{
			conn.rollback();
			
			if (thw instanceof SQLException) 
			{
				ResourceTableManager.getInstance().getResourceName(	this.resourceSourceKey, true);
				ResourceTableManager.getInstance().getResourceName(	this.resourceTargetKey, true);
			}
			
			throw thw;
		}
		finally 
		{
			if (stmt != null) 
			{
				try 
				{
					stmt.close();
				}
				catch (Throwable thw) 
				{
					logger.error(	"release.resource.error", thw);
				}
				stmt = null;
			}

			if (this.conn != null) 
			{
				try 
				{
					this.conn.close();
				}				
				catch (Throwable thw) 
				{
					logger.error(	"release.resource.error", thw);
				}
				this.conn = null;
			}
		}
	}

	/**
	 * validation when startup<br>
	 * 초기화 유효성 검증.
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validate_SmsLogMigrationTask() {
		this.format = new SimpleDateFormat(this.timestamp);

		this.queryUpdateMigrationLog = this.sql.getSql("sms-query-update-migration-log");
		this.queryCopyMigrationLogBefore = this.sql.getSql("sms-query-copy-migration-log-before");
		this.queryCopyMigrationLogCurrent = this.sql.getSql("sms-query-copy-migration-log-current");
		this.queryCopyMigrationLogAfter = this.sql.getSql("sms-query-copy-migration-log-after");
		this.queryDeleteMigrationLog = this.sql.getSql("sms-query-delete-migration-log");

		ResourceTableManager.getInstance().getResourceName(this.resourceSourceKey);
		ResourceTableManager.getInstance().getResourceName(this.resourceTargetKey);
	}

	/**
	 * source table resource id <br>
	 * 소스 테이블 리소스 아이디.
	 */
	@MemberField(elementName = "resource-source-id", nullable = false)
	String resourceSourceKey = null;

	/**
	 * target table resource id<br>
	 * 타겟 테이블 리소스 아이디.
	 */
	@MemberField(elementName = "resource-target-id", nullable = false)
	String resourceTargetKey = null;

	/**
	 * 
	 */
	@MemberField
	String timestamp = "yyyy-MM";
	
	/**
	 * DB Connection Info <br>
	 * DB Driver
	 */
	@MemberField(elementName = "resource-driver", nullable = false)
	String db_driver = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Url
	 */
	@MemberField(elementName = "resource-url", nullable = false)
	String db_url = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Id
	 */
	@MemberField(elementName = "resource-id", nullable = false)
	String db_id = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Password
	 */
	@MemberField(elementName = "resource-pass", nullable = false)
	String db_pass = null;

}
