package com.kskyb.magent.task;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.service.scheduler.ITimer;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.broker.util.ISelector;
import com.kskyb.broker.util.ISheet;
import com.kskyb.broker.util.MapDelegateSelector;
import com.kskyb.broker.util.jdbc.DeligatePreparedStatementInfo;
import com.kskyb.broker.util.jdbc.ResultSetTransferTableWrapper;
import com.kskyb.magent.AuthEvent;
import com.kskyb.magent.HasNoRcptListException;
import com.kskyb.magent.IAuthActor;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.UtilDelegator;
import com.kskyb.magent.config.MagentConfig;
import com.kskyb.magent.config.ResourceTableManager;
import com.kskyb.magent.struct.BufferManager;
import com.kskyb.magent.struct.MMSReportBuffer;
import com.kskyb.magent.struct.MMSSendBuffer;

/**
 * MMS source table scan task
 */
public class MmsSourceTableScanTask
									extends
									AbstractDbTask {
	/**
	 * 
	 */
	private static final MessageResource resource = MessageResource.getResource(MmsSourceTableScanTask.class);

	/**
	 * 
	 */
	private static final Logger logger = Logger.getLogger("mms_send");

	/**
	 * 
	 */
	private static final Logger tran = Logger.getLogger("mms_tran");

	private static final Logger common= Logger.getLogger("common");

	/**
	 * status code <br>
	 * 상태코드
	 */
	public static final String GROUP_INTO_BUFFER = "6";

	/**
	 * status code <br>
	 * 상태코드
	 */
	public static final String MSG_HAS_NO_RCPT_ERROR = "Y";

	/**
	 * status code <br>
	 * 상태코드
	 */
	public static final String MSG_ETC_ERROR = "1";

	/**
	 * content table mapping index <br>
	 * CONTENT 테이블 매핑 인덱스
	 */
	private static final Map.Entry<String, String> contentEntry = new Map.Entry<String, String>() {
		String value = null;

		@Override
		public String getKey() {
			return "#CONTENT_TABLE#";
		}

		@Override
		public String getValue() {
			return this.value;
		}

		@Override
		public String setValue(String _value) {
			String old = this.value;
			this.value = _value;
			return old;
		}

	};
	
	/**
	 * msg table mapping index <br>
	 * MSG 테이블 매핑 인덱스
	 */
	private static final Map.Entry<String, String> msgEntry = new Map.Entry<String, String>() {
		String value = null;

		@Override
		public String getKey() {
			return "#MSG_TABLE#";
		}

		@Override
		public String getValue() {
			return this.value;
		}

		@Override
		public String setValue(String _value) {
			String old = this.value;
			this.value = _value;
			return old;
		}

	};

	/**
	 * array of table mapping index <br>
	 * 테이블 매핑 인덱스 배열
	 */
	private static final Map.Entry<String, String>[] entrys = new Map.Entry[] {
		contentEntry,
		msgEntry
	};

	/**
	 * db statement <br>
	 * 쿼리 수행 Statement
	 */
	Statement stmt = null;

	/**
	 * execute info <br>
	 * 쿼리 수행 정보
	 */
	String infoSelectCheckedTarget = null;

	
	/**
	 * execute statement <br>
	 * 쿼리 수행 Statement
	 */
	Statement psSelectContents = null;

	/**
	 * execute statement <br>
	 * 쿼리 수행 Statement
	 */
	Statement psUpdatePushTarget = null;

	/**
	 * execute info <br>
	 * 쿼리 수행 정보
	 */
	DeligatePreparedStatementInfo infoSelectContentsTarget = null;

	/**
	 * execute info <br>
	 * 쿼리 수행 정보
	 */
	DeligatePreparedStatementInfo infoUpdatePushTarget = null;
	
		
	/**
	 * query reference <br>
	 * 쿼리 지정자.
	 */
	protected String queryUpdateOutofTTLMsg = null;

	/**
	 * query reference <br>
	 * 쿼리 지정자.
	 */
	protected String queryUpdatePushNoSendTarget = null;
	
	/**
	 * query reference <br>
	 * 쿼리 지정자.
	 */
	protected String queryUpdatePushTarget = null;

	/**
	 * query reference <br>
	 * 쿼리 지정자.
	 */
	protected String querySelectCheckedTarget = null;

	/**
	 * query reference <br>
	 * 쿼리 지정자.
	 */
	protected String querySelectContentsTarget = null;

	/**
	 * initialize flag <br>
	 * 초기화 플래그.
	 */
	boolean initFlag = false;

	/**
	 * initialize connection and statement <br>
	 * 자원 초기화.
	 * 
	 * @throws Exception
	 *             error occur
	 */
	private final void initStatement() throws Exception {
		if (this.initFlag) {
			return;
		}

		this.releaseStatement();

		this.connect();
		
		final String resourceNameOfContent = ResourceTableManager.getInstance().getResourceName(this.resourceContentKey);
		final String resourceNameOfMsg = ResourceTableManager.getInstance().getResourceName(this.resourceMsgKey);

		
		contentEntry.setValue(resourceNameOfContent);
		msgEntry.setValue(resourceNameOfMsg);

		this.infoSelectCheckedTarget = UtilDelegator.convertString(	this.querySelectCheckedTarget,
																	entrys);
		this.stmt = this.conn.createStatement();
	
		final String _querySelectContentsTarget = UtilDelegator.convertString(	this.querySelectContentsTarget,
																				entrys);
		this.infoSelectContentsTarget = DeligatePreparedStatementInfo.getInstance(_querySelectContentsTarget);
		this.psSelectContents = DeligatePreparedStatementInfo.getStatement(	this.conn,
																			this.infoSelectContentsTarget);

		final String _queryUpdatePushTarget = UtilDelegator.convertString(	this.queryUpdatePushTarget,
																			entrys);
		this.infoUpdatePushTarget = DeligatePreparedStatementInfo.getInstance(_queryUpdatePushTarget);
		this.psUpdatePushTarget = DeligatePreparedStatementInfo.getStatement(	this.conn,
																				this.infoUpdatePushTarget);
		this.initFlag = true;
	}

	/**
	 * release connection and statement <br>
	 * 자원반환.
	 */
	private final void releaseStatement() {
		this.initFlag = false;
		if (this.stmt != null) {
			try {
				this.stmt.close();
			}
			catch (Throwable e) {
				logger.error(	resource.getBundle("release.resource.error"),
								e);
			}
			this.stmt = null;
		}
		if (this.psSelectContents != null) {
			try {
				this.psSelectContents.close();
			}
			catch (Throwable e) {
				logger.error(	resource.getBundle("release.resource.error"),
								e);
			}
			this.psSelectContents = null;
		}		
		if (this.psUpdatePushTarget != null) {
			try {
				this.psUpdatePushTarget.close();
			}
			catch (Throwable e) {
				logger.error(	resource.getBundle("release.resource.error"),
								e);
			}
			this.psUpdatePushTarget = null;
		}

		if (this.conn != null) {
			try {
				this.conn.close();
			}
			catch (Throwable thw) {
				logger.error(	resource.getBundle("release.resource.error"),
								thw);
			}
		}
	}

	/**
	 * @param buffer
	 * @param suspendLog
	 * @return
	 * @throws Throwable
	 */
	private final boolean _process(	final MMSSendBuffer buffer,
									boolean suspendLog) throws Throwable {
		final ITimer localTimer = this.getTimer();
		if (buffer.isFull()) {
			logger.warn(resource.getBundle("send.buffer.full"));
			buffer.waitOnPop(60000);
			return suspendLog;
		}

		if (localTimer.isSuspend()) {
			if (suspendLog) {
				logger.debug(resource.getBundle("task.suspended",
												this.getDesc()));
				suspendLog = false;
			}

			try {
				synchronized (localTimer) {
					localTimer.wait(60000);
				}
			}
			catch (Throwable thw) {
				if (DebugEchoEvent.isEnable()) {
					logger.error(	"timer waiting error",
									thw);
				}
			}
			return suspendLog;
		}
		suspendLog = true;

		try {
			long current;
			if(validationHeartBeatTimer()) {
				this.initStatement();
				if (this.processMain(buffer) == 0) {
					try {
						synchronized (localTimer) {
							localTimer.wait(this.tableEmptyWait);
						}
					}
					catch (Throwable thw) {
						if (DebugEchoEvent.isEnable()) {
							logger.error(	"timer waiting error",
											thw);
						}
					}
				}
			}else {
				try {
					synchronized (localTimer) {
						localTimer.wait(this.tableEmptyWait);
					}
				}
				catch (Throwable thw) {
					if (DebugEchoEvent.isEnable()) {
						logger.error(	"timer waiting error",
										thw);
					}
				}
			}
		}
		catch (Throwable thw) {
			logger.error(	"task.inner.process.error",
							thw);
			if (thw instanceof SQLException) {
				this.releaseStatement();
			}
			try {
				synchronized (localTimer) {
					localTimer.wait(this.tableEmptyWait);
				}
			}
			catch (Throwable ignore) {
				if (DebugEchoEvent.isEnable()) {
					logger.error(	"timer waiting error",
									ignore);
				}
			}
		}
		finally {
			this.releaseStatement();

		}

		final int limit = buffer.getLimit();
		int size = buffer.size();
		if ((limit - size) < size) {
			logger.info(resource.getBundle(	"mms.send.buffer.shrink.wait.start",
											String.valueOf(size),
											String.valueOf(limit)));
			do {
				buffer.waitOnPop(10000);
				size = buffer.size();
			} while ((limit - size) < size);
			logger.info(resource.getBundle(	"mms.send.buffer.shrink.wait.finish",
											String.valueOf(size),
											String.valueOf(limit)));
		}
		return suspendLog;
	}

	/**
	 * main logic <br>
	 * 메인 수행로직.
	 */
	@Override
	public void processMain() throws Throwable {
		MMSSendBuffer buffer = BufferManager.getInstance().getMmsSendBuffer();

		// -- process out of ttl list
		if (this.ttlUpdate > 0) {
			//상태값 6->1 업데이트.2012.10.16.by han
			this.processNoSendDataUpdate();
			this.processOutOfTTL();
		}
		boolean suspendLog = true;
		final ITimer localTimer = this.getTimer();
		while (true) {
			try {
				suspendLog = this._process(	buffer,
											suspendLog);
			}
			catch (Throwable thw) {
				logger.error(	"process error",
								thw);
			}
		}
	}

	/**
	 * main inner logic <br>
	 * 내부 단위 수행로직.
	 * 
	 * @param buffer
	 *            send buffer
	 * @return process count
	 * @throws Throwable
	 *             error occur
	 */
	protected int processMain(MMSSendBuffer buffer) throws Throwable {
		if (DebugEchoEvent.isEnable()) {
			logger.info(resource.getBundle(	"process.execute.start",
											this.getDesc()));
		}
		final boolean useBuffer = MagentConfig.getProperty(	"mms.send.use.buffer",
															"true").equalsIgnoreCase("true");
		HashMap map = null;

		ISelector selector = null;

		ISheet selectorMsg = null;		
		ISheet selectorContents = null;

		ResultSet rs = null;
		
		try {
			map = new HashMap();
			map.putAll(this.addInfo);

			MMSReportBuffer grpLogBuffer = BufferManager.getInstance().getMmsReportBuffer();

			final String limit = String.valueOf(buffer.getLimit() - buffer.size());
			map.put("LIMIT",
					limit);

			if (DebugEchoEvent.isEnable()) {
				logger.debug(resource.getBundle("select.checked.target.start",
												limit));
			}

			//common.info("infoSelectCheckedTarget Start");
			
			rs = DeligatePreparedStatementInfo.executeQuery(this.infoSelectCheckedTarget,
															this.stmt,
															map,
															"${",
															"}");
			//common.info("infoSelectCheckedTarget End");

			selectorMsg = ResultSetTransferTableWrapper.getInstanceFromResultSet(	null,
																					rs);

			rs.close();
			rs = null;

			if (DebugEchoEvent.isEnable()) {
				logger.debug(resource.getBundle("select.checked.target.result.patch.start",
												String.valueOf(selectorMsg.getRecordCnt())));
			}

			selectorMsg.open();
			int patchCnt = 0;
			/**
			 * 선택된 그룹을 하나씩 패치하는 루틴
			 */
			Calendar calendar = Calendar.getInstance();
			SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");			
			
			while (selectorMsg.next(map)) {
				selector = MapDelegateSelector.getInstance(map);
				if (DebugEchoEvent.isEnable()) {
					tran.debug(resource.getBundle(	"patch.from.cache",
													selector.getParameter("TRAN_PR")));
				}
				
				patchCnt++;
				
				/**
				 * 첨부 테이블 가져오기
				 */
				//common.info("infoSelectContentsTarget Start");
				//contents 조회 원복 소스
				/*rs = DeligatePreparedStatementInfo.executeQuery(this.infoSelectContentsTarget,
																this.psSelectContents,
																selector); 
				//common.info("infoSelectContentsTarget End");
				if (DebugEchoEvent.isEnable()) {
					logger.info(resource.getBundle(	"select.content.list",
													selector.getParameter("TRAN_PR")));
				}
				selectorContents = ResultSetTransferTableWrapper.getInstanceFromResultSet(null, rs);	
				ObjectFinalizer.close(rs);
				rs = null;*/
				selectorContents = null;
				

				try {
					/**
					 * 버퍼에 메시지 삽입 전 MSG 테이블 6(버퍼삽입완료)로 업데이트
					 */
					//common.info("infoUpdatePushTarget Start");
					
					selector.setParameter("TRAN_SENDDATE", dateFormat.format(calendar.getTime()));
					
					//send table 6 update
					/*final int cnt = DeligatePreparedStatementInfo.executeUpdate(this.infoUpdatePushTarget,
																				this.psUpdatePushTarget,
																				selector); */
					int cnt = 1;
					//common.info("infoUpdatePushTarget End");
					
					// 전화번호서 "-" 삭제 
					String filteringPhone = selector.getParameter("TRAN_PHONE");
					String filteringCallback = selector.getParameter("TRAN_CALLBACK");
					
					selector.setParameter("TRAN_PHONE", filteringPhone.replaceAll("[-]", ""));
					
					if(filteringCallback != null ||  !filteringCallback.equals("")) {
						selector.setParameter("TRAN_CALLBACK", filteringCallback.replaceAll("[-]", ""));						
					}
					

					if (cnt == 1) {
						buffer.pushLog(	selector, selectorContents);
					}
					else {
						logger.warn(resource.getBundle(	"push.grp.info.update.fail",
														selector.getParameter("TRAN_PR"),
														String.valueOf(cnt)));
					}

				}
				catch (Throwable thw) {
					logger.error(	"send push buffer error",
									thw);
					grpLogBuffer.setMsgLog(	map.get("TRAN_PR").toString(),
												(thw instanceof HasNoRcptListException)	? MSG_HAS_NO_RCPT_ERROR
																						: MSG_ETC_ERROR,
												thw.toString(),
												useBuffer);
				}

				if (DebugEchoEvent.isEnable()) {
					tran.debug("put buffer >> " + map.get("TRAN_PR"));
				}

				ObjectFinalizer.close(selectorContents);
				ObjectFinalizer.close(selector);

				selectorContents = null;
				selector = null;

			}

			if (patchCnt > 0) {
				logger.debug(resource.getBundle("select.checked.target.result.patch.end",
												String.valueOf(patchCnt)));
			}
			else {
				if (DebugEchoEvent.isEnable()) {
					logger.debug(resource.getBundle("select.checked.target.result.patch.end",
													String.valueOf(patchCnt)));
				}
			}
			
			ObjectFinalizer.close(selectorMsg);
			selectorMsg = null;
			
			return patchCnt;
		}
		finally {
			ObjectFinalizer.close(selector);
			ObjectFinalizer.close(selectorMsg);
			ObjectFinalizer.close(selectorContents);

			ObjectFinalizer.close(rs);
		}
	}

	/**
	 * process outof ttl list process <br>
	 * 상태값 4,6 -> 1번으로 업데이트 처리 수행.
	 * 
	 * @throws Exception
	 *             error occur
	 */
	final void processNoSendDataUpdate() throws Exception {
		try {
			this.initStatement();
			final String _queryUpdatePushNoSendTarget = UtilDelegator.convertString(	this.queryUpdatePushNoSendTarget,
																				entrys);

			int cntNoSendData = this.stmt.executeUpdate(_queryUpdatePushNoSendTarget);

			if (DebugEchoEvent.isEnable()) {
				logger.warn(resource.getBundle(	"update.nosend.data.update",
												String.valueOf(cntNoSendData)));
			}
		}
		catch (Throwable thw) {
			logger.error(	resource.getBundle("process.nosend.data.update.error"),
							thw);
		}
		finally {
			this.releaseStatement();
		}
	}

	/**
	 * process outof ttl list process <br>
	 * TTL 오버 리스트 처리 수행.
	 * 
	 * @throws Exception
	 *             error occur
	 */
	final void processOutOfTTL() throws Exception {
		try {
			this.initStatement();					
			
			final String _queryUpdateOutofTTLMsg = UtilDelegator.convertString(this.queryUpdateOutofTTLMsg, entrys);
			int cntMarkGroup = this.stmt.executeUpdate(_queryUpdateOutofTTLMsg);

			if (cntMarkGroup == 0) {
				return;
			}

			if (DebugEchoEvent.isEnable()) {
				logger.info(resource.getBundle("mark.out.of.ttl.group",
						String.valueOf(cntMarkGroup)));
			}
			
		}
		catch (Throwable thw) {
			logger.error(	resource.getBundle("process.out.of.ttl.error"),
							thw);
		}
		finally {
			this.releaseStatement();
		}
	}

	/**
	 * 
	 */
	final AuthEvent.Listener listener = new AuthEvent.Listener(this) {

		/**
		 * @see com.kskyb.magent.AuthEvent.Listener#isTarget(int)
		 */
		@Override
		protected boolean isTarget(int actorType) {
			return actorType == IAuthActor.MMS;
		}
	};

	/**
	 * validation when startup <br>
	 * 초기화 유효성 검증.
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validateMmsSourceTableScanTask() {
		// -- out of ttl		
		this.queryUpdatePushNoSendTarget = this.sql.getSql("mms-query-update-pushed-nosend-target");
		
		//MSG TABLE에서 TRAN_DATE가 일정 시간 이상 지난 메시지는 3(REPORT수신완료)으로 UPDATE
		this.queryUpdateOutofTTLMsg = this.sql.getSql("mms-query-update-out-of-ttl-msg");
		
		// 테이블 스캔
		this.querySelectCheckedTarget = this.sql.getSql("mms-query-select-send-target");		
		this.querySelectContentsTarget = this.sql.getSql("mms-query-select-contents-target");

		this.queryUpdatePushTarget = this.sql.getSql("mms-query-update-pushed-target");
	}

	public final boolean validationHeartBeatTimer() {

		int exceptStartTime = -1;
		int exceptEndTime = -1;
		int from = 0;
		int to = 0;
		if (this.exceptTime == null) {
			return true;
		}

		int idx = this.exceptTime.indexOf('~');

		if (idx < 0) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"invalid.except.time",
																			this.exceptTime));
		}
		try {
			from = Integer.parseInt(this.exceptTime.substring(0, idx).trim());
			to = Integer.parseInt(this.exceptTime.substring(idx + 1).trim());
		}
		catch (Throwable thw) {
			throw new RuntimeException(	ResourceContext.getBundledMessage(	"invalid.except.time.parse.error",
																			this.exceptTime),
										thw);
		}

		if (from == to) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"invalid.except.time",
																			this.exceptTime));
		}

		long now = System.currentTimeMillis();
		SimpleDateFormat simple_format = new SimpleDateFormat("HHmm");
		String checkTime = DateUtil.getFormatDate(	simple_format, now);
		int iTime = Integer.parseInt(checkTime);
		boolean intime = false;
		
		if (from < to) {
			exceptStartTime = from;
			exceptEndTime = to;
			if (exceptStartTime < 0 || exceptEndTime < 0) {
				return true;
			}
			//iTime = 10;
			if (exceptStartTime <= iTime && iTime <= exceptEndTime) {
				return false;
			}
			else {
				return true;
			}
		}
		else {
			exceptStartTime = to;
			exceptEndTime = from;
			if (exceptStartTime < 0 || exceptEndTime < 0) {
				return true;
			}
			//iTime = 10;
			if (exceptStartTime <= iTime && iTime <= exceptEndTime) {
				return true;
			}
			else {
				return false;
			}
		}
		
	}

	/**
	 * content table resource id <br>
	 * CONTENT 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-content-id", nullable = false)
	String resourceContentKey = null;
	
	/**
	 * msg table resource id <br>
	 * MSG 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-msg-id", nullable = false)
	String resourceMsgKey = null;

	/**
	 * wait interval when target table empty <br>
	 * 대상 테이블 값 없을 경우 대기시간.
	 */
	@MemberField(elementName = "table-empty-wait")
	long tableEmptyWait = 5000L;

	/**
	 * outof ttl interval <br>
	 * out of ttl 시간
	 */
	@MemberField(elementName = "update-outof-ttl", nullable = false)
	int ttlUpdate = 0;
	@MemberField(elementName = "except-time2")
	String exceptTime = null;
	// /**
	// * auth actor list <br>
	// * 인증 수행자 리스트.
	// */
	// final ArrayList<IAuthActor> actors = new ArrayList<IAuthActor>();
	//
	// /**
	// * auth fail event notify <br>
	// * 인증실패 이벤트 알림.
	// */
	// @Override
	// public void authFail() {
	// for (Iterator<IAuthActor> iter = this.actors.iterator(); iter.hasNext();) {
	// final IAuthActor actor = iter.next();
	//
	// if (actor.isLogin()) {
	// return;
	// }
	// }
	// this.getTimer().suspend();
	// }
	//
	// /**
	// * auth ok envent notify <br>
	// * 인증성공 이벤트 알림.
	// */
	// @Override
	// public void authOk() {
	// this.getTimer().resume();
	//
	// }
	//
	// /**
	// * add auth actor <br>
	// * 인증수행자 추가.
	// */
	// @Override
	// public void setActor(IAuthActor actor) {
	// if (!this.actors.contains(this)) {
	// this.actors.add(actor);
	// actor.addListener(this);
	// }
	// }
}
