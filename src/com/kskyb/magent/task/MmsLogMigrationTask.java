package com.kskyb.magent.task;

import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kskyb.broker.adapter.jdbc.SQLExceptionFilter;
import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.magent.KskybException;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.UtilDelegator;
import com.kskyb.magent.config.MagentConfig;
import com.kskyb.magent.config.ResourceTableManager;

/**
 * MMS DB Move task
 */
public class MmsLogMigrationTask extends AbstractDbTask_Direct 
{
	private static final MessageResource resource = MessageResource.getResource(MmsLogMigrationTask.class);

	private static final Logger logger = Logger.getLogger("mms_dbmove");

	private static final boolean SQL_DEBUG = MagentConfig.getProperty(	"logging.sql", "false").equalsIgnoreCase("true");

	/**
	 * source table mapping index <br>
	 * 원본 테이블 매핑인덱스.
	 */	

	private static final MappingEntry msgSourceEntry = new MappingEntry("#MSG_SOURCE_TABLE#");

	private static final MappingEntry msgBeforeTargetEntry = new MappingEntry("#MSG_BEFORE_TARGET_TABLE#");

	private static final MappingEntry msgCurrentTargetEntry = new MappingEntry("#MSG_CURRENT_TARGET_TABLE#");

	private static final MappingEntry msgAfterTargetEntry = new MappingEntry("#MSG_AFTER_TARGET_TABLE#");

	private static final MappingEntry contentSourceEntry = new MappingEntry("#CONTENT_SOURCE_TABLE#");

	private static final MappingEntry contentBeforeTargetEntry = new MappingEntry("#CONTENT_BEFORE_TARGET_TABLE#");

	private static final MappingEntry contentCurrentTargetEntry = new MappingEntry("#CONTENT_CURRENT_TARGET_TABLE#");

	private static final MappingEntry contentAfterTargetEntry = new MappingEntry("#CONTENT_AFTER_TARGET_TABLE#");

	/**
	 * target table mapping index <br>
	 * 타겟 테이블 매핑 인덱스.
	 */
	private static final MappingEntry stampEntry = new MappingEntry("#TIME_STAMP#");

	/**
	 * target table mapping index <br>
	 * before 타겟 테이블 매핑 인덱스.
	 */
	private static final MappingEntry afterStampEntry = new MappingEntry("#AFTER_TIME_STAMP#");

	/**
	 * array of table mapping index <br>
	 * 테이블 매핑인덱스 배열
	 */
	private static final Map.Entry<String, String>[] entrys = new Map.Entry[] 
			{	
		msgSourceEntry,
		msgBeforeTargetEntry,
		msgCurrentTargetEntry,
		msgAfterTargetEntry, // -- msg -- //
		contentSourceEntry,
		contentBeforeTargetEntry,
		contentCurrentTargetEntry,
		contentAfterTargetEntry, // -- content --//
		stampEntry,
		afterStampEntry
	};

	SimpleDateFormat format = null;

	/**
	 * query <br>
	 * 수행쿼리
	 */
	protected String queryUpdateMsg = null;

	/**
	 * query <br>
	 * 수행쿼리
	 */
	protected String queryCopyMsgBefore = null;

	protected String queryCopyMsgCurrent = null;

	protected String queryCopyMsgAfter = null;

	/**
	 * query <br>
	 * 수행쿼리
	 */
	protected String queryDeleteMsg = null;

	/**
	 * query <br>
	 * 수행쿼리
	 */
	
	/**
	 * query <br>
	 * 수행쿼리
	 */
	protected String queryUpdateContent = null;

	/**
	 * query <br>
	 * 수행쿼리
	 */
	protected String queryCopyContentCurrent = null;

	/**
	 * 
	 */
	protected String queryCopyContentBefore = null;

	/**
	 * 
	 */
	protected String queryCopyContentAfter = null;

	/**
	 * query <br>
	 * 수행쿼리
	 */
	protected String queryDeleteContent = null;

	/**
	 * main logic <br>
	 * 메인 실행 로직.
	 */
	@Override
	public void processMain() throws Throwable 
	{
		if (DebugEchoEvent.isEnable()) 
		{
			logger.info(resource.getBundle(	"process.execute.start", this.getDesc()));
		}

		final long current = System.currentTimeMillis();
		final long beforeTime = DateUtil.beforeMonth(current);
		final long afterTime = DateUtil.afterMonth(current);
		ResourceTableManager manager = ResourceTableManager.getInstance();

		Statement stmt = null;
		try 
		{
			//this.connect();
			//트랜잭션 처리를 위해서 DB 직접연결로 변경
			this.connect(db_driver, db_url, db_id, db_pass);
			
			//오토커밋 해제 후 DB 작업이 끝나면 커밋 처리 실패 시 롤백
			conn.setAutoCommit(false);
			
			//20150310 Auto commit 해제
			//conn.setAutoCommit(false);
			
			stmt = this.conn.createStatement();
			
			msgSourceEntry.setValue(manager.getResourceName(this.resourceSourceMsgKey));
			msgCurrentTargetEntry.setValue(manager.getResourceName(this.resourceTargetMsgKey));
			msgBeforeTargetEntry.setValue(manager.getResourceName(	this.resourceTargetMsgKey, beforeTime));
			msgAfterTargetEntry.setValue(manager.getResourceName(	this.resourceTargetMsgKey, afterTime)); 

			contentSourceEntry.setValue(manager.getResourceName(this.resourceSourceContentKey));
			contentCurrentTargetEntry.setValue(manager.getResourceName(this.resourceTargetContentKey));
			contentBeforeTargetEntry.setValue(manager.getResourceName(	this.resourceTargetContentKey, beforeTime));
			contentAfterTargetEntry.setValue(manager.getResourceName(	this.resourceTargetContentKey, afterTime));

			stampEntry.setValue(DateUtil.getFormatDate(	this.format, current));
			afterStampEntry.setValue(DateUtil.getFormatDate(this.format, afterTime));

			if (DebugEchoEvent.isEnable()) 
			{
				logger.debug(resource.getBundle("start.content.table.migration"));
			}
			
			this.migrateTableMsg(stmt);
			
			conn.commit();
		}
		catch (SQLException sqe) 
		{
			logger.error(	"task.inner.process.error", sqe);
			
			conn.rollback();
			
			switch (this.filter.analize(sqe)) 
			{
				case SQLExceptionFilter.CONNECT_ERROR: 
				{
					break;
				}

				case SQLExceptionFilter.OBJECT_ERROR: 
				{					

					manager.getResourceName(this.resourceSourceMsgKey, true);
					manager.getResourceName(this.resourceTargetMsgKey, current, true);
					manager.getResourceName(this.resourceTargetMsgKey, beforeTime, true);
					manager.getResourceName(this.resourceTargetMsgKey, afterTime, true);
					manager.getResourceName(this.resourceSourceContentKey, true);
					manager.getResourceName(this.resourceTargetContentKey, current, true);
					manager.getResourceName(this.resourceTargetContentKey, beforeTime, true);
					manager.getResourceName(this.resourceTargetContentKey, afterTime, true);
					break;
				}

				default: 
				{
					break;
				}
			}
			return;
		}
		catch (Throwable thw) 
		{
			logger.error(	"execute error", thw);
			conn.rollback();
			throw thw;
		}
		finally 
		{
			if (stmt != null) 
			{
				try 
				{
					stmt.close();
				}
				catch (Throwable thw) 
				{
					logger.error(	"release.resource.error", thw);
				}
				stmt = null;
			}
			if (this.conn != null) 
			{
				try 
				{
					this.conn.close();
				}
				catch (Throwable thw) 
				{
					logger.error(	"release.resource.error", thw);
				}
				this.conn = null;
			}
		}
	}

	/**
	 * DB Move tran table <br>
	 * TRAN 테이블 DB Move
	 * 
	 * @param stmt
	 *            statement
	 * @throws Exception
	 *             error occur
	 */
	private synchronized final void migrateTable(Statement stmt) throws Exception {

		
	}

	/**
	 * DB Move group and content table <br>
	 * GROUP, CONTENT 테이블 DB Move
	 * 
	 * @param stmt
	 *            statement
	 * @throws Exception
	 *             error occur
	 */
	private synchronized final void migrateTableMsg(Statement stmt) throws Exception 
	{
		int targetCountMsg = 0;
		{
			final String _queryUpdate = UtilDelegator.convertString(this.queryUpdateMsg, entrys);

			if (SQL_DEBUG) 
			{
				logger.debug("mark execute >> " + _queryUpdate);
			}
			
			targetCountMsg = stmt.executeUpdate(_queryUpdate);
		}

		if (targetCountMsg == 0) 
		{
			if (DebugEchoEvent.isEnable()) 
			{
				logger.info(resource.getBundle(	"migration.target.empty", "GROUP-CONTENT"));
			}
			return;
		}

		int targetCountContent = 0;
		{
			final String _queryUpdate = UtilDelegator.convertString(this.queryUpdateContent, entrys);

			if (SQL_DEBUG) 
			{
				logger.debug("mark execute >> " + _queryUpdate);
			}

			targetCountContent = stmt.executeUpdate(_queryUpdate);

		}

		boolean processContent = targetCountContent > 0;

		int copyCountContent = 0;
		if (processContent) 
		{
			String _queryCopy = null;

			_queryCopy = UtilDelegator.convertString(	this.queryCopyContentBefore, entrys);

			if (SQL_DEBUG) {
				logger.debug("copy execute >> " + _queryCopy);
			}

			copyCountContent += stmt.executeUpdate(_queryCopy);


			_queryCopy = UtilDelegator.convertString(	this.queryCopyContentCurrent, entrys);

			if (SQL_DEBUG) {
				logger.debug("copy execute >> " + _queryCopy);
			}

			copyCountContent += stmt.executeUpdate(_queryCopy);


			_queryCopy = UtilDelegator.convertString(	this.queryCopyContentAfter, entrys);

			if (SQL_DEBUG) 
			{
				logger.debug("copy execute >> " + _queryCopy);
			}
			
			copyCountContent += stmt.executeUpdate(_queryCopy);
			
		}

		int deleteCountContent = 0;
		
		if (processContent) 
		{
			final String _queryDelete = UtilDelegator.convertString(this.queryDeleteContent, entrys);

			if (SQL_DEBUG) 
			{
				logger.debug("delete execute >> " + _queryDelete);
			}
			
			deleteCountContent = stmt.executeUpdate(_queryDelete);
			
		}

		int copyCountMsg = 0;
		{
			String _queryCopy = null;

			_queryCopy = UtilDelegator.convertString(	this.queryCopyMsgBefore, entrys);

			if (SQL_DEBUG) 
			{
				logger.debug("copy execute >> " + _queryCopy);
			}
			
			copyCountMsg += stmt.executeUpdate(_queryCopy);
			

			_queryCopy = UtilDelegator.convertString(	this.queryCopyMsgCurrent, entrys);

			if (SQL_DEBUG) 
			{
				logger.debug("copy execute >> " + _queryCopy);
			}
			
			copyCountMsg += stmt.executeUpdate(_queryCopy);
			

			_queryCopy = UtilDelegator.convertString(	this.queryCopyMsgAfter, entrys);

			if (SQL_DEBUG) 
			{
				logger.debug("copy execute >> " + _queryCopy);
			}
			
			copyCountMsg += stmt.executeUpdate(_queryCopy);
			
		}

		int deleteCountMsg = 0;
		{
			final String _queryDelete = UtilDelegator.convertString(this.queryDeleteMsg, entrys);
			
			if (SQL_DEBUG) 
			{
				logger.debug("delete execute >> " + _queryDelete);
			}
			
			deleteCountMsg = stmt.executeUpdate(_queryDelete);
			
		}
		if ((copyCountMsg == deleteCountMsg) && (copyCountContent == deleteCountContent)) {
			logger.info(resource.getBundle(	"success.migration.log",
											"CONTENT",
											String.valueOf(copyCountContent)));
			logger.info(resource.getBundle(	"success.migration.log",
											"GROUP",
											String.valueOf(copyCountMsg)));
		}
		else {			
			
			logger.error(resource.getBundle("fail.group.log",
											"GROUP",
											String.valueOf(copyCountMsg),
											String.valueOf(deleteCountMsg)));
			logger.error(resource.getBundle("fail.content.log",
											"CONTENT",
											String.valueOf(copyCountContent),
											String.valueOf(deleteCountContent)));			
			throw new KskybException(resource.getBundle("migration.result.count.different",
														"GROUP-CONTENT",
														String.valueOf(copyCountMsg),
														String.valueOf(deleteCountMsg),
														String.valueOf(copyCountContent),
														String.valueOf(deleteCountContent)));

		}		
		
	}

	/**
	 * validation when startup <br>
	 * 초기화 유효성 검증.
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validate_MmsLogMigrationTask() {
		this.format = new SimpleDateFormat(this.timestamp);

		this.queryUpdateMsg = this.sql.getSql("mms-update-msg-migration-target");
		this.queryCopyMsgBefore = this.sql.getSql("mms-copy-msg-migration-target-before");
		this.queryCopyMsgCurrent = this.sql.getSql("mms-copy-msg-migration-target-current");
		this.queryCopyMsgAfter = this.sql.getSql("mms-copy-msg-migration-target-after");
		this.queryDeleteMsg = this.sql.getSql("mms-delete-msg-migration-target");
				
		this.queryUpdateContent = this.sql.getSql("mms-update-content-migration-target");
		this.queryCopyContentBefore = this.sql.getSql("mms-copy-content-migration-target-before");
		this.queryCopyContentCurrent = this.sql.getSql("mms-copy-content-migration-target-current");
		this.queryCopyContentAfter = this.sql.getSql("mms-copy-content-migration-target-after");
		this.queryDeleteContent = this.sql.getSql("mms-delete-content-migration-target");

		ResourceTableManager.getInstance().getResourceName(this.resourceSourceMsgKey);
		ResourceTableManager.getInstance().getResourceName(this.resourceTargetMsgKey);		
		ResourceTableManager.getInstance().getResourceName(this.resourceSourceContentKey);
		ResourceTableManager.getInstance().getResourceName(this.resourceTargetContentKey);
	}

	@MemberField
	String timestamp = "yyyy-MM";
	
	/**
	 * source msg table resource id <br>
	 * MSG SOURCE 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-msg-source-id", nullable = false)
	String resourceSourceMsgKey = null;

	/**
	 * target msg table resource id <br>
	 * MSG TARGET 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-msg-target-id", nullable = false)
	String resourceTargetMsgKey = null;

		
	/**
	 * source content table resource id <br>
	 * CONTENT SOURCE 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-content-source-id", nullable = false)
	String resourceSourceContentKey = null;

	/**
	 * target content table resource id <br>
	 * CONTENT TARGET 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-content-target-id", nullable = false)
	String resourceTargetContentKey = null;
	
	//디비 직접 연결 관련 추가 멤버필드 2017.08.01
		/**
		 * DB Connection Info <br>
		 * DB Driver
		 */
		@MemberField(elementName = "resource-driver", nullable = false)
		String db_driver = null;
		
		/**
		 * DB Connection Info <br>
		 * DB Url
		 */
		@MemberField(elementName = "resource-url", nullable = false)
		String db_url = null;
		
		/**
		 * DB Connection Info <br>
		 * DB Id
		 */
		@MemberField(elementName = "resource-id", nullable = false)
		String db_id = null;
		
		/**
		 * DB Connection Info <br>
		 * DB Password
		 */
		@MemberField(elementName = "resource-pass", nullable = false)
		String db_pass = null;
}
