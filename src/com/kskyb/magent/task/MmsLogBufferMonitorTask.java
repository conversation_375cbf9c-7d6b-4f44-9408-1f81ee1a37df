package com.kskyb.magent.task;

import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kskyb.broker.adapter.jdbc.SQLExceptionFilter;
import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.BroadcastEventBroker;
import com.kskyb.broker.event.BroadcastEventListener;
import com.kskyb.broker.event.BroadcastEventListenerAdapter;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.service.pool.db.ConnectionInvalidException;
import com.kskyb.broker.util.ISelector;
import com.kskyb.broker.util.jdbc.DeligatePreparedStatementInfo;
import com.kskyb.magent.KskybException;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.UtilDelegator;
import com.kskyb.magent.config.MagentConfig;
import com.kskyb.magent.config.ResourceTableManager;
import com.kskyb.magent.struct.BufferManager;
import com.kskyb.magent.struct.MMSReportBuffer;
import com.kskyb.magent.struct.MMSReportStruct;

/**
 * MMS 로그, 리포트 버퍼를 처리하는 Task
 */
public class MmsLogBufferMonitorTask extends AbstractDbTask_Direct 
{

	private static final MessageResource resource = MessageResource.getResource(MmsLogBufferMonitorTask.class);

	private static final Logger logger = Logger.getLogger("mms_report");
	private static final Logger common= Logger.getLogger("common");

	private static final boolean DEBUG = true;

	private static final MappingEntry contentEntry = new MappingEntry("#CONTENT_TABLE#");
	private static final MappingEntry msgEntry = new MappingEntry("#MSG_TABLE#");
	
	private static final Map.Entry<String, String>[] entrys = new Map.Entry[] {		
		contentEntry,
		msgEntry
	};
	
	DeligatePreparedStatementInfo infoTranUpdateStart = null;
	DeligatePreparedStatementInfo infoMsgUpdateLog = null;
	DeligatePreparedStatementInfo infoUpdateLog = null;
	DeligatePreparedStatementInfo infoUpdateResult = null;
	
	Statement psTranUpdateStart = null;
	Statement psMsgUpdateLog = null;
	Statement psUpdateLog = null;
	Statement psUpdateResult = null;

	DeligatePreparedStatementInfo infoSelectValidationQry = null;
	Statement psSelectValidationQry = null;
	
	
	
	protected String queryMsgUpdateLog = null;
	protected String queryTranUpdateStart = null;
	protected String queryUpdateResult = null;
	protected String querySelectValidationQry = null;
	
	boolean initFlag = false;
	
	private final void initStatement(MMSReportStruct struct) throws Exception 
	{
		if (this.initFlag) 
		{
			return;
		}

		this.releaseStatement();

		//this.connect();		
		//this.connect(db_driver, db_url, db_id, db_pass);				

		final String tableContentName = ResourceTableManager.getInstance().getResourceName(this.resourceContentKey);
		contentEntry.setValue(tableContentName);
		
		final String tableMsgName = ResourceTableManager.getInstance().getResourceName(this.resourceMsgKey);
		msgEntry.setValue(tableMsgName);
		
		
		//2012.09.05.by han
		if (struct.getType() == MMSReportStruct.TYPE_MSG_RESULT)
		{
			final String _queryUpdateResult = UtilDelegator.convertString(	this.queryUpdateResult,
					entrys);
			this.infoUpdateResult = DeligatePreparedStatementInfo.getInstance(_queryUpdateResult);
			this.psUpdateResult = DeligatePreparedStatementInfo.getStatement(	this.conn,
																				this.infoUpdateResult);
		}
		//2012.09.05.by han
		//2015.03.18 modified by LSJ
		if (struct.getType() == MMSReportStruct.TYPE_MSG_LOG)
		{
			
			final String _queryMsgUpdateLog = UtilDelegator.convertString(	this.queryMsgUpdateLog,
					entrys);
			this.infoMsgUpdateLog = DeligatePreparedStatementInfo.getInstance(_queryMsgUpdateLog);
			this.psMsgUpdateLog = DeligatePreparedStatementInfo.getStatement(	this.conn,
																				this.infoMsgUpdateLog);
		}
		//2012.09.05.by han
		if (struct.getType() == MMSReportStruct.TYPE_MSG_START_LOG)
		{
			final String _queryTranUpdateStart = UtilDelegator.convertString(	this.queryTranUpdateStart,
					entrys);
			this.infoTranUpdateStart = DeligatePreparedStatementInfo.getInstance(_queryTranUpdateStart);
			this.psTranUpdateStart = DeligatePreparedStatementInfo.getStatement(this.conn,
																				this.infoTranUpdateStart);
		}
		this.initFlag = true;
	}

	/**
	 * release connection and statement <br>
	 * 데이터베이스 자원 반환.
	 */
	private final void releaseStatement() {
		this.initFlag = false;
		// -- 자원반환 -- //
		if (this.psUpdateLog != null) {
			try {
				this.psUpdateLog.close();
			}
			catch (Throwable thw) {
				logger.error(	"release.db.resource.error",
								thw);
			}
			this.psUpdateLog = null;
		}
		if (this.psUpdateResult != null) {
			try {
				this.psUpdateResult.close();
			}
			catch (Throwable thw) {
				logger.error(	"release.db.resource.error",
								thw);
			}
			this.psUpdateResult = null;
		}
		if (this.psMsgUpdateLog != null) {
			try {
				this.psMsgUpdateLog.close();
			}
			catch (Throwable thw) {
				logger.error(	"release.db.resource.error",
								thw);
			}
			this.psMsgUpdateLog = null;
		}
		if (this.psTranUpdateStart != null) {
			try {
				this.psTranUpdateStart.close();
			}
			catch (Throwable thw) {
				logger.error(	"release.db.resource.error",
								thw);
			}
			this.psTranUpdateStart = null;
		}		
	}

	/**
	 * main logic <br>
	 * 실행 마스터 로직.
	 */
	@Override
	public void processMain() throws Throwable 
	{
		logger.info("task.processMain.Start");
		
		MMSReportBuffer report = BufferManager.getInstance().getMmsReportBuffer();
		
		this.connect(db_driver, db_url, db_id, db_pass);
		
		while (true) 
		{
			try 
			{
				if (this.innerProcess(report) > 0) 
				{
					this.getTimer().commit(System.currentTimeMillis());
				}
			}			
			catch (Throwable thw) 
			{
				logger.error("task.processMain.unknown.error", thw);
				
				try 
				{
					if (this.conn != null)						
						this.conn.close();
				} 
				catch (Exception e) 
				{
					// TODO Auto-generated catch block					
				}
				
				return;
			}

			if (report.isEmpty()) {
				report.waitOnPush(this.emptyBufferWait);
			}
		}
	}

	/**
	 * 
	 */
	private final BroadcastEventListener<MMSReportStruct> listener = new BroadcastEventListenerAdapter<MMSReportStruct>(MMSReportStruct.class) {

		/**
		 * @see com.kskyb.broker.event.BroadcastEventListenerAdapter#fire(com.kskyb.broker.event.BroadcastEvent)
		 */
		@Override
		protected void fire(MMSReportStruct event) {
			try {
				MmsLogBufferMonitorTask.this.processLog(event);
			}
			catch(Throwable thw){
			
			}
		}

	};

	/**
	 * @param struct
	 * @return
	 */
	private synchronized final boolean processLog(final MMSReportStruct struct) throws Throwable 
	{
		ISelector selector = null;
		
		try 
		{
			selector = struct.getSelector();
						
			String reportDate_ = selector.getParameter(MMSReportStruct.TRAN_REPORTDATE);			
			
			final String msgKey = selector.getParameter(MMSReportStruct.TRAN_PR);
			//final String msgKey = selector.getParameter(MMSReportStruct.TRAN_MSG_KEY);
			final String result = selector.getParameter(MMSReportStruct.TRAN_RSLT);
			final String telco = selector.getParameter(MMSReportStruct.TRAN_END_TELCO);
			final String reportDate = reportDate_;
			final String tranStatus = selector.getParameter(MMSReportStruct.TRAN_STATUS);
						
			int affect = 0;
			this.initStatement(struct);

			switch (struct.getType()) {
				case MMSReportStruct.TYPE_MSG_START_LOG: {
					if (DEBUG) {
						CommonLogGW.debug("update tran start log >> " + selector.toString());
					}
					//common.info("infoTranUpdateStart Start");
					affect = DeligatePreparedStatementInfo.executeUpdate(	this.infoTranUpdateStart,
																			this.psTranUpdateStart,
																			selector);
					//common.info("infoTranUpdateStart End");
					if (DEBUG) {
						logger.debug(resource.getBundle("update.group.start.log",
														String.valueOf(affect),
														msgKey,
														result));
					}
					if (affect == 0) {
						logger.warn(resource.getBundle(	"update.group.start.log.affect.none",
														String.valueOf(affect),
														msgKey));
					}
					break;
				}
				case MMSReportStruct.TYPE_MSG_LOG: {					
					if (DEBUG) {
						CommonLogGW.debug("update grp log >> " + selector.toString());
					}
					//common.info("infoMsgUpdateLog Start");
					affect = DeligatePreparedStatementInfo.executeUpdate(	this.infoMsgUpdateLog,
																			this.psMsgUpdateLog,
																			selector);
					//common.info("infoGrpUpdateLog End");
					if (DEBUG) {
						logger.debug(resource.getBundle("update.msg.log",
														String.valueOf(affect),
														msgKey,
														result));
					}
					if (affect != 1) {
						logger.warn(resource.getBundle(	"update.msg.log.affect.none",
														String.valueOf(affect),
														msgKey));
					}
					break;
				}				
				case MMSReportStruct.TYPE_MSG_RESULT: {
					if (DEBUG) {
						CommonLogGW.debug("update tran result >> " + selector.toString());
					}
					//common.info("infoUpdateResult Start");
					affect = DeligatePreparedStatementInfo.executeUpdate(	this.infoUpdateResult,
																			this.psUpdateResult,
																			selector);
					//common.info("infoUpdateResult End");
					if (affect == 1) {
						logger.debug(resource.getBundle("update.msg.report",
														msgKey,
														tranStatus,
														result,
														telco,
														reportDate));
					}
					else {
						logger.error(resource.getBundle("update.msg.report.affect.none",
														String.valueOf(affect),
														msgKey));
					}
					break;
				}

				default: {
					// never happen
					throw new KskybException(resource.getBundle("unknown.mms.log.type",
																String.valueOf(struct.getType())));
				}
			}
		}
		catch (SQLException sqe) 
		{
			logger.error("SQLException ex : " + sqe.getMessage() + ", ErrorCode : " + sqe.getErrorCode() );
			
			switch (this.filter.analize(sqe)) 
			{
				case SQLExceptionFilter.CONNECT_ERROR: 
				{
					// 업데이트하지 못한 로그를 다시 버퍼에 넣고 종료
					BufferManager.getInstance().getMmsReportBuffer().pushBack(struct);
					
					try 
					{
						if (this.conn != null)				
						{
							logger.info("SQLException dbReconnect conn.close()");
							this.conn.close();						
						}
					} 
					catch (Exception e) 
					{}	
					
					this.conn = null;
					
					try 
					{
						logger.info("SQLException dbReconnect connect()");
						this.connect(db_driver, db_url, db_id, db_pass);
					} 
					catch (Throwable thw) 
					{
						// TODO Auto-generated catch block
						logger.error("SQLException dbReconnect connect() ex : ", thw);
						
						throw thw;
					}						
					
					break;
				}
				default: 
				{
					break;
				}
			}
			
			sleep();
		}
		catch (Throwable thw) 
		{
			logger.error(	resource.getBundle("update.log.error"), thw);
			
			sleep();

			throw thw;
		}
		finally 
		{
			this.releaseStatement();
			
			if(selector != null)
			{
				selector.close();
				selector = null;
			}
		}
		return true;
	}
	
	private void dbReconnectValidation(SQLException sqe) throws Throwable
	{								
		switch (this.filter.analize(sqe)) 
		{
			case SQLExceptionFilter.CONNECT_ERROR: 
			{
				
				try 
				{
					if (this.conn != null)				
					{
						logger.info("Validation SQLException dbReconnect conn.close()");
						this.conn.close();						
					}
				} 
				catch (Exception e) 
				{}	
				
				this.conn = null;
				
				try 
				{
					logger.info("Validation SQLException dbReconnect connect()");
					this.connect(db_driver, db_url, db_id, db_pass);
				} 
				catch (Throwable thw) 
				{
					// TODO Auto-generated catch block
					logger.error("Validation SQLException dbReconnect connect() ex : ", thw);
					
					throw thw;
				}						
				
				break;
			}
			default: 
			{
				break;
			}
		}
	}
	
	
	private void sleep()
	{
		try 
		{
			logger.info("SQLException Thread Sleep 3 Second");
			
			Thread.sleep(3000);
		} 
		catch (Exception e) 
		{
			// TODO: handle exception
		}
	}

	/**
	 * inner process <br>
	 * 내부 단위 실행 로직.
	 * 
	 * @param report
	 *            target log buffer
	 * @return process count
	 * @throws Throwable
	 *             error occur
	 */
	private int innerProcess(MMSReportBuffer report) throws Throwable 
	{
		if (report.size() < 1) 
		{
			try 
			{
				String validationFlag = MagentConfig.getProperty("mms.logbuffer.validation.query", "false");
				
				if(validationFlag.toLowerCase().equals("true")) 
				{
					//2017.03.21 DB 커넥션 유지를 위한 검증 쿼리				
					this.infoSelectValidationQry = DeligatePreparedStatementInfo.getInstance(this.querySelectValidationQry);
					this.psSelectValidationQry = DeligatePreparedStatementInfo.getStatement(this.conn,this.infoSelectValidationQry);
											
					this.psSelectValidationQry.executeQuery(this.querySelectValidationQry);											
					this.psSelectValidationQry.close();
					
					this.psSelectValidationQry = null;
					
				}								
				
				if (DEBUG) 
				{
					logger.debug(resource.getBundle("report.buffer.empty.skip"));
				}
				
				
			}
			catch (SQLException sqe) 
			{
				logger.error("Validation SQLException ex : " + sqe.getMessage() + ", ErrorCode : " + sqe.getErrorCode() );
				
				dbReconnectValidation(sqe);

				sleep();
			}
			catch (Throwable thw) 
			{
				logger.error("validationQry init error", thw);
			}
				
			return 0;
		}


		try 
		{
			if (DEBUG) 
			{
				logger.debug(resource.getBundle("buffer.patch.start"));
			}

			MMSReportStruct struct = null;
			int count = 0;
			while ((struct = report.pop(10000)) != null) {
				if (!this.processLog(struct)) {
					return count;
				}
				
				count++;
				// ---------------------------------------------------------
			}
			if (count > 0) {
				logger.debug(resource.getBundle("buffer.patch.complete",
												String.valueOf(count)));
			}
			else {
				if (DEBUG) {
					logger.debug(resource.getBundle("buffer.patch.complete",
													String.valueOf(count)));
				}
			}

			return count;

		}
		catch (Throwable thw) {
			throw thw;
		}
		finally {
		}
	}

	/**
	 * initialize when startup<br>
	 * 초기화 유효성 검증
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validate_MmsLogBufferMonitorTask() {
		this.queryMsgUpdateLog = this.sql.getSql("mms-query-msg-log");
		
		this.queryUpdateResult = this.sql.getSql("mms-query-update-result");

		this.queryTranUpdateStart = this.sql.getSql("mms-query-msg-start-log");				

		this.querySelectValidationQry = this.sql.getSql("mms-query-select-validationQuery");
		
		BroadcastEventBroker.getInstance().registMessageListener(this.listener);
	}


	/**
	 * content table resource id <br>
	 * CONTENT 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-content-id", nullable = false)
	String resourceContentKey = null;
	
	/**
	 * msg table resource id <br>
	 * MSG 테이블 리소스 ID
	 */
	@MemberField(elementName = "resource-msg-id", nullable = false)
	String resourceMsgKey = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Driver
	 */
	@MemberField(elementName = "resource-driver", nullable = false)
	String db_driver = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Url
	 */
	@MemberField(elementName = "resource-url", nullable = false)
	String db_url = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Id
	 */
	@MemberField(elementName = "resource-id", nullable = false)
	String db_id = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Password
	 */
	@MemberField(elementName = "resource-pass", nullable = false)
	String db_pass = null;
}
