package com.kskyb.magent.task;

import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.service.scheduler.AbstractScheduleTask;

/**
 * <AUTHOR>
 * 
 */
public abstract class KskybTask
								extends
								AbstractScheduleTask {
	@Override
	public void startTask() {
	}

	@Override
	public void endTask() {
	}

	@Override
	public boolean isFinishTask() {
		return false;
	}

	@Override
	public void processSuccess() {
	}

	@Override
	public void processError(Throwable thw) {
	}

	@Override
	public void release() {
	}

	@Override
	protected boolean ready() {
		return false;
	}

	@Override
	protected void prepareResource() throws Throwable {
	}

	/**
	 * 
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validateKskybTask() {
		this.registContainer();
	}
}
