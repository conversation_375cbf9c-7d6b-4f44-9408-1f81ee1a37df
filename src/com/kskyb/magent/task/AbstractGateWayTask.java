package com.kskyb.magent.task;

import org.apache.log4j.Logger;


import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.magent.MessageResource;

/**
 * GW 와 통신하는 Task의 기본 루틴을 정의함.
 */
public abstract class AbstractGateWayTask
											extends
											KskybTask {
	private static final MessageResource resource = MessageResource.getResource(AbstractGateWayTask.class);

	private static final Logger logger = Logger.getLogger("common");

	/**
	 * wait interval when target buffer is empty <br>
	 * 버퍼가 비었을 경우 대기 시간.
	 */
	@MemberField(elementName = "empty-buffer-wait")
	long emptyBufferWait = 10000L;

	/**
	 * @return the emptyBufferWait
	 */
	public final long getEmptyBufferWait() {
		return this.emptyBufferWait;
	}

	/**
	 * @param emptyBufferWait
	 *            the emptyBufferWait to set
	 */
	public final void setEmptyBufferWait(long emptyBufferWait) {
		this.emptyBufferWait = emptyBufferWait;
	}

	/**
	 * wait interval when server error occur <br>
	 * 서버오류시 대기시간.
	 */
	@MemberField(nullable = false, elementName = "server-error-wait-interval")
	long serverErrorWaitInterval = 1000L;

	/**
	 * @return the serverErrorWaitInterval
	 */
	public final long getServerErrorWaitInterval() {
		return this.serverErrorWaitInterval;
	}

	/**
	 * @param serverErrorWaitInterval
	 *            the serverErrorWaitInterval to set
	 */
	public final void setServerErrorWaitInterval(long serverErrorWaitInterval) {
		this.serverErrorWaitInterval = serverErrorWaitInterval;
	}

	/**
	 * wait interval when auth fail <br>
	 * 로그인 오류시 대기시간.
	 */
	@MemberField(nullable = false, elementName = "auth-error-wait-interval")
	long authErrorWaitInterval = 10000L;

	/**
	 * @return the authErrorWaitInterval
	 */
	public final long getAuthErrorWaitInterval() {
		return this.authErrorWaitInterval;
	}

	/**
	 * @param authErrorWaitInterval
	 *            the authErrorWaitInterval to set
	 */
	public final void setAuthErrorWaitInterval(long authErrorWaitInterval) {
		this.authErrorWaitInterval = authErrorWaitInterval;
	}

	/**
	 * wait interval when eof receive <br>
	 * 서버로부터 EOF 수신시 대기시간.
	 */
	@MemberField(elementName = "eof-wait-interval")
	long eofWaitInterval = 1000L;

	/**
	 * @return the eofWaitInterval
	 */
	public final long getEofWaitInterval() {
		return this.eofWaitInterval;
	}

	/**
	 * @param eofWaitInterval
	 *            the eofWaitInterval to set
	 */
	public final void setEofWaitInterval(long eofWaitInterval) {
		this.eofWaitInterval = eofWaitInterval;
	}

	/**
	 * wait count when eof receive <br>
	 * 서버로부터 EOF수신시 재시도 수.
	 */
	@MemberField(elementName = "eof-wait-count")
	int eofWaitCount = 3;

	/**
	 * @return the eofWaitCount
	 */
	public final int getEofWaitCount() {
		return this.eofWaitCount;
	}

	/**
	 * @param eofWaitCount
	 *            the eofWaitCount to set
	 */
	public final void setEofWaitCount(int eofWaitCount) {
		this.eofWaitCount = eofWaitCount;
	}

	/**
	 * wait interval when server eof error <br>
	 * 서버로 부터 EOF 수신오류 발생시 대기시간.
	 */
	@MemberField(elementName = "eof-delay-time")
	long eofDelayTime = 1000L;

	/**
	 * @return the eofDelayTime
	 */
	public final long getEofDelayTime() {
		return this.eofDelayTime;
	}

	/**
	 * @param eofDelayTime
	 *            the eofDelayTime to set
	 */
	public final void setEofDelayTime(long eofDelayTime) {
		this.eofDelayTime = eofDelayTime;
	}

	/**
	 * prepare resource <br>
	 * 리소스 할당.
	 */
	@Override
	protected void prepareResource() throws Throwable {
	}

	/**
	 * process execute success <br>
	 * 실행성공
	 */
	@Override
	public void processSuccess() {
		if (DebugEchoEvent.isEnable()) {
			logger.info(resource.getBundle(	"process.execute.success",
											this.getDesc()));
		}
	}

	/**
	 * process execute error <br>
	 * 실행에러.
	 */
	@Override
	public void processError(Throwable thw) {
		logger.error(	resource.getBundle(	"process.execute.error",
											this.getDesc()),
						thw);
	}

	/**
	 * execute ready <br>
	 * 수행가능여부 반환.
	 */
	@Override
	protected boolean ready() {
		return true;
	}

	/**
	 * release resource <br>
	 * 자원반환.
	 */
	@Override
	public void release() {
	}

}
