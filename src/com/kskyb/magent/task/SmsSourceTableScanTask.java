package com.kskyb.magent.task;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;

import org.apache.log4j.Logger;

import com.kskyb.broker.adapter.jdbc.SQLExceptionFilter;
import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.lang.ResourceContext;
import com.kskyb.broker.service.scheduler.ITimer;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.broker.util.ISelector;
import com.kskyb.broker.util.ISheet;
import com.kskyb.broker.util.SelectorWrapper;
import com.kskyb.broker.util.StringUtils;
import com.kskyb.broker.util.jdbc.DeligatePreparedStatementInfo;
import com.kskyb.broker.util.jdbc.ResultSetTransferTableWrapper;
import com.kskyb.magent.AuthEvent;
import com.kskyb.magent.IAuthActor;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.config.ResourceTableManager;
import com.kskyb.magent.struct.BufferManager;
import com.kskyb.magent.struct.SMSSendBuffer;
import com.kskyb.magent.struct.SMSSendStruct;

/**
 * MMS source table scan task
 */
public class SmsSourceTableScanTask
									extends
									AbstractDbTask_Direct {
									//AbstractDbTask {
	private static final MessageResource resource = MessageResource.getResource(SmsSourceTableScanTask.class);

	private static final Logger logger = Logger.getLogger("sms_send");

	private static final Logger commlog = Logger.getLogger("common");

	/**
	 * db statement <br>
	 * 쿼리 수행 Statement
	 */
	Statement stmt = null;

	/**
	 * execute query <br>
	 * 쿼리 수행 정보.
	 */
	String convertTableNameSelectQuery = null;

	/**
	 * execute statement <br>
	 * 쿼리 수행 Statement
	 */
	Statement psUpdateCheckedNoSendTarget = null;

	/**
	 * execute statement <br>
	 * 쿼리 수행 Statement
	 */
	Statement psUpdateCheckedTarget = null;

	/**
	 * execute statement <br>
	 * 쿼리 수행 Statement
	 */
	Statement psUpdateOutOfTTLTarget = null;

	/**
	 * execute statement info <br>
	 * 쿼리 수행 정보.
	 */
	DeligatePreparedStatementInfo infoUpdateOutOfTTLTarget = null;

	/**
	 * execute statement info <br>
	 * 쿼리 수행 정보.
	 */
	DeligatePreparedStatementInfo infoUpdateCheckedNoSendTarget = null;
	
	/**
	 * execute statement info <br>
	 * 쿼리 수행 정보.
	 */
	DeligatePreparedStatementInfo infoUpdateCheckedTarget = null;

	/**
	 * config sql <br>
	 * 수행 쿼리 설정.
	 */
	protected String queryUpdateOutOfTTLTarget = null;

	/**
	 * config sql <br>
	 * 수행 쿼리 설정.
	 */
	protected String querySelectCheckedTarget = null;

	/**
	 * config sql <br>
	 * 수행 쿼리 설정.
	 */
	protected String queryUpdateCheckedNoSendTarget = null;
	
	/**
	 * config sql <br>
	 * 수행 쿼리 설정.
	 */
	protected String queryUpdateCheckedTarget = null;

	/**
	 * initialize flag <br>
	 * 초기화 생태 플래그.
	 */
	boolean initFlag = false;

	/**
	 * initialize connection and statement <br>
	 * 데이터 베이스 연결 초기화.
	 * 
	 * @throws Exception
	 *             error occur
	 */
	private final void initStatement() throws Exception {
		if (this.initFlag) {
			return;
		}
		this.releaseStatement();

		// innterOpen -> STATUS_OPEN
		
		if (DebugEchoEvent.isEnable()) {
			commlog.info("this.connect() start");
		}
		//this.connect();
		this.connect(db_driver, db_url, db_id, db_pass);
		if (DebugEchoEvent.isEnable()) {
			commlog.info("this.connect() end");
		}

		final String resourceName = ResourceTableManager.getInstance().getResourceName(this.resourceKey);

		this.convertTableNameSelectQuery = StringUtils.convertString(	this.querySelectCheckedTarget,
																		"#TABLE_NAME#",
																		resourceName);
		// init, inneropen
		if (DebugEchoEvent.isEnable()) {
			commlog.info("this.conn.createStatement start");
		}
		this.stmt = this.conn.createStatement();
		if (DebugEchoEvent.isEnable()) {
			commlog.info("this.conn.createStatement end");
		}

		final String _queryUpdateCheckedTarget = StringUtils.convertString(	this.queryUpdateCheckedTarget,
																			"#TABLE_NAME#",
																			resourceName);
		this.infoUpdateCheckedTarget = DeligatePreparedStatementInfo.getInstance(_queryUpdateCheckedTarget);
		
		// init, inneropen
		if (DebugEchoEvent.isEnable()) {
			commlog.info("_queryUpdateOutOfTTLTarget start");
		}
		this.psUpdateCheckedTarget = DeligatePreparedStatementInfo.getStatement(this.conn,
																				this.infoUpdateCheckedTarget);
		if (DebugEchoEvent.isEnable()) {
			commlog.info("_queryUpdateOutOfTTLTarget end");
		}

		final String _queryUpdateCheckedNoSendTarget = StringUtils.convertString(	this.queryUpdateCheckedNoSendTarget,
																					"#TABLE_NAME#",
																					resourceName);

		this.infoUpdateCheckedNoSendTarget = DeligatePreparedStatementInfo.getInstance(_queryUpdateCheckedNoSendTarget);
		this.psUpdateCheckedNoSendTarget = DeligatePreparedStatementInfo.getStatement(	this.conn,
																						this.infoUpdateCheckedNoSendTarget);

		final String _queryUpdateOutOfTTLTarget = StringUtils.convertString(this.queryUpdateOutOfTTLTarget,
																			"#TABLE_NAME#",
																			resourceName);
		this.infoUpdateOutOfTTLTarget = DeligatePreparedStatementInfo.getInstance(_queryUpdateOutOfTTLTarget);
		// init, inneropen
		if (DebugEchoEvent.isEnable()) {
			commlog.info("psUpdateOutOfTTLTarget start");
		}
		this.psUpdateOutOfTTLTarget = DeligatePreparedStatementInfo.getStatement(	this.conn,
																					this.infoUpdateOutOfTTLTarget);
		if (DebugEchoEvent.isEnable()) {
			commlog.info("psUpdateOutOfTTLTarget end");
		}

		this.initFlag = true;
	}

	/**
	 * release connection and statement <br>
	 * 데이터 베이스 연결 리소스 반환.
	 */
	protected final void releaseStatement() {
		this.initFlag = false;

		if (this.stmt != null) {
			try {
				this.stmt.close();
			}
			catch (Throwable e) {
				logger.error(	resource.getBundle("resource.release.error"),
								e);
			}
			this.stmt = null;
		}
		if (this.psUpdateCheckedTarget != null) {
			try {
				this.psUpdateCheckedTarget.close();
			}
			catch (Throwable e) {
				logger.error(	resource.getBundle("resource.release.error"),
								e);
			}
			this.psUpdateCheckedTarget = null;
		}
		if (this.psUpdateCheckedNoSendTarget != null) {
			try {
				this.psUpdateCheckedNoSendTarget.close();
			}
			catch (Throwable e) {
				logger.error(	resource.getBundle("resource.release.error"),
								e);
			}
			this.psUpdateCheckedNoSendTarget = null;
		}		
		if (this.psUpdateOutOfTTLTarget != null) {
			try {
				this.psUpdateOutOfTTLTarget.close();
			}
			catch (Throwable e) {
				logger.error(	resource.getBundle("resource.release.error"),
								e);
			}
			this.psUpdateOutOfTTLTarget = null;
		}
		if (this.conn != null) {
			try {
				this.conn.close();
			}
			catch (Throwable thw) {
				logger.error(	"resource.release.error",
								thw);
			}
		}
	}

	/**
	 * main logic <br>
	 * 메인 로직.
	 */
	@Override
	public void processMain() throws Throwable {
		SMSSendBuffer buffer = BufferManager.getInstance().getSmsSendBuffer();
		this.initStatement();
		// -- update out of ttl -- //
		if (this.ttlUpdate > 0) {
			try {
				if (DebugEchoEvent.isEnable()) {
					commlog.info("executeUpdate psUpdateOutOfTTLTarget start");
				}
				//상태값 6->1 업데이트.2012.10.16.by han
				int cntUpdateCheckedNoSend = DeligatePreparedStatementInfo.executeUpdate(	this.infoUpdateCheckedNoSendTarget,
																							this.psUpdateCheckedNoSendTarget,
																							null);
				if (cntUpdateCheckedNoSend > 0) {
					logger.info(resource.getBundle(	"update.nosend.data",
													String.valueOf(cntUpdateCheckedNoSend)));
				}

				int cntUpdateOutOfTTL = DeligatePreparedStatementInfo.executeUpdate(this.infoUpdateOutOfTTLTarget,
																					this.psUpdateOutOfTTLTarget,
																					null);
				if (DebugEchoEvent.isEnable()) {
					commlog.info("executeUpdate psUpdateOutOfTTLTarget end");
				}

				if (cntUpdateOutOfTTL > 0) {
					logger.info(resource.getBundle(	"update.outof.ttl",
													String.valueOf(cntUpdateOutOfTTL)));
				}
			}
			catch (Throwable thw) {
				logger.error(	"ttl.update.error",
								thw);
			}
		}

		boolean suspendLog = true;

		final ITimer localTimer = this.getTimer();

		try {
			while (true) {
				if (buffer.isFull()) {
					logger.warn(resource.getBundle("send.buffer.full"));
					buffer.waitOnPop(10000);
					continue;
				}

				if (localTimer.isSuspend()) {
					if (suspendLog) {
						logger.debug(resource.getBundle("task.suspended",
														this.getDesc()));
						suspendLog = false;
					}

					try {
						synchronized (localTimer) {
							localTimer.wait(60000);
						}
					}
					catch (Throwable thw) {
						if (DebugEchoEvent.isEnable()) {
							logger.error(	"timer waiting error",
											thw);
						}
					}
					continue;
				}

				suspendLog = true;

				
				try {

					if (validationHeartBeatTimer()) {

						if (this.processMain(buffer) == 0) {
							try {
								synchronized (localTimer) {
									localTimer.wait(this.tableEmptyWait);
								}
							} catch (Throwable thw) {
								if (DebugEchoEvent.isEnable()) {
									logger.error("timer waiting error", thw);
								}
							}
							continue;
						}
					} else {
						try {
							synchronized (localTimer) {
								localTimer.wait(this.tableEmptyWait);
							}
						} catch (Throwable thw) {
							if (DebugEchoEvent.isEnable()) {
								logger.error("timer waiting error", thw);
							}
						}
						continue;

					}

				} catch (SQLException sqe) {
					switch (this.filter.analize(sqe)) {
					case SQLExceptionFilter.CONNECT_ERROR: {
						this.initFlag = false;
						break;
					}
					default: {
						break;
					}
					}

				}
				catch (Throwable thw) {
					logger.error(	"task.inner.process.error",
									thw);
					this.releaseStatement();
					
					if (thw instanceof SQLException) {
						this.releaseStatement();
					}

					try {
						synchronized (localTimer) {
							localTimer.wait(this.tableEmptyWait);
						}
					}
					catch (Throwable ignore) {
						if (DebugEchoEvent.isEnable()) {
							logger.error(	"timer waiting error",
											ignore);
						}
					}
				}

				final int limit = buffer.getLimit();
				int size = buffer.size();
				if ((limit - size) < size) {
					logger.info(resource.getBundle(	"sms.send.buffer.shrink.wait.start",
													String.valueOf(size),
													String.valueOf(limit)));
					do {
						buffer.waitOnPop(10000);
						size = buffer.size();
					} while ((limit - size) < size);
					logger.info(resource.getBundle(	"sms.send.buffer.shrink.wait.finish",
													String.valueOf(size),
													String.valueOf(limit)));
				}
			}
		}
		finally {
			this.releaseStatement();
		}

	}

	/**
	 * inner main logic <br>
	 * 내부 단위로직.
	 * 
	 * @param buffer
	 *            sms send buffer
	 * @return process count
	 * @throws Throwable
	 *             error occur
	 */
	protected int processMain(SMSSendBuffer buffer) throws Throwable {
		if (DebugEchoEvent.isEnable()) {
			logger.debug(resource.getBundle("process.execute.start",
											this.getDesc()));
		}
		this.initStatement();

		ResultSet rs = null;

		ISelector selector = null;
		ISheet table = null;

		try {
			if (DebugEchoEvent.isEnable()) {
				commlog.info("SelectorWrapper.wrap start");
			}
			selector = SelectorWrapper.wrap(this.addInfo);
			if (DebugEchoEvent.isEnable()) {
				commlog.info("SelectorWrapper.wrap end");
			}

			// -- select unsend list and gen table -- //
			final String limit = String.valueOf(buffer.getLimit() - buffer.size());

			if (DebugEchoEvent.isEnable()) {
				logger.debug(resource.getBundle("select.checked.target.start",
												limit));
			}
			selector.setParameter(	"LIMIT",
									limit,
									true);

			// Select Query Execute
			if (DebugEchoEvent.isEnable()) {
				commlog.info("executeQuery convertTableNameSelectQuery start");
			}
			rs = DeligatePreparedStatementInfo.executeQuery(this.convertTableNameSelectQuery,
															this.stmt,
															selector,
															"${",
															"}");
			if (DebugEchoEvent.isEnable()) {
				commlog.info("executeQuery convertTableNameSelectQuery end");
			}

			if (DebugEchoEvent.isEnable()) {
				commlog.info("ResultSetTransferTableWrapper start");
			}
			table = ResultSetTransferTableWrapper.getInstanceFromResultSet(	null,
																			rs);
			if (DebugEchoEvent.isEnable()) {
				commlog.info("ResultSetTransferTableWrapper end");
			}

			if (DebugEchoEvent.isEnable()) {
				commlog.info("rs.close() start");
			}
			rs.close();
			if (DebugEchoEvent.isEnable()) {
				commlog.info("rs.close() end");
			}
			rs = null;

			// -- patch table -- //
			if (DebugEchoEvent.isEnable()) {
				logger.debug(resource.getBundle("select.checked.target.result.patch.start",
												String.valueOf(table.getRecordCnt())));
			}

			table.open();
			int cnt = 0;
			/**
			 * 스캔된 대상 레코드를 패치하면서 발송 버퍼로 전송하는 루틴
			 */
			while (table.next()) {
				//TEST - 20140507
//				logger.info("[TRAN_PR] "+table.getParameter("TRAN_PR"));
//				logger.info("[TRAN_PHONE] "+table.getParameter("TRAN_PHONE"));
//				logger.info("[TRAN_CALLBACK] "+table.getParameter("TRAN_CALLBACK"));
//				logger.info("[TRAN_MSG] "+table.getParameter("TRAN_MSG"));
//				logger.info("[TRAN_REVERSE] "+table.getParameter("TRAN_REVERSE"));
//				logger.info("[TRAN_TTL] "+table.getParameter("TRAN_TTL"));
//				logger.info("[TRAN_TYPE] "+table.getParameter("TRAN_TYPE"));
				//TEST -----------------------------------------------------------------
				
				// 전화번호서 "-" 삭제 
				String filteringPhone = table.getParameter("TRAN_PHONE");
				String filteringCallback = table.getParameter("TRAN_CALLBACK");
				
				table.setParameter("TRAN_PHONE", filteringPhone.replaceAll("[-]", ""));
				
				if(filteringCallback != null || !filteringCallback.equals("")) {
					table.setParameter("TRAN_CALLBACK", filteringCallback.replaceAll("[-]", ""));						
				}
				
				// -- 상태값 6 update
				if (DebugEchoEvent.isEnable()) {
					commlog.info("executeQuery psUpdateCheckedTarget start");
				}
				int cntUpdateChecked = DeligatePreparedStatementInfo.executeUpdate(	this.infoUpdateCheckedTarget,
																					this.psUpdateCheckedTarget,
																					table);
				if (DebugEchoEvent.isEnable()) {
					commlog.info("executeQuery psUpdateCheckedTarget end");
				}
				if (cntUpdateChecked == 1) {
					buffer.pushLog(table);
				}
				else {
					logger.warn(resource.getBundle(	"checked.uncheced.update.cnt.is.different",
													table.toString()));
				}
				cnt++;
			}
			if (DebugEchoEvent.isEnable()) {
				logger.debug(resource.getBundle("select.checked.target.result.patch.end",
												String.valueOf(cnt)));
			}

			return cnt;
		}								
		finally {
			if (DebugEchoEvent.isEnable()) {
			commlog.info("ObjectFinalizer.close(table) start");
			}
			ObjectFinalizer.close(table);
			if (DebugEchoEvent.isEnable()) {
				commlog.info("ObjectFinalizer.close(table) end");
			}
			if (DebugEchoEvent.isEnable()) {
				commlog.info("ObjectFinalizer.close(selector) start");
			}
			ObjectFinalizer.close(selector);
			if (DebugEchoEvent.isEnable()) {
				commlog.info("ObjectFinalizer.close(selector) end");
			}
			selector = null;
			ObjectFinalizer.close(rs);
		}
	}

	/**
	 * 
	 */
	final AuthEvent.Listener listener = new AuthEvent.Listener(this) {

		@Override
		protected boolean isTarget(int actorType) {
			return actorType == IAuthActor.SMS;
		}
	};

	// /**
	// * auth actor list <br>
	// * 인증수행자 리스트.
	// */
	// final ArrayList<IAuthActor> actors = new ArrayList<IAuthActor>();
	//
	// /**
	// * auth fail event notify <br>
	// * 인증실패알림.
	// */
	// @Override
	// public void authFail() {
	// for (Iterator<IAuthActor> iter = this.actors.iterator(); iter.hasNext();) {
	// final IAuthActor actor = iter.next();
	//
	// if (actor.isLogin()) {
	// return;
	// }
	// }
	// this.getTimer().suspend();
	// }
	//
	// /**
	// * auth ok envent notify <br>
	// * 인증성공알림.
	// */
	// @Override
	// public void authOk() {
	// this.getTimer().resume();
	//
	// }
	//
	// /**
	// * add auth actor <br>
	// * 인증수행자 등록.
	// */
	// @Override
	// public void setActor(IAuthActor actor) {
	// if (!this.actors.contains(this)) {
	// this.actors.add(actor);
	// actor.addListener(this);
	// }
	// }

	/**
	 * validation when startup <br>
	 * 초기화 유효성 검증.
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validate_SmsSourceTableScanTask() {
		this.queryUpdateOutOfTTLTarget = this.sql.getSql("sms-query-update-out-of-ttl-target");
		this.querySelectCheckedTarget = this.sql.getSql("sms-query-select-checked-target");
		this.queryUpdateCheckedNoSendTarget = this.sql.getSql("sms-query-update-checked-nosend-target");
		this.queryUpdateCheckedTarget = this.sql.getSql("sms-query-update-checked-target");

		ResourceTableManager.getInstance().getResourceName(this.resourceKey);
	}

	public final boolean validationHeartBeatTimer() {

		int exceptStartTime = -1;
		int exceptEndTime = -1;
		int from = 0;
		int to = 0;
		if (this.exceptTime == null) {
			return true;
		}

		int idx = this.exceptTime.indexOf('~');

		if (idx < 0) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"invalid.except.time",
																			this.exceptTime));
		}
		try {
			from = Integer.parseInt(this.exceptTime.substring(0, idx).trim());
			to = Integer.parseInt(this.exceptTime.substring(idx + 1).trim());
		}
		catch (Throwable thw) {
			throw new RuntimeException(	ResourceContext.getBundledMessage(	"invalid.except.time.parse.error",
																			this.exceptTime),
										thw);
		}

		if (from == to) {
			throw new RuntimeException(ResourceContext.getBundledMessage(	"invalid.except.time",
																			this.exceptTime));
		}

		long now = System.currentTimeMillis();
		SimpleDateFormat simple_format = new SimpleDateFormat("HHmm");
		String checkTime = DateUtil.getFormatDate(	simple_format, now);
		int iTime = Integer.parseInt(checkTime);
		boolean intime = false;
		
		if (from < to) {
			exceptStartTime = from;
			exceptEndTime = to;
			if (exceptStartTime < 0 || exceptEndTime < 0) {
				return true;
			}
			//iTime = 10;
			if (exceptStartTime <= iTime && iTime <= exceptEndTime) {
				return false;
			}
			else {
				return true;
			}
		}
		else {
			exceptStartTime = to;
			exceptEndTime = from;
			if (exceptStartTime < 0 || exceptEndTime < 0) {
				return true;
			}
			//iTime = 10;
			if (exceptStartTime <= iTime && iTime <= exceptEndTime) {
				return true;
			}
			else {
				return false;
			}
		}
		
	}	
	
	/**
	 * wait interval when target table empty <br>
	 * 테이블 값이 없을 경우 대기 시간.
	 */
	@MemberField(elementName = "table-empty-wait")
	long tableEmptyWait = 5000L;

	/**
	 * source table resource id <br>
	 * 소스 테이블 리소스 아이디.
	 */
	@MemberField(elementName = "resource-target-id", nullable = false)
	String resourceKey = null;

	/**
	 * out of ttl interval <br>
	 * TTL over 시간.
	 */
	@MemberField(elementName = "update-outof-ttl", nullable = false)
	int ttlUpdate = 0;
	
	/**
	 * DB Connection Info <br>
	 * DB Driver
	 */
	@MemberField(elementName = "resource-driver", nullable = false)
	String db_driver = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Url
	 */
	@MemberField(elementName = "resource-url", nullable = false)
	String db_url = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Id
	 */
	@MemberField(elementName = "resource-id", nullable = false)
	String db_id = null;
	
	/**
	 * DB Connection Info <br>
	 * DB Password
	 */
	@MemberField(elementName = "resource-pass", nullable = false)
	String db_pass = null;
	
	@MemberField(elementName = "except-time2")
	String exceptTime = null;
}
