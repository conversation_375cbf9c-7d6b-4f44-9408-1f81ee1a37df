package com.kskyb.magent.task;

import java.sql.Connection;
import java.util.HashMap;

import org.apache.log4j.Logger;

import com.kskyb.broker.adapter.jdbc.SQLExceptionFilter;
import com.kskyb.broker.annotation.IFieldType;
import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.io.SerializeCodecs;
import com.kskyb.broker.service.pool.db.ConnectionPoolMasterService;
import com.kskyb.broker.util.jdbc.SQLSet;
import com.kskyb.magent.MessageResource;

/**
 * DB를 통한 작업을 진행하는 Task의 기본 루틴을 정의함.
 */
public abstract class AbstractDbTask
									extends
									KskybTask {
	private static final MessageResource resource = MessageResource.getResource(SmsSourceTableScanTask.class);

	private static final Logger logger = Logger.getLogger("common");

	/**
	 * additional query mapping info <br>
	 * 추가 쿼리 매핑 정보
	 */
	protected final HashMap<String, String> addInfo = new HashMap<String, String>();

	/**
	 * 
	 */
	SQLSet sql = null;

	/**
	 * 
	 */
	SQLExceptionFilter filter = null;

	/**
	 * validation when initialize <br>
	 * 초기화시 유효성검증
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validateAbstractDbTask() {
		if (this.infAddInfos != null) {
			// CommonUtils.ConvertStringToMap( this.addInfo,
			// this.infAddInfos);
			SerializeCodecs.decodeMap(	this.addInfo,
										this.infAddInfos);
		}

		this.sql = SQLSet.lookup(this.sqlName);
		this.filter = SQLExceptionFilter.lookup(this.filterName);
	}

	/**
	 * target connection id <br>
	 * 대상 연결 아이디.
	 */
	@MemberField(elementName = "connect-id", nullable = false)
	String connId = null;

	/**
	 * sql set <br>
	 * sql 정보 참조 지정자.
	 */
	@MemberField(elementName = "sql-set", nullable = false)
	String sqlName = null;

	/**
	 * sql exception handle filter <br>
	 * 데이터베이스 오류 관리 필터 지정자.
	 */
	@MemberField(elementName = "sql-exception-filter", nullable = false)
	String filterName = null;

	/**
	 * @return the filter
	 */
	public final SQLExceptionFilter getFilter() {
		return this.filter;
	}

	/**
	 * @param filter
	 *            the filter to set
	 */
	public final void setFilter(SQLExceptionFilter filter) {
		this.filter = filter;
	}

	/**
	 * additional query mapping info <br>
	 * 추가 쿼리 매핑 정보.
	 */
	@MemberField(type = IFieldType.CDATA, elementName = "info-add")
	protected String infAddInfos = null;

	/**
	 * @return the infAddInfos
	 */
	public final String getInfAddInfos() {
		return this.infAddInfos;
	}

	/**
	 * @param infAddInfos
	 *            the infAddInfos to set
	 */
	public final void setInfAddInfos(String infAddInfos) {
		this.infAddInfos = infAddInfos;
	}

	/**
	 * wait interval <br>
	 * 버퍼가 비어 있을때 대기시간.
	 */
	@MemberField(elementName = "empty-buffer-wait")
	long emptyBufferWait = 10000L;

	/**
	 * @return the emptyBufferWait
	 */
	public final long getEmptyBufferWait() {
		return this.emptyBufferWait;
	}

	/**
	 * @param emptyBufferWait
	 *            the emptyBufferWait to set
	 */
	public final void setEmptyBufferWait(long emptyBufferWait) {
		this.emptyBufferWait = emptyBufferWait;
	}

	/**
	 * DB connection <br>
	 * 데이터 베이스 연결.
	 */
	protected Connection conn = null;

	/**
	 * db handshake wrapping method <br>
	 * 데이터 베이스 연결.
	 * 
	 * @throws Exception
	 *             error occur
	 */
	protected void connect() throws Exception 
	{
		if (DebugEchoEvent.isEnable()) {
			logger.debug(resource.getBundle("task.init.connection"));
		}
		this.conn = ConnectionPoolMasterService.getService().getConnection(this.connId);
		
		this.conn.setAutoCommit(true);
//		this.conn.setTransactionIsolation(2); // READ-COMMITTED 방식
		
		// container.getInstance(this.connId);
	}

	/**
	 * resource allocate <br>
	 * 리소스 할당.
	 */
	@Override
	protected void prepareResource() throws Throwable {
	}

	/**
	 * process execute success <br>
	 * 프로세스 실행 성공
	 */
	@Override
	public void processSuccess() {
		if (DebugEchoEvent.isEnable()) {
			logger.debug(resource.getBundle("process.execute.success",
											this.getDesc()));
		}
	}

	/**
	 * process execute error <br>
	 * 프로세스 실행 실패.
	 */
	@Override
	public void processError(Throwable thw) {
		logger.error(	resource.getBundle(	"process.execute.error",
											this.getDesc()),
						thw);
	}

	/**
	 * execute ready <br>
	 * 실행 가능 여부 반환
	 */
	@Override
	protected boolean ready() {
		return true;
	}

	/**
	 * release resource <br>
	 * 자원반환
	 */
	@Override
	public void release() {
	}
}
