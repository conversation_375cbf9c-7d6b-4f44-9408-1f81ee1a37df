package com.kskyb.magent.task;

import java.io.EOFException;

import org.apache.log4j.Logger;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.service.scheduler.ITimer;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.magent.AuthEvent;
import com.kskyb.magent.AuthFailException;
import com.kskyb.magent.IAuthActor;
import com.kskyb.magent.KskyB;
import com.kskyb.magent.KskybException;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.adapter.SMSAdapter;
import com.kskyb.magent.config.MagentConfig;
import com.kskyb.magent.struct.BufferManager;
import com.kskyb.magent.struct.SMSReportBuffer;
import com.kskyb.magent.struct.SMSSendBuffer;
import com.kskyb.magent.struct.SMSSendStruct;
import com.kskyb.magent.util.TTLChecker;

/**
 * SMS send buffer monitoring and send Task
 */
public class SmsSourceBufferMonitorTask
										extends
										AbstractGateWayTask {
	private static final MessageResource resource = MessageResource.getResource(SmsSourceTableScanTask.class);

	private static final Logger logger = Logger.getLogger("sms_send");

	/**
	 * main logic <br>
	 * 메인 로직.
	 */
	@Override
	public void processMain() throws Throwable {
		final SMSSendBuffer buffer = BufferManager.getInstance().getSmsSendBuffer();
		final ITimer localTimer = this.getTimer();
		while (true) {
			boolean loggingSuspend = true;

			while (localTimer.isSuspend()) {
				if (loggingSuspend) {
					logger.warn(resource.getBundle(	"task.suspended",
													this.getDesc()));
					loggingSuspend = false;
				}

				try {
					synchronized (localTimer) {
						localTimer.wait(60000);
					}
				}
				catch (Throwable thw) {
					if (DebugEchoEvent.isEnable()) {
						logger.error(	resource.getBundle("timer.wait.error"),
										thw);
					}
				}
			}

			try {
				this.processBuffer(buffer);
			}
			catch (Throwable thw) {
				logger.error(	resource.getBundle("sms.process.buffer.error"),
								thw);
			}

			if (buffer.size() == 0) {
				buffer.waitOnPush(this.emptyBufferWait);
			}
		}
	}

	/**
	 * inner main logic <br>
	 * 내부실행 단위 로직.
	 * 
	 * @param buffer
	 *            send info buffer
	 * @throws Exception
	 *             error occur
	 */
	private final void processBuffer(final SMSSendBuffer buffer) throws Exception {
		final SMSReportBuffer result = BufferManager.getInstance().getSmsReportBuffer();
		final TTLChecker ttlChecker = TTLChecker.getInstance();
		final boolean useBuffer = MagentConfig.getProperty(	"sms.send.use.buffer",
															"true").equalsIgnoreCase("true");
		SMSSendStruct struct = null;
		while ((struct = buffer.pop(10000)) != null) {
			// 전송
			final String _serial = struct.getValue(SMSSendStruct.TRAN_PR);

			// logging popup buffer logging

//			result.pushLog(	_serial,
//							KskyB.STATUS_SEND_PROCESS,
//							KskyB.RSLT_SEND_PROCESS,
//							useBuffer);

			// -- out of ttl check -- //
			if (!ttlChecker.isValidate(struct)) {
				result.pushReport(	_serial,
									" ",
									DateUtil.getFormatDate(DateUtil.serial_time),
									KskyB.RSLT_OUT_OF_TTL_CODE,
									useBuffer);
				// resultBuffer.pushLog(_serial, KskyB.STATUS_RECEVE_REPORT, KskyB.RSLT_OUT_OF_TTL_CODE);
				continue;
			}

			boolean serverError = false;
			while (true) {
				try {
					this.adapter.send(struct);
					final String _result = struct.getValue(SMSSendStruct.INDEX_RESULT);

					result.pushLog(	_serial,
									KskyB.STATUS_SEND_COMPLETE,
									_result,
									useBuffer);

					this.getTimer().commit(System.currentTimeMillis());

					serverError = false;

				}
				catch (Throwable kse) {
					this.adapter.releaseConnection();
					if (kse instanceof EOFException) {
						logger.error(	resource.getBundle("ack.listen.error"),
										kse);
						try {
							Thread.sleep(this.eofDelayTime);
						}
						catch (Throwable thw) {
							if (DebugEchoEvent.isEnable()) {
								logger.error(	"thread sleep error",
												thw);
							}
						}
						continue;
					}
					else if (kse instanceof AuthFailException) {
						logger.error(	resource.getBundle("auth.fail"),
										kse);
						try {
							Thread.sleep(this.authErrorWaitInterval);
						}
						catch (Throwable thw) {
							logger.debug(	resource.getBundle("adapter.monitor.wait.fail"),
											thw);
						}
						continue;
					}
					else if (kse instanceof KskybException) {
						logger.error(	resource.getBundle("message.send.catch.server.error"),
										kse);
						if (serverError) {
							// wait for connection
							this.adapter.waitForConnection(this.serverErrorWaitInterval);
						}

						serverError = true;
						continue;
					}
					else {
						logger.error(	resource.getBundle("message.send.catch.unrecoverable.error"),
										kse);
						result.pushLog(	_serial,
										KskyB.STATUS_SEND_ERROR,
										KskyB.RSLT_SEND_ERROR,
										useBuffer);
					}
				}

				break;
			}
		}
	}

	/**
	 * 
	 */
	final AuthEvent.Listener listener = new AuthEvent.Listener(this) {
		/**
		 * @see com.kskyb.magent.AuthEvent.Listener#isTarget(int)
		 */
		@Override
		protected boolean isTarget(int actorType) {
			return actorType == IAuthActor.SMS;
		}
	};

	/**
	 * validation when startup <br>
	 * 초기화 유효성 검증.
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validate_SmsSourceBufferMonitorTask() {
		if (this.isValidTask()) {
			this.adapter.startMonitorThread();
		}
	}

	/**
	 * gw adapter <br>
	 * 서버 어답터.
	 */
	@MemberField(nullable = false, elementName = "sms-gate")
	SMSAdapter adapter = null;

	// /**
	// * auth actor list <br>
	// * 인증수행자 리스트.
	// */
	// final ArrayList<IAuthActor> actors = new ArrayList<IAuthActor>();
	//
	// /**
	// * auth fail event notify <br>
	// * 인증실패 알림.
	// */
	// @Override
	// public void authFail() {
	// for (Iterator<IAuthActor> iter = this.actors.iterator(); iter.hasNext();) {
	// final IAuthActor actor = iter.next();
	//
	// if (actor.isLogin()) {
	// return;
	// }
	// }
	// this.getTimer().suspend();
	// }
	//
	// /**
	// * auth ok envent notify <br>
	// * 인증성공 알림
	// */
	// @Override
	// public void authOk() {
	// this.getTimer().resume();
	//
	// }
	//
	// /**
	// * add auth actor <br>
	// * 인증수행자 추가.
	// */
	// @Override
	// public void setActor(IAuthActor actor) {
	// if (!this.actors.contains(this)) {
	// this.actors.add(actor);
	// actor.addListener(this);
	// }
	// }
}
