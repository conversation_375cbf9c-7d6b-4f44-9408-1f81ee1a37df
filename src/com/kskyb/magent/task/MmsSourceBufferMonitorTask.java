package com.kskyb.magent.task;

import java.io.EOFException;
import java.util.Iterator;
import java.util.Map;

import org.apache.log4j.Logger;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.service.scheduler.ITimer;
import com.kskyb.magent.AuthEvent;
import com.kskyb.magent.AuthFailException;
import com.kskyb.magent.IAuthActor;
import com.kskyb.magent.KskyB;
import com.kskyb.magent.MessageResource;
import com.kskyb.magent.ServerCommunicationException;
import com.kskyb.magent.adapter.MMSAdapter;
import com.kskyb.magent.config.MagentConfig;
import com.kskyb.magent.struct.BufferManager;
import com.kskyb.magent.struct.MMSReportBuffer;
import com.kskyb.magent.struct.MMSSendBuffer;
import com.kskyb.magent.struct.MMSSendStruct;

/**
 * MMS send buffer monitoring and send Task
 */
public class MmsSourceBufferMonitorTask
										extends
										AbstractGateWayTask {
	/**
	 * 
	 */
	private static final MessageResource resource = MessageResource.getResource(MmsSourceBufferMonitorTask.class);

	/**
	 * 
	 */
	private static final Logger logger = Logger.getLogger("mms_send");

	/**
	 * 
	 */
	private static final Logger tran = Logger.getLogger("mms_tran");

	/**
	 * main logic <br>
	 * 메인 수행 로직
	 */
	@Override
	public void processMain() throws Throwable {
		MMSSendBuffer buffer = BufferManager.getInstance().getMmsSendBuffer();
		MMSReportBuffer result = BufferManager.getInstance().getMmsReportBuffer();
		final ITimer localTimer = this.getTimer();
		while (true) {
			boolean loggingSuspend = true;
			while (localTimer.isSuspend()) {
				if (loggingSuspend) {
					logger.warn(resource.getBundle(	"task.suspended",
													this.getDesc()));
					loggingSuspend = false;
				}

				try {
					synchronized (localTimer) {
						localTimer.wait(60000);
					}
				}
				catch (Throwable thw) {
					if (DebugEchoEvent.isEnable()) {
						logger.error(	resource.getBundle("timer.wait.error"),
										thw);
					}
				}
			}

			try {
				this.processBuffer(	buffer, result);
			}
			catch (Throwable thw) {
				logger.error(	resource.getBundle("mms.process.buffer.error"),
								thw);
			}
			if (buffer.size() == 0) {
				buffer.waitOnPush(this.emptyBufferWait);
			}
		}
	}

	private final void processBuffer(	final MMSSendBuffer buffer,
										final MMSReportBuffer result) throws Exception {
		MMSSendStruct struct = null;
		final boolean useBuffer = MagentConfig.getProperty(	"mms.send.use.buffer",
															"true").equalsIgnoreCase("true");
		
		/*
		final boolean sendFailRetryFlag = MagentConfig.getLocalProperty("mms_send_fail_retry",
															  "true").equalsIgnoreCase("true");
		
		//20150312 전송실패시 retry flag by LSJ
		String seven = "failRetryFlag:"+sendFailRetryFlag;
		logger.info(seven);
		*/
		
		while ((struct = buffer.pop(10000)) != null) {
			if (DebugEchoEvent.isEnable()) {
				tran.debug(resource.getBundle(	"popup.from.buffer",
												struct.getTranPr()));
			}

			// 전송
			boolean serverError = false;
			while (true) {
				Map<String, String> map = null;
				try {
					final long startTime = System.currentTimeMillis();
					if (DebugEchoEvent.isEnable()) {
						tran.debug(resource.getBundle(	"try.send.start",
														struct.getTranPr()));
					}
					
					//G/W로 전송 후 SEND ACK 까지 수신
					map = this.adapter.send(struct);

					// - 발송한 시간을 스탬프로 찍는다
					this.getTimer().commit(System.currentTimeMillis());
					if (DebugEchoEvent.isEnable()) {
						tran.debug(resource.getBundle(	"send.complete",
														struct.getTranPr(),
														String.valueOf(System.currentTimeMillis() - startTime)));
					}

					if (DebugEchoEvent.isEnable()) {
						logger.debug(resource.getBundle("send.complete",
														struct.getTranPr(),
														String.valueOf(System.currentTimeMillis() - startTime)));
					}

					// logging popup buffer logging					
					result.setMsgLog(	struct,
										KskyB.STATUS_SEND_COMPLETE,
										map.get("CODE"),
										useBuffer);
					
					
				}
				catch (EOFException EOF) {
					logger.error(	resource.getBundle("ack.listen.error"),
									EOF);
					this.adapter.releaseConnection();

					try {
						Thread.sleep(this.eofDelayTime);
					}
					catch (Throwable thw) {
						if (DebugEchoEvent.isEnable()) {
							logger.error(	"thread sleep error",
											thw);
						}
					}

					continue;
				}
				catch (AuthFailException afe) {
					this.adapter.releaseConnection();
					try {
						Thread.sleep(this.authErrorWaitInterval);
					}
					catch (Throwable thw) {
						if (DebugEchoEvent.isEnable()) {
							logger.debug(	resource.getBundle("adapter.monitor.wait.fail"),
											thw);
						}
					}
					continue;
				}
				catch (ServerCommunicationException sce) {
					logger.error(	resource.getBundle("message.send.catch.server.error"),
									sce);
					if (DebugEchoEvent.isEnable()) {
						CommonLogGW.debug(	">>> DEBUG  MmsSourceBufferMonitorTask.processMain>>>",
											new Throwable("releaseConnection call"));
					}
					this.adapter.releaseConnection();
					if (serverError) {
						// wait for connection
						this.adapter.waitForConnection(this.serverErrorWaitInterval);
					}

					serverError = true;
					continue;
				}
				catch (Throwable kse) {
					logger.error(	resource.getBundle(	"message.send.catch.server.error",
														struct.getTranPr()),
									kse);
					this.adapter.releaseConnection();
					// logging popup buffer logging
//*
					//logger.info("send error!!!!!!!!!");
					
					/*
					if(sendFailRetryFlag == true) {
						result.setMsgLog(struct, KskyB.STATUS_SEND_RETRY,
								KskyB.RSLT_SEND_ERROR, useBuffer);												
					}
					else {
						result.setMsgLog(struct, KskyB.STATUS_SEND_ERROR,
								KskyB.RSLT_SEND_ERROR, useBuffer);
										
					}
					*/
					result.setMsgLog(struct, KskyB.STATUS_SEND_ERROR,
							KskyB.RSLT_SEND_ERROR, useBuffer);
				}

				break;
			}
			//Thread.sleep(10);
		}
	}

	/**
	 * 
	 */
	final AuthEvent.Listener listener = new AuthEvent.Listener(this) {

		/**
		 * @see com.kskyb.magent.AuthEvent.Listener#isTarget(int)
		 */
		@Override
		protected boolean isTarget(int actorType) {
			return actorType == IAuthActor.MMS;
		}
	};

	/**
	 * validation when startup <br>
	 * 초기화 유효성 검증
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validate_SmsSourceBufferMonitorTask() {
		if (this.isValidTask()) {
			this.adapter.startMonitorThread();
		}
	}

	/**
	 * gw adapter <br>
	 * 서버 어답터
	 */
	@MemberField(nullable = false, elementName = "mms-gate")
	MMSAdapter adapter = null;
}
