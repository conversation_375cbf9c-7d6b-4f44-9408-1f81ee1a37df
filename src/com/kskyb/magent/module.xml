<?xml version="1.0" encoding="EUC-KR"?>

<!-- SMS SERVICE -->
<#if sms_service??>
<#else>
<#assign sms_service="false"/>
</#if>
<#if
sms_send_service??>
<#else>
<#assign
sms_send_service>${sms_service}</#assign>
</#if>
<#if
sms_report_service??>
<#else>
<#assign
sms_report_service>${sms_service}</#assign>
</#if>

<!-- MMS SERVICE -->
<#if mms_service??>
<#else>
<#assign mms_service="false"/>
</#if>
<#if
mms_send_service??>
<#else>
<#assign
mms_send_service>${mms_service}</#assign>
</#if>
<#if
mms_report_service??>
<#else>
<#assign
mms_report_service>${mms_service}</#assign>
</#if>

<!-- SMS TTL -->
<#if sms_send_ttl??>
<#else>
<#assign sms_send_ttl="0" />
</#if>
<#if
mms_send_ttl??>
<#else>
<#assign mms_send_ttl="0" />
</#if>

<#if
db_base_charset??>
<#else>
<#assign db_base_charset="KSC5601" />
</#if>
<#if db_in_charset??>
<#else>
<#assign
db_in_charset="${db_base_charset}"
/>
</#if>
<#if db_out_charset??>
<#else>
<#assign
db_out_charset="${db_in_charset}" />
</#if>

<#if
sms_migrate_log??>
<#else>
<#assign sms_migrate_log="false" />
</#if>
<#if mms_migrate_log??>
<#else>
<#assign mms_migrate_log="false" />
</#if>


<!-- SMS MIGRATION -->
<#if sms_migrate_log_table??>
<#if sms_migrate_log_table == "log">
<#assign sms_migrate_log_table_sigle="true"/>
<#else>
<#assign
sms_migrate_log_table_sigle="false"/>
</#if>
<#else>
<#assign
sms_migrate_log_table="log"/>
<#assign
sms_migrate_log_table_sigle="true"/>
</#if>

<!-- MMS MIGRATION -->
<#if mms_migrate_log_table??>
<#if mms_migrate_log_table == "log">
<#assign mms_migrate_log_table_sigle="true"/>
<#else>
<#assign
mms_migrate_log_table_sigle="false"/>
</#if>
<#else>
<#assign
mms_migrate_log_table="log"/>
<#assign
mms_migrate_log_table_sigle="true"/>
</#if>

<#if sms_gw_port??>
<#else>
<#assign sms_gw_port="10000"/>
</#if>

<#if sms_gw_send_port??>
<#else>
<#assign
sms_gw_send_port="${sms_gw_port}"/>
</#if>
<#if
sms_gw_report_port??>
<#else>
<#assign
sms_gw_report_port="${sms_gw_port}"/>
</#if>

<#if
admin_port??>
<#else>
<#assign admin_port="1024"/>
</#if>

<#if server_error_wait_interval??>
<#else>
<#assign
server_error_wait_interval="30"/>
</#if>

<#if
sms_auth_error_wait_interval??>
<#else>
<#assign
sms_auth_error_wait_interval="${server_error_wait_interval}"/>
</#if>
<#if mms_auth_error_wait_interval??>
<#else>
<#assign
mms_auth_error_wait_interval="${server_error_wait_interval}"/>
</#if>


<#if sms_send_buffer_empty_wait??>
<#else>
<#assign
sms_send_buffer_empty_wait="5"/>
</#if>
<#if
mms_send_buffer_empty_wait??>
<#else>
<#assign mms_send_buffer_empty_wait="5"/>
</#if>

<CONF>
	<module
		id="buffer.manager"
		clazz="com.kskyb.magent.struct.BufferManager">
		<sms-send-buffer-limit>${sms_send_buffer_limit}
		</sms-send-buffer-limit>
		<sms-report-buffer-limit>${sms_report_buffer_limit}
		</sms-report-buffer-limit>
		<mms-send-buffer-limit>${mms_send_buffer_limit}
		</mms-send-buffer-limit>
		<mms-result-buffer-limit>${mms_result_buffer_limit}
		</mms-result-buffer-limit>
	</module>

	<module
		id="resource.manager"
		clazz="com.kskyb.magent.config.ResourceTableManager">
		<resource>
			<#if sms_service=="true">
			<resource-hash
				id="sms.resource"
				clazz="com.kskyb.magent.config.ResourceTableInfo"
				singletone="true">
				<connect-id>${db_type}</connect-id>
				<sql-set>sql.set.sms</sql-set>
				<table-name>${table_kb_tran}</table-name>
				<check-query>table-exists-scan</check-query>
				<create-query>
					sms-create-source-table,
					sms-create-source-table-idx1,
					sms-create-source-table-idx2,
					sms-create-source-table-idx3,
					sms-create-source-table-idx4					
				</create-query>
			</resource-hash>
			</#if>
			<#if sms_service=="true" && sms_migrate_log=="true">
			<resource-hash
				id="sms.migration"
				clazz="com.kskyb.magent.config.ResourceTableInfo"
				singletone="${sms_migrate_log_table_sigle}">
				<connect-id>${db_type}</connect-id>
				<sql-set>sql.set.sms</sql-set>
				<table-name><#if
					sms_migrate_log_table=="log">${table_kb_tran_log}<#else>'${table_kb_tran_log}'yyyyMM</#if>
				</table-name>
				<check-query>table-exists-scan</check-query>
				<create-query>
					sms-create-migration-table,
					sms-create-migration-table-idx1,
					sms-create-migration-table-idx2,
					sms-create-migration-table-idx3,
					sms-create-migration-table-idx4,
					sms-create-migration-table-idx5					
				</create-query>
			</resource-hash>
			</#if>
			<#if mms_service=="true">
			<resource-hash
				id="mms.source.msg"
				clazz="com.kskyb.magent.config.ResourceTableInfo"
				singletone="true">
				<connect-id>${db_type}</connect-id>
				<sql-set>sql.set.mms</sql-set>
				<table-name>${table_kb_mms_msg}</table-name>
				<check-query>table-exists-scan</check-query>
				<create-query>
					mms-create-source-msg-table,
					mms-create-source-msg-table-idx1,
					mms-create-source-msg-table-idx2,
					mms-create-source-msg-table-idx3,
					mms-create-source-msg-table-idx4					
				</create-query>
			</resource-hash>
			<resource-hash
				id="mms.source.content"
				clazz="com.kskyb.magent.config.ResourceTableInfo"
				singletone="true">
				<connect-id>${db_type}</connect-id>
				<sql-set>sql.set.mms</sql-set>
				<table-name>${table_kb_mms_contents}</table-name>
				<check-query>table-exists-scan</check-query>
				<create-query>mms-create-source-content-table</create-query>
			</resource-hash>			
			</#if>
			<#if mms_service=="true" && mms_migrate_log=="true">
			<resource-hash
				id="mms.migration.msg"
				clazz="com.kskyb.magent.config.ResourceTableInfo"
				singletone="${mms_migrate_log_table_sigle}">
				<connect-id>${db_type}</connect-id>
				<sql-set>sql.set.mms</sql-set>
				<table-name><#if mms_migrate_log_table=="log">${table_kb_mms_msg_log}<#else>'${table_kb_mms_msg_log}'yyyyMM</#if>
				</table-name>
				<check-query>table-exists-scan</check-query>
				<create-query>
					mms-create-migration-msg-table,
					mms-create-migration-msg-table-idx1,
					mms-create-migration-msg-table-idx2,
					mms-create-migration-msg-table-idx3,
					mms-create-migration-msg-table-idx4,
					mms-create-migration-msg-table-idx5					
				</create-query>
			</resource-hash>
			<resource-hash
				id="mms.migration.content"
				clazz="com.kskyb.magent.config.ResourceTableInfo"
				singletone="${mms_migrate_log_table_sigle}">
				<connect-id>${db_type}</connect-id>
				<sql-set>sql.set.mms</sql-set>
				<table-name><#if 
					mms_migrate_log_table=="log">${table_kb_mms_contents_log}<#else>'${table_kb_mms_contents_log}'yyyyMM</#if>
				</table-name>
				<check-query>table-exists-scan</check-query>
				<create-query>mms-create-migration-content-table</create-query>
			</resource-hash>
			</#if>
		</resource>
	</module>

	<module
		id="ttl.checker"
		clazz="com.kskyb.magent.util.TTLChecker">
		<sms-ttl><#if sms_send_ttl??>${sms_send_ttl}<#else>0</#if></sms-ttl>
		<mms-ttl><#if mms_send_ttl??>${mms_send_ttl}<#else>0</#if></mms-ttl>
	</module>

	<module
		id="mime.config"
		clazz="com.kskyb.magent.struct.MimeTypeConfig">
		<!-- 0 : text 1 : image \\$\\{aaa\\} 2 : audio 3 : mpeg 동영상 -->
		<code>
			<code-hath id="TXT">0</code-hath>
			<code-hath id="IMG">1</code-hath>
			<code-hath id="SND">2</code-hath>
			<code-hath id="VDO">3</code-hath>
			<code-hath id="VOD">3</code-hath>
			<code-hath id="MPG">3</code-hath>
		</code>
	</module>
	<module
		clazz="com.kskyb.broker.service.scheduler.ScheduleContainerServiceImpl"
		serviceName="task-manager"
		startOnRegist="true">
		<executor-pool
			clazz="com.kskyb.broker.async.ExecutorPool"
			maxIdleTimeMs="60000"
			maxThreads="100"
			minThreads="1"
			transaction="false">
		</executor-pool>
	</module>
	<task>
		<#if sms_service == "true">
		<task-hash
			name="@sms.log.buffer.scan@"
			clazz="com.kskyb.magent.task.SmsLogBufferMonitorTask"
			validTask="true">
			<desc>SMS 로그버퍼 업데이트 Task</desc>
			<container-service-name>task-manager</container-service-name>
			<resource-target-id>sms.resource</resource-target-id>
			<resource-driver>${db_connect_driver}</resource-driver>
			<resource-url>${db_connect_url}</resource-url>
			<resource-id>${db_connect_id}</resource-id>
			<resource-pass><![CDATA[${db_connect_pass}]]></resource-pass>
			<sql-set>sql.set.sms</sql-set>
			<sql-exception-filter>sql.exception.codes</sql-exception-filter>
			<connect-id>${db_type}</connect-id>
			<info-add><![CDATA[<#if select_additional_info??>${select_additional_info}</#if>]]></info-add>
			<timer
				id="sms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${sms_select_target_interval}</period>
				<pattern>${sms_select_target_interval_pattern}</pattern>
			</timer>
			<empty-buffer-wait>30000</empty-buffer-wait>
		</task-hash>
		<#if sms_send_service == "true">
		<task-hash
			name="@sms.table.scan@"
			broadcast="sms.table.scan"
			clazz="com.kskyb.magent.task.SmsSourceTableScanTask"
			validTask="true">
			<container-service-name>task-manager</container-service-name>
			<desc>SMS 원본 테이블 모니터링 Task</desc>
			<resource-target-id>sms.resource</resource-target-id>
			<resource-driver>${db_connect_driver}</resource-driver>
			<resource-url>${db_connect_url}</resource-url>
			<resource-id>${db_connect_id}</resource-id>
			<resource-pass><![CDATA[${db_connect_pass}]]></resource-pass>
			<sql-set>sql.set.sms</sql-set>
			<sql-exception-filter>sql.exception.codes</sql-exception-filter>
			<connect-id>${db_type}</connect-id>
			<info-add><![CDATA[<#if sms_select_additional_info??>${sms_select_additional_info}</#if>]]></info-add>
			<timer
				id="sms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${sms_select_target_interval}000</period>
				<pattern>${sms_select_target_interval_pattern}</pattern>
				<#if sms_send_limit_time??>
				<except-time>${sms_send_limit_time}</except-time>
				</#if>
			</timer>
						<#if sms_send_limit_time2??>
				<except-time2>${sms_send_limit_time2}</except-time2>
			</#if>
			<update-outof-ttl>${sms_send_ttl}</update-outof-ttl>
			<table-empty-wait>${sms_select_target_interval}000
			</table-empty-wait>
		</task-hash>
		<#list sms_send_auth as t>
		<task-hash
			name="@sms.send.${t.id}@"
			clazz="com.kskyb.magent.task.SmsSourceBufferMonitorTask"
			validTask="true">
			<desc>SMS 송신 버퍼 모니터링 (${t.id})</desc>
			<container-service-name>task-manager</container-service-name>
			<timer
				id="sms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${sms_select_target_interval}</period>
				<pattern>${sms_select_target_interval_pattern}</pattern>
			</timer>
			<sms-gate
				name="SMS Send Adapter"
				clazz="com.kskyb.magent.adapter.SMSAdapter"
				send="true">
				<agent-version>${agent_version}</agent-version>
				<auth
					id="sms.table.scan.auth"
					clazz="com.kskyb.magent.struct.AuthStruct">
					<agent-version>${agent_version}</agent-version>
					<connect-host-ip>${sms_gw_ip}</connect-host-ip>
					<connect-host-port>${sms_gw_send_port}</connect-host-port>
					<login-id>${t.id}</login-id>
					<login-pass>${t.pass}</login-pass>
				</auth>
				<check-interval>${sms_gw_send_check_interval}000</check-interval>
				<ping-interval>${sms_gw_send_ping_interval}000</ping-interval>
				<auth-error-interval>${sms_auth_error_wait_interval}000
				</auth-error-interval>
				<#if sms_server_error_interval??>
				<server-error-wait>${sms_server_error_interval}000
				</server-error-wait>
				</#if>
				<eof-wait-interval><#if
					sms_send_eof_wait_time??>${sms_send_eof_wait_time}000<#else>3000</#if>
				</eof-wait-interval>
				<eof-wait-count><#if
					sms_send_eof_wait_count??>${sms_send_eof_wait_count}<#else>3</#if>
				</eof-wait-count>
			</sms-gate>
			<server-error-wait-interval>${server_error_wait_interval}000
			</server-error-wait-interval>
			<empty-buffer-wait>${sms_send_buffer_empty_wait}000
			</empty-buffer-wait>
			<auth-error-wait-interval>${sms_auth_error_wait_interval}000
			</auth-error-wait-interval>
			<eof-delay-time><#if
				sms_gw_eof_delay_time??>${sms_gw_eof_delay_time}000<#else>500</#if>
			</eof-delay-time>
		</task-hash>
		</#list>
		</#if>
		<#if sms_report_service == "true">
		<#list sms_report_auth as t>
		<task-hash
			name="@sms.report.${t.id}@"
			clazz="com.kskyb.magent.task.SmsReportReceiveTask"
			validTask="true">
			<desc>SMS Report Listen ( ${t.id} )</desc>
			<container-service-name>task-manager</container-service-name>
			<timer
				id="sms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${sms_select_target_interval}</period>
				<pattern>${sms_select_target_interval_pattern}</pattern>
			</timer>
			<sms-gate
				name="SMS Report Adapter"
				clazz="com.kskyb.magent.adapter.SMSAdapter"
				send="false">
				<agent-version>${agent_version}</agent-version>
				<auth
					id="sms.table.scan.auth"
					clazz="com.kskyb.magent.struct.AuthStruct">
					<agent-version>${agent_version}</agent-version>
					<connect-host-ip>${sms_gw_ip}</connect-host-ip>
					<connect-host-port>${sms_gw_report_port}</connect-host-port>
					<login-id>${t.id}</login-id>
					<login-pass>${t.pass}</login-pass>
				</auth>
				<check-interval>${sms_gw_report_check_interval}000</check-interval>
				<ping-interval>${sms_gw_report_ping_interval}000</ping-interval>
				<auth-error-interval>${sms_auth_error_wait_interval}000
				</auth-error-interval>
				<#if sms_server_error_interval??>
				<server-error-wait>${sms_server_error_interval}000
				</server-error-wait>
				</#if>
				<eof-wait-interval><#if
					sms_report_eof_wait_time??>${sms_report_eof_wait_time}000<#else>3000</#if>
				</eof-wait-interval>
				<eof-wait-count><#if
					sms_report_eof_wait_count??>${sms_report_eof_wait_count}<#else>3</#if>
				</eof-wait-count>
			</sms-gate>
			<auth-error-wait-interval>${sms_auth_error_wait_interval}000
			</auth-error-wait-interval>
			<server-error-wait-interval>${server_error_wait_interval}000
			</server-error-wait-interval>
			<eof-delay-time><#if
				sms_gw_eof_delay_time??>${sms_gw_eof_delay_time}000<#else>500</#if>
			</eof-delay-time>
		</task-hash>
		</#list>
		</#if>
		<#if sms_service=="true" && sms_migrate_log=="true">
		<task-hash
			name="@sms.log.migration@"
			clazz="com.kskyb.magent.task.SmsLogMigrationTask"
			validTask="${sms_service}">
			<desc>SMS 로그 마이그레이션 Task</desc>
			<container-service-name>task-manager</container-service-name>
			<resource-source-id>sms.resource</resource-source-id>
			<resource-target-id>sms.migration</resource-target-id>
			<resource-driver>${db_connect_driver}</resource-driver>
			<resource-url>${db_connect_url}</resource-url>
			<resource-id>${db_connect_id}</resource-id>
			<resource-pass><![CDATA[${db_connect_pass}]]></resource-pass>
			<sql-set>sql.set.sms</sql-set>
			<sql-exception-filter>sql.exception.codes</sql-exception-filter>
			<connect-id>${db_type}</connect-id>
			<info-add><![CDATA[<#if select_additional_info??>${select_additional_info}</#if>]]></info-add>
			<timer
				id="sms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${sms_migrate_log_interval}000</period>
				<pattern>${sms_migrate_log_interval_pattern}</pattern>
				<resource-driver>${db_connect_driver}</resource-driver>
				<resource-url>${db_connect_url}</resource-url>
				<resource-id>${db_connect_id}</resource-id>
				<resource-pass><![CDATA[${db_connect_pass}]]></resource-pass>
			</timer>
		</task-hash>
		</#if>
		</#if>

		<#if mms_service == "true">
		<#list mms_send_auth as t>
		<task-hash
			name="@mms.log.buffer.${t.id}@"
			clazz="com.kskyb.magent.task.MmsLogBufferMonitorTask"
			validTask="true">
			<desc>MMS 로그버퍼 업데이트 ( ${t.id} )</desc>
			<container-service-name>task-manager</container-service-name>
			<sql-set>sql.set.mms</sql-set>
			<sql-exception-filter>sql.exception.codes</sql-exception-filter>
			<connect-id>${db_type}</connect-id>
			<info-add><![CDATA[<#if select_additional_info_mms??>${select_additional_info_mms}</#if>]]></info-add>
			<timer
				id="mms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${mms_select_target_interval}</period>
				<pattern>${mms_select_target_interval_pattern}</pattern>
			</timer>
			<empty-buffer-wait>30000</empty-buffer-wait>					
			<resource-content-id>mms.source.content</resource-content-id>
			<resource-msg-id>mms.source.msg</resource-msg-id>
			<resource-driver>${db_connect_driver}</resource-driver>
			<resource-url>${db_connect_url}</resource-url>
			<resource-id>${db_connect_id}</resource-id>
			<resource-pass><![CDATA[${db_connect_pass}]]></resource-pass>
		</task-hash>
		</#list>
				
		<#if mms_send_service=="true">
		<!-- MMS Section ${mms_service} -->
		<task-hash
			name="@mms.table.scan@"
			broadcast="mms.table.scan"
			clazz="com.kskyb.magent.task.MmsSourceTableScanTask"
			validTask="true">
			<desc>MMS 원본 테이블 모니터링 Task</desc>
			<container-service-name>task-manager</container-service-name>
			<sql-set>sql.set.mms</sql-set>
			<sql-exception-filter>sql.exception.codes</sql-exception-filter>
			<connect-id>${db_type}</connect-id>
			<info-add><![CDATA[<#if select_additional_info_mms??>${select_additional_info_mms}</#if>]]></info-add>
			<timer
				id="mms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${mms_select_target_interval}000</period>
				<pattern>${mms_select_target_interval_pattern}</pattern>
				<#if mms_send_limit_time??>
				<except-time>${mms_send_limit_time}</except-time>
				</#if>
			</timer>		
			<#if mms_send_limit_time2??>
				<except-time2>${mms_send_limit_time2}</except-time2>
			</#if>	
			<resource-content-id>mms.source.content</resource-content-id>
  			<resource-msg-id>mms.source.msg</resource-msg-id>
			<update-outof-ttl>${mms_send_ttl}</update-outof-ttl>
			<table-empty-wait>${mms_select_target_interval}000
			</table-empty-wait>
		</task-hash>
		<#list mms_send_auth as t>
		<task-hash
			name="@mms.send.${t.id}@"
			clazz="com.kskyb.magent.task.MmsSourceBufferMonitorTask"
			validTask="true">
			<desc>MMS 송신 버퍼 모니터링 ( ${t.id} )</desc>
			<container-service-name>task-manager</container-service-name>
			<timer
				id="mms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${mms_select_target_interval}000</period>
				<pattern>${mms_select_target_interval_pattern}</pattern>
			</timer>
			<mms-gate
				name="MMS Send Adapter.${t.id}"
				clazz="com.kskyb.magent.adapter.MMSAdapter"
				send="true">
				<agent-version>${agent_version}</agent-version>
				<auth
					id="mms.table.scan.auth"
					clazz="com.kskyb.magent.struct.AuthStruct">
					<agent-version>${agent_version}</agent-version>
					<connect-host-ip>${mms_gw_ip}</connect-host-ip>
					<connect-host-port>${mms_gw_port}</connect-host-port>
					<login-id>${t.id}</login-id>
					<login-pass>${t.pass}</login-pass>
				</auth>
				<check-interval>${mms_gw_send_check_interval}000</check-interval>
				<ping-interval>${mms_gw_send_ping_interval}000</ping-interval>
				<auth-error-interval>${mms_auth_error_wait_interval}000
				</auth-error-interval>
				<#if mms_server_error_interval??>
				<server-error-wait>${mms_server_error_interval}000
				</server-error-wait>
				</#if>
				<eof-wait-interval><#if
					mms_report_eof_wait_time??>${mms_report_eof_wait_time}000<#else>3000</#if>
				</eof-wait-interval>
				<eof-wait-count><#if
					mms_report_eof_wait_count??>${mms_report_eof_wait_count}<#else>3</#if>
				</eof-wait-count>
			</mms-gate>
			<server-error-wait-interval>${server_error_wait_interval}000
			</server-error-wait-interval>
			<empty-buffer-wait>${mms_send_buffer_empty_wait}000
			</empty-buffer-wait>
			<auth-error-wait-interval>${mms_auth_error_wait_interval}000
			</auth-error-wait-interval>
			<eof-delay-time><#if
				mms_gw_eof_delay_time??>${mms_gw_eof_delay_time}000<#else>500</#if>
			</eof-delay-time>
		</task-hash>
		</#list>
		</#if>
		<#if mms_report_service=="true">
		<#list mms_report_auth as t>
		<task-hash
			name="@mms.report.${t.id}@"
			clazz="com.kskyb.magent.task.MmsReportReceiveTask"
			validTask="true">
			<desc>MMS Report Listen ( ${t.id} )</desc>
			<container-service-name>task-manager</container-service-name>
			<timer
				id="mms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${mms_select_target_interval}000</period>
				<pattern>${mms_select_target_interval_pattern}</pattern>
			</timer>
			<mms-gate
				name="MMS Report Adapter.${t.id}"
				clazz="com.kskyb.magent.adapter.MMSAdapter"
				send="false">
				<agent-version>${agent_version}</agent-version>
				<auth
					id="mms.table.scan.auth"
					clazz="com.kskyb.magent.struct.AuthStruct">
					<agent-version>${agent_version}</agent-version>
					<connect-host-ip>${mms_gw_ip}</connect-host-ip>
					<connect-host-port>${mms_gw_port}</connect-host-port>
					<login-id>${t.id}</login-id>
					<login-pass>${t.pass}</login-pass>
				</auth>
				<check-interval>${mms_gw_report_check_interval}000</check-interval>
				<ping-interval>${mms_gw_report_ping_interval}000</ping-interval>
				<auth-error-interval>${mms_auth_error_wait_interval}000
				</auth-error-interval>
				<#if mms_server_error_interval??>
				<server-error-wait>${mms_server_error_interval}000
				</server-error-wait>
				</#if>
				<eof-wait-interval><#if
					mms_report_eof_wait_time??>${mms_report_eof_wait_time}000<#else>3000</#if>
				</eof-wait-interval>
				<eof-wait-count><#if
					mms_report_eof_wait_count??>${mms_report_eof_wait_count}<#else>3</#if>
				</eof-wait-count>
			</mms-gate>
			<server-error-wait-interval>${server_error_wait_interval}000
			</server-error-wait-interval>
			<auth-error-wait-interval>${mms_auth_error_wait_interval}000
			</auth-error-wait-interval>
			<eof-delay-time><#if
				mms_gw_eof_delay_time??>${mms_gw_eof_delay_time}000<#else>500</#if>
			</eof-delay-time>
		</task-hash>
		</#list>
		</#if>
		<#if mms_service=="true" && mms_migrate_log=="true">
		<!-- MMS Section ${mms_service} | ${mms_migrate_log} -->
		<task-hash
			name="@mms.log.migration@"
			clazz="com.kskyb.magent.task.MmsLogMigrationTask"
			validTask="true">
			<desc>MMS 로그 마이그레이션 Task</desc>
			<container-service-name>task-manager</container-service-name>
			<sql-set>sql.set.mms</sql-set>
			<sql-exception-filter>sql.exception.codes</sql-exception-filter>
			<connect-id>${db_type}</connect-id>
			<info-add><![CDATA[<#if select_additional_info??>${select_additional_info}</#if>]]></info-add>
			<timer
				id="mms.table.scan.timer"
				clazz="com.kskyb.broker.service.scheduler.HeartBeatTimer">
				<period>${mms_migrate_log_interval}000</period>
				<pattern>${mms_migrate_log_interval_pattern}</pattern>
			</timer>			
			<resource-content-source-id>mms.source.content</resource-content-source-id>
			<resource-msg-source-id>mms.source.msg</resource-msg-source-id>
			<resource-content-target-id>mms.migration.content</resource-content-target-id>
			<resource-msg-target-id>mms.migration.msg</resource-msg-target-id>
			<resource-driver>${db_connect_driver}</resource-driver>
			<resource-url>${db_connect_url}</resource-url>
			<resource-id>${db_connect_id}</resource-id>
			<resource-pass><![CDATA[${db_connect_pass}]]></resource-pass>
		</task-hash>
		</#if>
		</#if>
	</task>
	<service
		clazz="com.kskyb.broker.service.pool.thread.BridgeThreadPoolService"
		serviceName="context-thread-pool"
		startOnRegist="true">
		<desc>context-thread-pool</desc>
		<note>context-thread-pool</note>
		<executor-pool
			clazz="com.kskyb.broker.async.ExecutorPool"
			maxIdleTimeMs="60000"
			maxThreads="<#if context_runtime_max_thread??>${context_runtime_max_thread}<#else>100</#if>"
			maxSpareThreads="<#if context_runtime_max_thread??>${context_runtime_max_thread}<#else>100</#if>"
			minSpareThreads="<#if context_runtime_min_thread??>${context_runtime_min_thread}<#else>2</#if>"
			transaction="false">
		</executor-pool>
	</service>
	<container
		clazz="com.kskyb.broker.service.net.bio.transport.socket.NetBioServerSocketContainer"
		serviceName="context.tcp.service"
		startOnRegist="true">
		<desc>context-tcp-service</desc>
		<note>context-tcp-service</note>
		<thread-pool-name>context-thread-pool</thread-pool-name>
		<acceptor
			id="context.tcp.service.acceptor"
			name="context.tcp.service.acceptor"
			clazz="com.kskyb.broker.service.net.bio.transport.socket.ServerSocketIoAcceptor">
			<timer-queue-factor>100</timer-queue-factor>
			<session-buffering-limit>100</session-buffering-limit>
			<pool-popup-delay-time>1000</pool-popup-delay-time>
			<pool-empty-wait-time>1000</pool-empty-wait-time>
		</acceptor>
	</container>
	<service-config
		clazz="com.kskyb.broker.service.net.bio.transport.socket.SocketIoAcceptorConfig"
		name="protocol.context.connector">
		<desc>protocol.context.connector</desc>
		<note>protocol.context.connector</note>
		<service-container>context.tcp.service</service-container>
		<socket-service-info clazz="com.kskyb.broker.service.net.transport.socket.SocketServiceInfo">
			<service-address><#if bind_service_address??>${admin_service_address}<#else>127.0.0.1</#if></service-address>
			<service-port>${admin_port}</service-port>
		</socket-service-info>
		<default-write-timeout><#if context_service_timeout??>${context_service_timeout}<#else>10000</#if></default-write-timeout>
		<default-read-timeout><#if context_service_timeout??>${context_service_timeout}<#else>10000</#if></default-read-timeout>
		<default-wait-timeout><#if context_service_timeout??>${context_service_timeout}<#else>10000</#if></default-wait-timeout>
		<buffer-depth>18</buffer-depth>
		<codec-factory
			clazz="com.kskyb.broker.service.net.codec.StringCodecFactory"
			charset="KSC5601" />
		<io-filter-chain
			clazz="com.kskyb.broker.service.net.filter.impl.CommonIoFilterChain"
			name="messaging-io-filter-chain">
			<filter>
				<filter-list
					clazz="com.kskyb.broker.service.net.filter.impl.TelnetMessageIoFilter"
					name="messaging-telnet-io-filter" />
			</filter>
			<disconnect-when-idle-detect>true</disconnect-when-idle-detect>
		</io-filter-chain>
		<message-handler
			clazz="com.kskyb.broker.service.context.comm.ContextMessageHandler"
			name="context-data-filter-chain">
			<command-context-name>context.command</command-context-name>
			<welcome-message>Welcome KskyB Agent console Manager</welcome-message>
			<blank-command-message>
				command list
				status: echo buffer status
				tasks: echo task status
				resize: resize buffer size
				shutdown: Shutdown magent
				quit: Exit manager
			</blank-command-message>
			<message-lookup-fail>cannot find command</message-lookup-fail>
			<message-execute-fail>execute error</message-execute-fail>
		</message-handler>
	</service-config>

	<context-command clazz="com.kskyb.magent.manage.BufferStatusEchoCommandEntry">
		<context-id>context.command</context-id>
		<command-id>status</command-id>
		<command-desc>echo buffer status</command-desc>
	</context-command>
	<context-command clazz="com.kskyb.magent.manage.BufferResizeCommandEntry">
		<context-id>context.command</context-id>
		<command-id>resize</command-id>
		<command-desc>resize buffer size</command-desc>
	</context-command>
	<context-command clazz="com.kskyb.magent.manage.TaskStatusEchoCommandEntry">
		<context-id>context.command</context-id>
		<command-id>tasks</command-id>
		<command-desc>echo task status</command-desc>
	</context-command>
	<context-command clazz="com.kskyb.magent.manage.ShutdownCommandEntry">
		<context-id>context.command</context-id>
		<command-id>shutdown</command-id>
		<command-desc>shutdown magent</command-desc>
	</context-command>
	<context-command clazz="com.kskyb.magent.manage.QuiteCommandEntry">
		<context-id>context.command</context-id>
		<command-id>quit</command-id>
		<command-desc>exit manager</command-desc>
	</context-command>
</CONF>
