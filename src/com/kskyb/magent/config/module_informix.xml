<?xml version="1.0" encoding="EUC-KR"?>
<#if sms_migrate_ttl??>
<#else>
	<#assign sms_migrate_ttl="36"/>
</#if>
<#if mms_migrate_ttl??>
<#else>
	<#assign mms_migrate_ttl="36"/>
</#if>
<CONF>

<!--
<module id="${db_type}" clazz="com.kskyb.broker.service.pool.jdbc.CommonPooledConnectionPool"
		broadcast="connection.local.pool"
		max="20" min="1" factor="0.5" emptyWait="-1" defaultEntry="true">
	<factory clazz="com.kskyb.broker.service.pool.jdbc.CommonPooledConnectionFactory">
		<connection-config clazz="com.kskyb.broker.service.pool.jdbc.PoolConfig" authCommit="true">
			<driver>${db_connect_driver}</driver>
			<url>${db_connect_url}</url>
			<#if db_connect_id??><user>${db_connect_id}</user></#if>
			<#if db_connect_pass??><password><![CDATA[${db_connect_pass}]]></password></#if>
			<#if db_base_charset??><base-charset>${db_base_charset}</base-charset></#if>
			<#if db_in_charset??><incoming-charset>${db_in_charset}</incoming-charset></#if>
			<#if db_out_charset??><outgoing-charset>${db_out_charset}</outgoing-charset></#if>
			<wait-on-handshake-fail>${wait_on_handshake_fail}</wait-on-handshake-fail>
			<wait-on-handshake-fail-interval>${wait_on_handshake_fail_interval}000</wait-on-handshake-fail-interval>
			<validation-query>SELECT SYSDATE FROM DUAL</validation-query>
		</connection-config>
	</factory>
</module>
--> 
<!-- <#if db_base_charset??><#else><#assign db_base_charset="KSC5601"></#if> -->
<!-- <#if db_in_charset??><#else><#assign db_in_charset="KSC5601"></#if> -->
<!-- <#if db_out_charset??><#else><#assign db_out_charset="KSC5601"></#if> -->
<module
	clazz="com.kskyb.broker.service.pool.db.DataSourcePoolService"
	serviceName="${db_type}"
	startOnRegist="true"
	defaultEntry="true"
	max="20"
	min="1"
	limit="20"
	idleLimit="60000"
	emptyWait="1000">
	<desc>${db_type}</desc>
	<note>${db_type}</note>
	<thread-usage-expire-interval>3600000</thread-usage-expire-interval>
	<thread-usage-monitor-interval>360000</thread-usage-monitor-interval>
	<data-source
		clazz="com.kskyb.broker.util.jdbc.datasource.DataSourceDelegate"
		initial="${db_name}"
		autoCommit="true">
		<driver>${db_connect_driver}</driver>
		<url>${db_connect_url}</url>
		<#if db_connect_id??><user>${db_connect_id}</user></#if>
		<#if db_connect_pass??><password><![CDATA[${db_connect_pass}]]></password></#if>
		<charsets>${db_base_charset},${db_in_charset},${db_out_charset}</charsets>
		<activate-query>SELECT * FROM SYSTABLES</activate-query>
		<#if extractor??>
		<extractor>${extractor}</extractor>
		</#if>
	</data-source>
	<handler
		clazz="com.kskyb.broker.service.pool.PoolBoundaryHandler">
	</handler>
</module>

<module broadcast="sql.exception.codes" clazz="com.kskyb.broker.adapter.jdbc.SQLExceptionFilter">
	<code>
		<code-hash id="0">connect</code-hash>
		<code-hash id="1146">object</code-hash>
		<code-hash id="1064">syntax</code-hash>
	</code>
</module>

<#assign sms_send_ttl><#if sms_send_ttl??>${sms_send_ttl}<#else>0</#if></#assign>
<#assign mms_send_ttl><#if mms_send_ttl??>${mms_send_ttl}<#else>0</#if></#assign>

<module broadcast="sql.set.sms" clazz="com.kskyb.broker.util.jdbc.SQLSet">
	<sql>
		<sql-hash id="table-exists-scan"><![CDATA[
			SELECT * FROM SYSTABLES WHERE UPPER(TABNAME)=UPPER('#TABLE_NAME#')
		]]></sql-hash>
		<sql-hash id="sms-create-source-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				 TRAN_PR            INTEGER NOT NULL PRIMARY KEY ,
				 TRAN_REFKEY        VARCHAR(20) ,
				 TRAN_ID            VARCHAR(20) ,
				 TRAN_PHONE         VARCHAR(15) NOT NULL ,
				 TRAN_CALLBACK      VARCHAR(15) NOT NULL ,
				 TRAN_MSG           VARCHAR(150) NOT NULL ,
				 TRAN_DATE          DATETIME YEAR TO SECOND NOT NULL ,
				 TRAN_TYPE          INTEGER  DEFAULT 0 NOT NULL ,
				 TRAN_STATUS        CHAR(1) DEFAULT '1' NOT NULL,
				 TRAN_SENDDATE      DATETIME YEAR TO SECOND,
				 TRAN_REPORTDATE    DATETIME YEAR TO SECOND,
				 TRAN_RSLTDATE      DATETIME YEAR TO SECOND,
				 TRAN_RSLT          CHAR(2) ,
				 TRAN_ETC1          VARCHAR(160) ,
				 TRAN_ETC2          VARCHAR(160) ,
				 TRAN_ETC3          VARCHAR(160) ,
				 TRAN_ETC4          VARCHAR(160) ,
				 TRAN_ETC5          VARCHAR(160) ,
				 TRAN_END_TELCO     VARCHAR(8) ,
				 TRAN_LOG			CHAR(1) DEFAULT 'N'
			)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx1"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX1 ON #TABLE_NAME#(TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx2"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX2 ON #TABLE_NAME#(TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				 TRAN_PR            INTEGER NOT NULL ,
				 TRAN_REFKEY        VARCHAR(20) ,
				 TRAN_ID            VARCHAR(20) ,
				 TRAN_PHONE         VARCHAR(15) NOT NULL ,
				 TRAN_CALLBACK      VARCHAR(15) NOT NULL ,
				 TRAN_MSG           VARCHAR(150) NOT NULL ,
				 TRAN_DATE          DATETIME YEAR TO SECOND NOT NULL ,
				 TRAN_TYPE          INTEGER  DEFAULT 0 NOT NULL ,
				 TRAN_STATUS        CHAR(1) DEFAULT '1' NOT NULL,
				 TRAN_SENDDATE      DATETIME YEAR TO SECOND,
				 TRAN_REPORTDATE    DATETIME YEAR TO SECOND,
				 TRAN_RSLTDATE      DATETIME YEAR TO SECOND,
				 TRAN_RSLT          CHAR(2) ,
				 TRAN_ETC1          VARCHAR(160) ,
				 TRAN_ETC2          VARCHAR(160) ,
				 TRAN_ETC3          VARCHAR(160) ,
				 TRAN_ETC4          VARCHAR(160) ,
				 TRAN_ETC5          VARCHAR(160) ,
				 TRAN_END_TELCO     VARCHAR(8) ,
				 TRAN_LOG			CHAR(1) DEFAULT 'N'	
			)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx1"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX1 ON #TABLE_NAME#(TRAN_PR)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx2"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX2 ON #TABLE_NAME#(TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx3"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX3 ON #TABLE_NAME#(TRAN_DATE)
		]]></sql-hash>
		<!-- 대상을 추출한다. SmsSourceTableScanTask -->
		<sql-hash id="sms-query-update-out-of-ttl-target"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS='5',
					TRAN_RSLT='OT',
					TRAN_SENDDATE = CURRENT,
					TRAN_RSLTDATE = CURRENT,
					TRAN_REPORTDATE = CURRENT
			WHERE	TRAN_STATUS='1'
			AND		TRAN_DATE + ${sms_send_ttl} UNITS MINUTE < CURRENT
		]]></sql-hash>
		<sql-hash id="sms-query-select-checked-target"><![CDATA[
			SELECT	FIRST			${r"${LIMIT}"}
					TRAN_PR			TRAN_PR,
					TRAN_PHONE  	TRAN_PHONE,
					TRAN_CALLBACK	TRAN_CALLBACK,
					TRAN_MSG		TRAN_MSG,
<#if sms_select_additional_info??>${sms_select_additional_info}<#else>' '</#if> TRAN_REVERSE,
					TRAN_TYPE		TRAN_TYPE,
					TO_CHAR(TRAN_DATE + ${sms_send_ttl} UNITS MINUTE ,'%Y%m%d%H%M%S') TRAN_TTL
			FROM	#TABLE_NAME#
			WHERE	TRAN_STATUS='1'
			AND 	TRAN_DATE < CURRENT
			AND		TO_CHAR(TRAN_DATE, '%Y%m%d%H%M%S') > TO_CHAR(CURRENT - ${sms_send_ttl} UNITS MINUTE , '%Y%m%d%H%M%S')
			<#if sms_select_target_addon_query??>${sms_select_target_addon_query}</#if>
		]]></sql-hash>
		<!-- 전송하지 못한 상태값이 4,6번인 경우 상태값을 1로 업데이트 한다. -->
		<sql-hash id="sms-query-update-checked-nosend-target"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS = '1'
			WHERE	TRAN_STATUS IN ('4','6')
		]]></sql-hash>		
		<!-- 버퍼로 넣고나서 업데이트 한다. -->
		<sql-hash id="sms-query-update-checked-target"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS='6',
					TRAN_SENDDATE= CURRENT
			WHERE	TRAN_PR = '${r"${TRAN_PR}"}'
		]]></sql-hash>


		<!-- -->
		<sql-hash id="sms-query-update-log"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS='${r"${TRAN_STATUS}"}',
					TRAN_RSLT='${r"${TRAN_RSLT}"}'
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
			AND		TRAN_STATUS != '3'
		]]></sql-hash>
		<sql-hash id="sms-query-update-result"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS		='3',
					TRAN_RSLT		='${r"${TRAN_RSLT}"}',
					TRAN_END_TELCO	='${r"${TRAN_END_TELCO}"}',
					TRAN_RSLTDATE	=TO_DATE('${r"${TRAN_RSLTDATE}"}',  '%Y%m%d%H%M%S'),
					TRAN_REPORTDATE	=TO_DATE('${r"${TRAN_REPORTDATE}"}','%Y%m%d%H%M%S')
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
		]]></sql-hash>
		 <!-- table migration : SmsLogMigrationTask -->
		<sql-hash id="sms-query-update-migration-log"><![CDATA[
			UPDATE	#SOURCE_TABLE_NAME#
			SET		TRAN_LOG		='Y'
			WHERE	( TRAN_STATUS in ('3','5') )
			OR		( TRAN_STATUS <> '1' AND TRAN_DATE + ${sms_migrate_ttl} UNITS HOUR < CURRENT )
		]]></sql-hash>

		<sql-hash id="sms-query-copy-migration-log-before"><![CDATA[
			INSERT INTO #BEFORE_TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE < TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>
		<sql-hash id="sms-query-copy-migration-log-current"><![CDATA[
			INSERT INTO #TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
			AND		TRAN_DATE < TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>
		<sql-hash id="sms-query-copy-migration-log-after"><![CDATA[
			INSERT INTO #AFTER_TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>

		<sql-hash id="sms-query-delete-migration-log"><![CDATA[
			DELETE FROM #SOURCE_TABLE_NAME# WHERE TRAN_LOG='Y'
		]]></sql-hash>
	</sql>
</module>
<module broadcast="sql.set.mms" clazz="com.kskyb.broker.util.jdbc.SQLSet">
	<sql>
		<sql-hash id="table-exists-scan"><![CDATA[
			SELECT * FROM SYSTABLES WHERE UPPER(TABNAME)=UPPER('#TABLE_NAME#')
		]]></sql-hash>
		<sql-hash id="mms-create-source-group-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				TRAN_GRP_KEY       VARCHAR(16)		NOT NULL PRIMARY KEY,
				TRAN_REFKEY        VARCHAR(20),
				TRAN_ID            VARCHAR(20),
				TRAN_CALLBACK      VARCHAR(15)		NOT NULL,
				TRAN_SUBJECT       VARCHAR(100),
				TRAN_MSG           VARCHAR(150)	NOT NULL,
				TRAN_DATE          DATETIME YEAR TO SECOND NOT NULL,
				TRAN_TYPE          INTEGER			NOT NULL,
				TRAN_STATUS        CHAR(1) DEFAULT '1' NOT NULL,
				TRAN_SENDDATE      DATETIME YEAR TO SECOND,
				TRAN_RECEIVER_CNT  INTEGER			NOT NULL,
				TRAN_ETC1          VARCHAR(160),
				TRAN_ETC2          VARCHAR(160),
				TRAN_ETC3          VARCHAR(160),
				TRAN_ETC4          VARCHAR(160),
				TRAN_ETC5          VARCHAR(160),
				TRAN_LOG		   CHAR(1) 			DEFAULT 'N'
			)
		]]></sql-hash>
		<sql-hash id="mms-create-source-group-table-idx1"><![CDATA[
			create index idx1_#TABLE_NAME# on #TABLE_NAME# (tran_date);
		]]></sql-hash>
		<sql-hash id="mms-create-source-group-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (tran_status)
		]]></sql-hash>

		<sql-hash id="mms-create-migration-group-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				TRAN_GRP_KEY       VARCHAR(16)         	NOT NULL,
			  	TRAN_REFKEY        VARCHAR(20),
			  	TRAN_ID            VARCHAR(20),
			  	TRAN_CALLBACK      VARCHAR(15)         	NOT NULL,
			  	TRAN_SUBJECT       VARCHAR(100),
			  	TRAN_MSG           VARCHAR(150)       	NOT NULL,
			  	TRAN_DATE          DATETIME YEAR TO SECOND NOT NULL,
			  	TRAN_TYPE          INTEGER              NOT NULL,
			  	TRAN_STATUS        CHAR(1)  DEFAULT '1' NOT NULL,
			  	TRAN_SENDDATE      DATETIME YEAR TO SECOND,
			  	TRAN_RECEIVER_CNT  INTEGER				NOT NULL,
			  	TRAN_ETC1          VARCHAR(160),
			  	TRAN_ETC2          VARCHAR(160),
			  	TRAN_ETC3          VARCHAR(160),
			  	TRAN_ETC4          VARCHAR(160),
			  	TRAN_ETC5          VARCHAR(160),
			  	TRAN_LOG      	   CHAR(1) DEFAULT 'N'
			)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-group-table-idx1"><![CDATA[
			create index idx1_#TABLE_NAME# on #TABLE_NAME# (TRAN_GRP_KEY);
		]]></sql-hash>
		<sql-hash id="mms-create-migration-group-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (TRAN_DATE);
		]]></sql-hash>

		<sql-hash id="mms-create-source-tran-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
			  	TRAN_GRP_KEY     VARCHAR(16)            NOT NULL,
			  	TRAN_MSG_KEY     VARCHAR(16)            NOT NULL PRIMARY KEY,
			  	TRAN_PHONE       VARCHAR(15)            NOT NULL,
			  	TRAN_EXTEND      VARCHAR(86),
			  	TRAN_STATUS      CHAR(1)	DEFAULT '1' NOT NULL,
			  	TRAN_DATE        DATETIME YEAR TO SECOND,
			  	TRAN_SENDDATE    DATETIME YEAR TO SECOND, 
			  	TRAN_REPORTDATE  DATETIME YEAR TO SECOND,
			  	TRAN_RSLTDATE    DATETIME YEAR TO SECOND,
			  	TRAN_RSLT        VARCHAR(5),
			  	TRAN_END_TELCO   VARCHAR(5),
			  	TRAN_LOG         CHAR(1) DEFAULT 'N'
			)
		]]></sql-hash>
		<sql-hash id="mms-create-source-tran-table-idx1"><![CDATA[
			create index idx1_#TABLE_NAME# on #TABLE_NAME# (TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="mms-create-source-tran-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (TRAN_GRP_KEY)
		]]></sql-hash>

		<sql-hash id="mms-create-migration-tran-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
			  	TRAN_GRP_KEY     VARCHAR(16)            NOT NULL,
			  	TRAN_MSG_KEY     VARCHAR(16)            NOT NULL PRIMARY KEY,
			  	TRAN_PHONE       VARCHAR(15)            NOT NULL,
			  	TRAN_EXTEND      VARCHAR(86),
			  	TRAN_STATUS      CHAR(1)  DEFAULT '1' 	NOT NULL,
			  	TRAN_DATE        DATETIME YEAR TO SECOND,
			  	TRAN_SENDDATE    DATETIME YEAR TO SECOND,
			  	TRAN_REPORTDATE  DATETIME YEAR TO SECOND,
			  	TRAN_RSLTDATE    DATETIME YEAR TO SECOND,
			  	TRAN_RSLT        VARCHAR(5),
			  	TRAN_END_TELCO   VARCHAR(5),
			  	TRAN_LOG      	 CHAR(1) DEFAULT 'N'
			)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-tran-table-idx1"><![CDATA[
			create index  idx1_#TABLE_NAME# on #TABLE_NAME# ( TRAN_GRP_KEY)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-tran-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-tran-table-idx3"><![CDATA[
			create index idx3_#TABLE_NAME# on #TABLE_NAME# (TRAN_DATE);
		]]></sql-hash>
		<sql-hash id="mms-create-source-content-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				TRAN_GRP_KEY  VARCHAR(16)               	NOT NULL PRIMARY KEY,
				CONTENT_SEQ   INTEGER						NOT NULL ,
				CONTENT_TYPE  VARCHAR(3)                	NOT NULL,
				CONTENT_NAME  VARCHAR(255)              	NOT NULL,
				CONTENT_SVC   VARCHAR(3),
				TRAN_LOG      CHAR(1) DEFAULT 'N'
			)
		]]></sql-hash>
		<sql-hash id="mms-create-source-content-table-idx"><![CDATA[
			create index  idx_#TABLE_NAME# on #TABLE_NAME# (TRAN_GRP_KEY)
		]]></sql-hash>

		<sql-hash id="mms-create-migration-content-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				TRAN_GRP_KEY  VARCHAR(16)               NOT NULL,
				CONTENT_SEQ   INTEGER					NOT NULL,
				CONTENT_TYPE  VARCHAR(3)                NOT NULL,
				CONTENT_NAME  VARCHAR(255)	            NOT NULL,
				CONTENT_SVC   VARCHAR(3),
				TRAN_LOG      CHAR(1) DEFAULT 'N'
			)
		]]></sql-hash>


		<!-- MmsSourceTableScanTask -->
		<sql-hash id="mms-query-mark-out-of-ttl-group"><![CDATA[
			UPDATE	#GROUP_TABLE#
			SET		TRAN_STATUS='X'
			WHERE	TRAN_STATUS='1'
			AND		TRAN_DATE + ${mms_send_ttl} UNITS MINUTE < CURRENT
		]]></sql-hash>
		<sql-hash id="mms-query-update-out-of-ttl-tran"><![CDATA[
			UPDATE	#TRAN_TABLE#
			SET		TRAN_STATUS='3',
					TRAN_RSLT='OTTL'
			WHERE	TRAN_GRP_KEY IN ( SELECT TRAN_GRP_KEY FROM #GROUP_TABLE# WHERE TRAN_STATUS = 'X' )
		]]></sql-hash>
		<sql-hash id="mms-query-update-out-of-ttl-group"><![CDATA[
			UPDATE	#GROUP_TABLE#
			SET		TRAN_STATUS='3'
			WHERE	TRAN_STATUS='X'
		]]></sql-hash>

		<!-- MmsSourceTableScanTask -->
		<sql-hash id="mms-query-select-send-target"><![CDATA[
			SELECT	FIRST			${r"${LIMIT}"}
					TRAN_GRP_KEY	TRAN_GRP_KEY,
					TRAN_CALLBACK	TRAN_CALLBACK,
					TRAN_SUBJECT	TRAN_SUBJECT,
					TRAN_MSG		TRAN_MSG,
					TRAN_TYPE		TRAN_TYPE,
					TO_CHAR(TRAN_DATE + ${mms_send_ttl} UNITS MINUTE ,'%Y%m%d%H%M%S') TRAN_TTL,
					TO_CHAR( TRAN_DATE,'%Y%m%d%H%M%S')				TRAN_DATE
			FROM	#GROUP_TABLE#
			WHERE	TRAN_STATUS = '1'
			AND TRAN_DATE < CURRENT
			AND		TO_CHAR(TRAN_DATE, '%Y%m%d%H%M%S') > TO_CHAR(CURRENT-${mms_send_ttl} UNITS MINUTE, '%Y%m%d%H%M%S')
			<#if mms_select_target_addon_query_mms??>${mms_select_target_addon_query_mms}</#if>
		]]></sql-hash>
		<sql-hash id="mms-query-select-rcpt-target"><![CDATA[
			SELECT	TRAN_MSG_KEY	TRAN_MSG_KEY,
					TRAN_PHONE		TRAN_PHONE,
					TRAN_EXTEND		TRAN_EXTEND
			FROM	#TRAN_TABLE#
			WHERE	TRAN_GRP_KEY='${r"${TRAN_GRP_KEY}"}'
		]]></sql-hash>
		<sql-hash id="mms-query-select-contents-target"><![CDATA[
			SELECT	CONTENT_TYPE	CONTENT_TYPE,
<#if mms_content_dir??>'${mms_content_dir}' || CONTENT_NAME<#else>CONTENT_NAME</#if> AS
					CONTENT_NAME,
					CONTENT_SVC		CONTENT_SVC,
					CONTENT_SEQ		CONTENT_SEQ
			FROM	#CONTENT_TABLE#
			WHERE	TRAN_GRP_KEY='${r"${TRAN_GRP_KEY}"}'
			ORDER BY CONTENT_SEQ
		]]></sql-hash>
		<!-- 전송하지 못한 상태값이 6번인 경우 상태값을 1로 업데이트 한다. -->
		<sql-hash id="mms-query-update-pushed-nosend-target"><![CDATA[
			UPDATE	#GROUP_TABLE#
			SET		TRAN_STATUS='1'
			WHERE	TRAN_STATUS='6'
		]]></sql-hash>		
		<sql-hash id="mms-query-update-pushed-target"><![CDATA[
			UPDATE	#GROUP_TABLE#
			SET		TRAN_STATUS='6'
			WHERE	TRAN_GRP_KEY='${r"${TRAN_GRP_KEY}"}'
		]]></sql-hash>

		<sql-hash id="mms-query-tran-start-log"><![CDATA[
			UPDATE	#TRAN_TABLE#
			SET		TRAN_DATE = TO_DATE('${r"${TRAN_DATE}"}','%Y%m%d%H%M%S')
			WHERE	TRAN_GRP_KEY	='${r"${TRAN_GRP_KEY}"}'
		]]></sql-hash>

		<sql-hash id="mms-query-group-log"><![CDATA[
			UPDATE	#GROUP_TABLE#
			SET		TRAN_STATUS		='${r"${TRAN_STATUS}"}',
					TRAN_SENDDATE	= TO_DATE('${r"${TRAN_SENDDATE}"}','%Y%m%d%H%M%S')
			WHERE	TRAN_GRP_KEY	='${r"${TRAN_GRP_KEY}"}'
		]]></sql-hash>
		<sql-hash id="mms-query-update-log"><![CDATA[
			UPDATE	#TRAN_TABLE#
			SET		TRAN_STATUS		='${r"${TRAN_STATUS}"}',
					TRAN_RSLT		='${r"${TRAN_RSLT}"}',
					TRAN_SENDDATE	= TO_DATE('${r"${TRAN_SENDDATE}"}','%Y%m%d%H%M%S')
			WHERE	TRAN_MSG_KEY	='${r"${TRAN_MSG_KEY}"}'
			AND		TRAN_STATUS != '3'
		]]></sql-hash>
		<sql-hash id="mms-query-update-result"><![CDATA[
			UPDATE	#TRAN_TABLE#
			SET		TRAN_STATUS		='${r"${TRAN_STATUS}"}',
					TRAN_RSLT		='${r"${TRAN_RSLT}"}',
					TRAN_END_TELCO	='${r"${TRAN_END_TELCO}"}',
					TRAN_RSLTDATE	=TO_DATE('${r"${TRAN_RSLTDATE}"}','%Y%m%d%H%M%S'),
					TRAN_REPORTDATE	=TO_DATE('${r"${TRAN_REPORTDATE}"}','%Y%m%d%H%M%S')
			WHERE	TRAN_MSG_KEY	='${r"${TRAN_MSG_KEY}"}'
		]]></sql-hash>
		<!--
		<sql-hash id="mms-query-update-content"><![CDATA[
			UPDATE	#CONTENT_TABLE#
			SET		TRAN_LOG = 'T'
			WHERE	TRAN_GRP_KEY='${r"${TRAN_GRP_KEY}"}'
		]]></sql-hash>
		-->
		<!-- MmsLogMigrationTask : Tran -->
		<sql-hash id="mms-update-tran-migration-target"><![CDATA[
			UPDATE	#TRAN_SOURCE_TABLE#
			SET		TRAN_LOG='Y'
			WHERE	( TRAN_STATUS in ('3','5') )
			OR		( TRAN_STATUS <> '1' AND (TRAN_SENDDATE + ${mms_migrate_ttl} UNITS HOUR ) < CURRENT )
		]]></sql-hash>

		<sql-hash id="mms-copy-tran-migration-target-before"><![CDATA[
			INSERT INTO #TRAN_BEFORE_TARGET_TABLE#
			SELECT	*
			FROM	#TRAN_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE < TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>
		<sql-hash id="mms-copy-tran-migration-target-current"><![CDATA[
			INSERT INTO #TRAN_CURRENT_TARGET_TABLE#
			SELECT	*
			FROM	#TRAN_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
			AND		TRAN_DATE < TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>
		<sql-hash id="mms-copy-tran-migration-target-after"><![CDATA[
			INSERT INTO #TRAN_AFTER_TARGET_TABLE#
			SELECT	*
			FROM	#TRAN_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>

		<sql-hash id="mms-delete-tran-migration-target"><![CDATA[
			DELETE FROM #TRAN_SOURCE_TABLE# WHERE TRAN_LOG='Y'
		]]></sql-hash>

		<!-- MmsLogMigrationTask : Group -->
		<sql-hash id="mms-update-group-migration-target"><![CDATA[
			UPDATE	#GROUP_SOURCE_TABLE#
			SET		TRAN_LOG='Y'
			WHERE	( TRAN_STATUS in ('2','3','5') )
			OR		( TRAN_STATUS <> '1' AND ( TRAN_DATE + ${mms_migrate_ttl} UNITS HOUR ) < CURRENT )
		]]></sql-hash>
		<sql-hash id="mms-update-content-migration-target"><![CDATA[
			UPDATE #CONTENT_SOURCE_TABLE#
			SET TRAN_LOG='Y'
			WHERE TRAN_GRP_KEY IN ( SELECT TRAN_GRP_KEY FROM #GROUP_SOURCE_TABLE#
				WHERE TRAN_LOG='Y' )
		]]></sql-hash>

		<sql-hash id="mms-copy-group-migration-target-before"><![CDATA[
			INSERT INTO #GROUP_BEFORE_TARGET_TABLE#
			SELECT	*
			FROM	#GROUP_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE < TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>
		<sql-hash id="mms-copy-group-migration-target-current"><![CDATA[
			INSERT INTO #GROUP_CURRENT_TARGET_TABLE#
			SELECT	*
			FROM	#GROUP_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
			AND		TRAN_DATE < TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>
		<sql-hash id="mms-copy-group-migration-target-after"><![CDATA[
			INSERT INTO #GROUP_AFTER_TARGET_TABLE#
			SELECT	*
			FROM	#GROUP_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
		]]></sql-hash>

		<sql-hash id="mms-copy-content-migration-target-before"><![CDATA[
			INSERT INTO #CONTENT_BEFORE_TARGET_TABLE#
			( TRAN_GRP_KEY, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_GRP_KEY, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#GROUP_SOURCE_TABLE# A,
					#CONTENT_SOURCE_TABLE# B
			WHERE	A.TRAN_DATE < TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
			AND		A.TRAN_LOG='Y'
			AND		A.TRAN_GRP_KEY = B.TRAN_GRP_KEY
			AND		B.TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-copy-content-migration-target-current"><![CDATA[
			INSERT INTO #CONTENT_CURRENT_TARGET_TABLE#
			( TRAN_GRP_KEY, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_GRP_KEY, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#GROUP_SOURCE_TABLE# A,
					#CONTENT_SOURCE_TABLE# B
			WHERE	A.TRAN_LOG='Y'
			AND		A.TRAN_DATE >= TO_DATE( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
			AND		A.TRAN_DATE < TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
			AND		A.TRAN_GRP_KEY = B.TRAN_GRP_KEY
			AND		B.TRAN_LOG='Y'
		]]></sql-hash>

		<sql-hash id="mms-copy-content-migration-target-after"><![CDATA[
			INSERT INTO #CONTENT_AFTER_TARGET_TABLE#
			( TRAN_GRP_KEY, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_GRP_KEY, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#GROUP_SOURCE_TABLE# A,
					#CONTENT_SOURCE_TABLE# B
			WHERE	A.TRAN_LOG='Y'
			AND		A.TRAN_DATE >= TO_DATE( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%M:%S' )
			AND		A.TRAN_GRP_KEY = B.TRAN_GRP_KEY
			AND		B.TRAN_LOG='Y'
		]]></sql-hash>

		<sql-hash id="mms-delete-group-migration-target"><![CDATA[
			DELETE FROM #GROUP_SOURCE_TABLE# WHERE TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-delete-content-migration-target"><![CDATA[
			DELETE FROM #CONTENT_SOURCE_TABLE# WHERE TRAN_LOG='Y'
		]]></sql-hash>
	</sql>
</module>
</CONF>

<!--
insert into kb_tran
(
TRAN_REFKEY,
TRAN_ID,
TRAN_PHONE,
TRAN_CALLBACK,
TRAN_MSG,
TRAN_DATE,
TRAN_TYPE,
TRAN_STATUS
)
values
('dragon','1234','01199716837','1588-3000','test msg',
now(),0,'1')
-->
