package com.kskyb.magent.config;

import java.util.HashMap;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;


/**
 * 테이블생성 정보를 관리하는 역할을 담당한다.
 * 
 */
public class ResourceTableManager {
	/**
	 * 
	 */
	private static final ResourceTableManager instance = new ResourceTableManager();

	/**
	 * get instance<br>
	 * 인스턴스 반환
	 * 
	 * @return singleton instance
	 */
	public static final ResourceTableManager getInstance() {
		return instance;
	}

	/**
	 * @param manager
	 */
	private static final void regist(ResourceTableManager manager) {
		synchronized (instance) {
			instance.resource.putAll(manager.resource);
		}
	}

	/**
	 * 
	 */
	public ResourceTableManager() {
		//
	}

	/**
	 * get resource name
	 * 
	 * @param key
	 *            resource index
	 * @return resource name
	 */
	public final String getResourceName(String key) {
		return this.getResourceName(key,
									System.currentTimeMillis(),
									false);
	}

	/**
	 * @param key
	 * @param force
	 * @return
	 */
	public final String getResourceName(String key,
										boolean force) {
		return this.getResourceName(key,
									System.currentTimeMillis(),
									force);
	}

	/**
	 * @param key
	 * @param time
	 * @return
	 */
	public final String getResourceName(String key,
										long time) {
		return this.getResourceName(key,
									time,
									false);
	}

	/**
	 * get resource name
	 * 
	 * @param key
	 *            resource index
	 * @param force
	 *            true : force check <br>
	 *            false : force check skip
	 * @return resource name
	 */
	public final String getResourceName(String key,
										long time,
										boolean force) {
		ResourceTableInfo info = this.resource.get(key);

		if (info == null) {
			throw new RuntimeException("regegist.resource.id:" + key);
		}

		return info.getResourceName(time,
									force);
	}

	/**
	 * 
	 */
	@ValidationMethod
	@SuppressWarnings("unused")
	private final void validateResourceTableManager() {
		regist(this);
	}

	/**
	 * info hash<br>
	 * 리소스 정보 저장 헤쉬
	 */
	@MemberField(clazz = ResourceTableInfo.class, elementName = "resource", nullable = false)
	private final HashMap<String, ResourceTableInfo> resource = new HashMap<String, ResourceTableInfo>();
}
