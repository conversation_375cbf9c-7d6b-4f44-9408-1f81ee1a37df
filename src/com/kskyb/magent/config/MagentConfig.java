package com.kskyb.magent.config;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.apache.log4j.Logger;


import com.kskyb.broker.common.digest.xml.XMLSerializer;
import com.kskyb.broker.common.digest.xml.resource.XMLInputSourceContext;
import com.kskyb.broker.template.freemarker.FreemarkerTemplateProcessor;
import com.kskyb.magent.MessageResource;

/**
 * 내/외 부 설정 파일을 관리한다.
 */
public class MagentConfig {
	/**
	 * 
	 */
	private static final MessageResource resource = MessageResource.getResource(MagentConfig.class);

	/**
	 * 
	 */
	private static final Logger logger = Logger.getLogger("common");

	/**
	 * internal properties ( load from config.xml )<br>
	 * 내부 속성값
	 */
	static final Properties appProp = new Properties();

	/**
	 * external properties ( load from config.ini )<br>
	 * 외부 속성값
	 */
	static final Properties localProp = new Properties();

	/**
	 * handle charset<br>
	 * 내부 문자 처리 케릭터셋
	 */
	public static String charset = null;

	/**
	 * convert string to byte array using handle charset<br>
	 * 내부 문자를 byte array로 전환
	 * 
	 * @param src
	 *            convert source
	 * @return result byte array
	 */
	public static final byte[] getBytes(String src) {
		try {
			return src.getBytes(charset);
		}
		catch (UnsupportedEncodingException e) {
			return src.getBytes();
		}
	}

	/**
	 * initialize config with local properties ( load from config.ini )<br>
	 * 외부 속성 초기화
	 * 
	 * @param prop
	 *            properties load from config.ini
	 */
	public static final void init(	Properties prop,
									Properties org) {
		logger.info(resource.getBundle(	"loaded.config",
										org.toString()));

		localProp.putAll(prop);

		charset = prop.getProperty("io_charset");

		InputStream in = null;

		try {
			in = MagentConfig.class.getResourceAsStream("config.xml");

			if (in == null) {
				throw new RuntimeException("config not include library");
			}

			appProp.loadFromXML(in);
		}
		catch (Throwable thw) {
			(new Error(	"local.config.xml.load.fail",
						thw)).printStackTrace();
			System.exit(1);
		}
		finally {
			if (in != null) {
				try {
					in.close();
				}
				catch (Throwable thw) {

				}
			}
		}

		final String xmlCharset = MagentConfig.getApplicationProperty(	"xml_loading_charset",
																		"KSC5601");
		String dbType = localProp.getProperty("db_type");

		if (dbType == null) {
			(new Error("config.ini.missing.db.type")).printStackTrace();
			System.exit(1);
		}

		String resourceName = "module_" + dbType + ".xml";
		
		HashMap map = null;
		try {
			map = new HashMap();

			map.putAll(localProp);
			map.putAll(appProp);

			in = MagentConfig.class.getResourceAsStream(resourceName);

			if (in == null) {
				(new Error("config.db.resource.missing")).printStackTrace();
				System.exit(1);
			}
		
			// final String xmlBody = FreeMarkerUtil.convert( "moduleConfig",
			// in,
			// xmlCharset,
			// map);
			final String xmlBody = FreemarkerTemplateProcessor.getProcessor().make(	in,
																					xmlCharset).convert(map);
		
			// BootLoader.activateXmlContentBody( xmlBody,
			// true);
			XMLSerializer.getSerializer().iterate(	XMLInputSourceContext.create(	xmlBody,
																					true),
													Object.class);

		}
		catch (Throwable thw) {
			logger.info("config init Error:"+thw);
			(new Error(	"config.db.resource.fail",
						thw)).printStackTrace();
			System.exit(1);
		}
		logger.info(resource.getBundle("loading.module.complete"));
	}

	/**
	 * get property<br>
	 * first lookup internal property and second external property<br>
	 * 속성 반환 : 내부 속성에서 먼저 찾고 없을 경우 외부 속성에서 찾는다.
	 * 
	 * @param key
	 *            lookup key
	 * @param defaultValue
	 *            default value
	 * @return property
	 */
	public static final String getProperty(	String key,
											String defaultValue) {
		return appProp.getProperty(	key,
									localProp.getProperty(	key,
															defaultValue));
	}

	/**
	 * get property<br>
	 * first lookup internal property and second external property<br>
	 * 속성 반환 : 내부 속성에서 먼저 찾고 없을 경우 외부 속성에서 찾는다.
	 * 
	 * @param key
	 *            lookup key
	 * @return property
	 */
	public static final String getProperty(String key) {
		return appProp.getProperty(	key,
									localProp.getProperty(key));
	}

	/**
	 * get internal property<br>
	 * 내부 속성값 반환
	 * 
	 * @return internal property
	 */
	public static final Properties getApplicationProperties() {
		return appProp;
	}

	/**
	 * get internal property<br>
	 * 내부 속성값 반환
	 * 
	 * @param key
	 *            lookup key
	 * @param defaultValue
	 *            default value
	 * @return property
	 */
	public static final String getApplicationProperty(	String key,
														String defaultValue) {
		return appProp.getProperty(	key,
									defaultValue);
	}

	/**
	 * get internal property<br>
	 * 내부 속성값 반환
	 * 
	 * @param key
	 *            lookup key
	 * @return property
	 */
	public static final String getApplicationProperty(String key) {
		return appProp.getProperty(key);
	}

	/**
	 * get external property<br>
	 * 외부 속성 반환
	 * 
	 * @return propety
	 */
	public static final Properties getLocalProperties() {
		return appProp;
	}

	/**
	 * get internal property<br>
	 * 내부 속성 반환
	 * 
	 * @param key
	 *            lookup key
	 * @param defaultValue
	 *            default value
	 * @return property
	 */
	public static final String getLocalProperty(String key,
												String defaultValue) {
		return localProp.getProperty(	key,
										defaultValue);
	}

	/**
	 * get internal property<br>
	 * 내부 속성반환
	 * 
	 * @param key
	 *            lookup key
	 * @return property
	 */
	public static final String getLocalProperty(String key) {
		return localProp.getProperty(key);
	}

	/**
	 * put all property <br>
	 * first local property, second internal property<br>
	 * 모든 속성을 전환함 외부 속성을 먼저 전송하고 다음에 내부설정을 전송한다.<br>
	 * 즉, 내부 설정이 우선하여 반영됨.
	 * 
	 * @param map
	 *            merged property map
	 */
	public static final void passToMap(Map map) {
		map.putAll(localProp);
		map.putAll(appProp);
	}
}
