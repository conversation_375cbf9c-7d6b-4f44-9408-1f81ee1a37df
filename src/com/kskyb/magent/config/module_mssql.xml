<?xml version="1.0" encoding="EUC-KR"?>
<#if sms_migrate_ttl??>
<#else>
	<#assign sms_migrate_ttl="36"/>
</#if>
<#if mms_migrate_ttl??>
<#else>
	<#assign mms_migrate_ttl="36"/>
</#if>
<CONF>

<!--
<module id="${db_type}" clazz="com.kskyb.broker.service.pool.jdbc.CommonPooledConnectionPool"
		broadcast="connection.local.pool"
		max="20" min="1" factor="0.5" emptyWait="-1" defaultEntry="true">
	<factory clazz="com.kskyb.broker.service.pool.jdbc.CommonPooledConnectionFactory">
		<connection-config clazz="com.kskyb.broker.service.pool.jdbc.PoolConfig" authCommit="true">
			<driver>${db_connect_driver}</driver>
			<url>${db_connect_url}</url>
			<#if db_connect_id??><user>${db_connect_id}</user></#if>
			<#if db_connect_pass??><password><![CDATA[${db_connect_pass}]]></password></#if>
			<#if db_base_charset??><base-charset>${db_base_charset}</base-charset></#if>
			<#if db_in_charset??><incoming-charset>${db_in_charset}</incoming-charset></#if>
			<#if db_out_charset??><outgoing-charset>${db_out_charset}</outgoing-charset></#if>
			<wait-on-handshake-fail>${wait_on_handshake_fail}</wait-on-handshake-fail>
			<wait-on-handshake-fail-interval>${wait_on_handshake_fail_interval}000</wait-on-handshake-fail-interval>
			<validation-query>select TOP 1 name FROM sysobjects</validation-query>
		</connection-config>
	</factory>
</module>
-->
<!-- <#if db_base_charset??><#else><#assign db_base_charset="KSC5601"></#if> -->
<!-- <#if db_in_charset??><#else><#assign db_in_charset="KSC5601"></#if> -->
<!-- <#if db_out_charset??><#else><#assign db_out_charset="KSC5601"></#if> -->
<module
	clazz="com.kskyb.broker.service.pool.db.DataSourcePoolService"
	serviceName="${db_type}"
	startOnRegist="true"
	defaultEntry="true"
	max="20"
	min="1"
	limit="100"
	idleLimit="60000"
	emptyWait="1000">
	<desc>${db_type}</desc>
	<note>${db_type}</note>
	<thread-usage-expire-interval>3600000</thread-usage-expire-interval>
	<thread-usage-monitor-interval>360000</thread-usage-monitor-interval>
	<data-source
		clazz="com.kskyb.broker.util.jdbc.datasource.DataSourceDelegate"
		initial="${db_name}"
		autoCommit="true">
		<driver>${db_connect_driver}</driver>
		<url>${db_connect_url}</url>
		<#if db_connect_id??><user>${db_connect_id}</user></#if>
		<#if db_connect_pass??><password><![CDATA[${db_connect_pass}]]></password></#if>
		<charsets>${db_base_charset},${db_in_charset},${db_out_charset}</charsets>
		<activate-query>select TOP 1 name FROM sysobjects</activate-query>
		<#if extractor??>
		<extractor>${extractor}</extractor>
		</#if>
	</data-source>
	<handler
		clazz="com.kskyb.broker.service.pool.PoolBoundaryHandler">
	</handler>
</module>

<module broadcast="sql.exception.codes" clazz="com.kskyb.broker.adapter.jdbc.SQLExceptionFilter">
	<code>
		<code-hash id="0">connect</code-hash>
		<code-hash id="1146">object</code-hash>
		<code-hash id="1064">syntax</code-hash>
	</code>
</module>

<#assign sms_send_ttl><#if sms_send_ttl??>${sms_send_ttl}<#else>0</#if></#assign>
<#assign mms_send_ttl><#if mms_send_ttl??>${mms_send_ttl}<#else>0</#if></#assign>

<module broadcast="sql.set.sms" clazz="com.kskyb.broker.util.jdbc.SQLSet">
	<sql>
		<sql-hash id="table-exists-scan"><![CDATA[
			select name from sysobjects where xtype = 'u' and name='#TABLE_NAME#'
		]]></sql-hash>
		<sql-hash id="sms-create-source-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				 TRAN_PR            int  IDENTITY (1, 1) NOT NULL ,
				 TRAN_REFKEY        VARCHAR(20) ,
				 TRAN_ID            VARCHAR(20) ,
				 TRAN_PHONE         VARCHAR(15) NOT NULL ,
				 TRAN_CALLBACK      VARCHAR(15) NOT NULL,
				 TRAN_MSG           VARCHAR(150) NOT NULL,
				 TRAN_DATE          DATETIME NOT NULL,
				 TRAN_TYPE          int  DEFAULT 0 NOT NULL ,
				 TRAN_STATUS        CHAR(1) DEFAULT '1' NOT NULL,
				 TRAN_SENDDATE      DATETIME ,
				 TRAN_REPORTDATE    DATETIME ,
				 TRAN_RSLTDATE      DATETIME ,
				 TRAN_RSLT          CHAR(2) ,
				 TRAN_ETC1          VARCHAR(160) ,
				 TRAN_ETC2          VARCHAR(160) ,
				 TRAN_ETC3          VARCHAR(160) ,
				 TRAN_ETC4          VARCHAR(160) ,				 
				 TRAN_END_TELCO     VARCHAR(8),
				 TRAN_LOG			CHAR(1) DEFAULT 'N',
				 TRAN_GRPSEQ		INT,
				 CONSTRAINT [pk_#TABLE_NAME#] PRIMARY KEY  CLUSTERED ([TRAN_PR])
				 )
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx1"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX1 ON #TABLE_NAME#(TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx2"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX2 ON #TABLE_NAME#(TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx3"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX3 ON #TABLE_NAME#(TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx4"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX4 ON #TABLE_NAME#(TRAN_REFKEY)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx5"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX5 ON #TABLE_NAME#(TRAN_GRPSEQ)
		]]></sql-hash>		
		<sql-hash id="sms-create-migration-table"><![CDATA[
			 CREATE TABLE #TABLE_NAME# (
				 TRAN_PR            int,
				 TRAN_REFKEY        VARCHAR(20),
				 TRAN_ID            VARCHAR(20),
				 TRAN_PHONE         VARCHAR(15) NOT NULL,
				 TRAN_CALLBACK      VARCHAR(15) NOT NULL,
				 TRAN_MSG           VARCHAR(150) NOT NULL,
				 TRAN_DATE          DATETIME NOT NULL,
				 TRAN_TYPE          int  DEFAULT 0 NOT NULL,
				 TRAN_STATUS        CHAR(1) DEFAULT '1' NOT NULL,
				 TRAN_SENDDATE      DATETIME,
				 TRAN_REPORTDATE    DATETIME,
				 TRAN_RSLTDATE      DATETIME,
				 TRAN_RSLT          CHAR(2),
				 TRAN_ETC1          VARCHAR(160),
				 TRAN_ETC2          VARCHAR(160),
				 TRAN_ETC3          VARCHAR(160),
				 TRAN_ETC4          VARCHAR(160),				 
				 TRAN_END_TELCO     VARCHAR(8),
				 TRAN_LOG			CHAR(1) DEFAULT 'N',
				 TRAN_GRPSEQ		INT
			)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx1"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX1 ON #TABLE_NAME#(TRAN_PR)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx2"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX2 ON #TABLE_NAME#(TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx3"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX3 ON #TABLE_NAME#(TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx4"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX4 ON #TABLE_NAME#(TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx5"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX5 ON #TABLE_NAME#(TRAN_REFKEY)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx6"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX6 ON #TABLE_NAME#(TRAN_GRPSEQ)
		]]></sql-hash>
		<!-- 대상을 추출한다. SmsSourceTableScanTask -->
		<sql-hash id="sms-query-update-out-of-ttl-target"><![CDATA[
			UPDATE	#TABLE_NAME# WITH (TABLOCK)
			SET		TRAN_STATUS='5',
					TRAN_RSLT='OT',
					TRAN_SENDDATE = GETDATE(),
					TRAN_RSLTDATE = GETDATE(),
					TRAN_REPORTDATE = GETDATE()
			WHERE	TRAN_STATUS='1'
			AND		DATEADD(MINUTE, ${sms_send_ttl} , TRAN_DATE ) < GETDATE()
		]]></sql-hash>
		<sql-hash id="sms-query-select-checked-target"><![CDATA[
			SELECT	TOP ${r"${LIMIT}"} TRAN_PR			TRAN_PR,
					TRAN_ID			TRAN_ID,
					TRAN_PHONE  	TRAN_PHONE,
					TRAN_CALLBACK	TRAN_CALLBACK,
					TRAN_MSG		TRAN_MSG,
<#if sms_select_additional_info??>${sms_select_additional_info}<#else>' '</#if>
									TRAN_REVERSE,
					TRAN_ID			TRAN_REVERSE,				
					TRAN_TYPE		TRAN_TYPE,
					CONVERT(varchar(14),  DATEADD(MINUTE,  ${sms_send_ttl} , TRAN_DATE), 112)
					+ REPLACE( CONVERT(varchar(14), DATEADD(MINUTE,  ${sms_send_ttl} , TRAN_DATE), 108),':','')
					TRAN_TTL
			FROM	#TABLE_NAME# WITH(NOLOCK)
			WHERE	TRAN_STATUS='1'
			AND		TRAN_DATE < GETDATE()
			AND		TRAN_DATE > CONVERT(DATETIME,GETDATE())-${sms_send_ttl}/1440
			<#if sms_select_target_addon_query??>${sms_select_target_addon_query}</#if>
		]]></sql-hash>
		<!-- 전송하지 못한 상태값이 4,6번인 경우 상태값을 1로 업데이트 한다. -->
		<sql-hash id="sms-query-update-checked-nosend-target"><![CDATA[
			UPDATE	#TABLE_NAME# WITH (TABLOCK)
			SET		TRAN_STATUS = '1'
			WHERE	TRAN_STATUS IN ('4','6')
		]]></sql-hash>
		<!-- 버퍼로 넣고나서 업데이트 한다. -->
		<sql-hash id="sms-query-update-checked-target"><![CDATA[
			UPDATE	#TABLE_NAME# WITH (TABLOCK)
			SET		TRAN_STATUS='6',
					TRAN_SENDDATE= GETDATE()
			WHERE	TRAN_PR = '${r"${TRAN_PR}"}'
		]]></sql-hash>
		
		<sql-hash id="sms-query-select-validationQuery"><![CDATA[
				SELECT 1
		]]></sql-hash>


		<!-- -->
		<sql-hash id="sms-query-update-log"><![CDATA[
			UPDATE	#TABLE_NAME# WITH (TABLOCK)
			SET		TRAN_STATUS='${r"${TRAN_STATUS}"}',
					TRAN_RSLT='${r"${TRAN_RSLT}"}'
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
			AND		TRAN_STATUS != '3'
		]]></sql-hash>
		<sql-hash id="sms-query-update-result"><![CDATA[
			UPDATE	#TABLE_NAME# WITH (TABLOCK)
			SET		TRAN_STATUS		='3',
					TRAN_RSLT		='${r"${TRAN_RSLT}"}',
					TRAN_END_TELCO	='${r"${TRAN_END_TELCO}"}',
					TRAN_RSLTDATE	=CONVERT(datetime,
										SUBSTRING('${r"${TRAN_RSLTDATE}"}',1,4) + '-'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',5,2) + '-'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',7,2) + ' '
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',9,2) + ':'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',11,2) + ':'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',13,2) , 120),
					TRAN_REPORTDATE	=CONVERT(datetime,
										SUBSTRING('${r"${TRAN_REPORTDATE}"}',1,4) + '-'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',5,2) + '-'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',7,2) + ' '
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',9,2) + ':'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',11,2) + ':'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',13,2) , 120)
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
		]]></sql-hash>
		 <!-- table migration : SmsLogMigrationTask -->
		<sql-hash id="sms-query-update-migration-log"><![CDATA[
			UPDATE	#SOURCE_TABLE_NAME# WITH (TABLOCK)
			SET		TRAN_LOG		='Y'
			WHERE	( TRAN_STATUS in ('3','5', '4', '9', 'X') )
			OR		( TRAN_STATUS = '2' AND TRAN_SENDDATE < DATEADD (HOUR, - ${sms_migrate_ttl}, GetDate()) )
		]]></sql-hash>

		<sql-hash id="sms-query-copy-migration-log-before"><![CDATA[
			INSERT INTO #BEFORE_TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME# WITH(NOLOCK)
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE < CONVERT(datetime,  '#TIME_STAMP#-01 00:00:00' , 120)
		]]></sql-hash>
		<sql-hash id="sms-query-copy-migration-log-current"><![CDATA[
			INSERT INTO #TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME# WITH(NOLOCK)
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= CONVERT(datetime,  '#TIME_STAMP#-01 00:00:00' , 120)
			AND		TRAN_DATE < CONVERT(datetime,  '#AFTER_TIME_STAMP#-01 00:00:00' , 120)
		]]></sql-hash>
		<sql-hash id="sms-query-copy-migration-log-after"><![CDATA[
			INSERT INTO #AFTER_TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME# WITH(NOLOCK)
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= CONVERT(datetime,  '#AFTER_TIME_STAMP#-01 00:00:00' , 120)
		]]></sql-hash>

		<sql-hash id="sms-query-delete-migration-log"><![CDATA[
			DELETE FROM #SOURCE_TABLE_NAME# WHERE TRAN_LOG='Y'
		]]></sql-hash>
	</sql>
</module>
<module broadcast="sql.set.mms" clazz="com.kskyb.broker.util.jdbc.SQLSet">
	<sql>
		<sql-hash id="table-exists-scan"><![CDATA[
			select name from sysobjects where xtype = 'u' and name='#TABLE_NAME#'
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (			 
			  TRAN_PR				INT  IDENTITY (1, 1)  NOT NULL,
			  TRAN_REFKEY			VARCHAR(20),
			  TRAN_ID				VARCHAR(20),		  
			  TRAN_CALLBACK			VARCHAR(15),
			  TRAN_PHONE			VARCHAR(15)          NOT NULL,
			  TRAN_SUBJECT			VARCHAR(100),
			  TRAN_MSG				VARCHAR(2000)        NOT NULL,
			  TRAN_DATE				DATETIME             NOT NULL,
			  TRAN_TYPE				INT                  NOT NULL,
			  TRAN_STATUS			CHAR(1) DEFAULT '1'  NOT NULL,
			  TRAN_SENDDATE			DATETIME,
			  TRAN_REPORTDATE		DATETIME,
			  TRAN_RSLTDATE			DATETIME,
			  TRAN_RSLT				VARCHAR(5),
			  TRAN_ETC1            	VARCHAR(160),
			  TRAN_ETC2           	VARCHAR(160),
			  TRAN_ETC3           	VARCHAR(160),
			  TRAN_ETC4            	VARCHAR(160),			  
			  TRAN_END_TELCO       	VARCHAR(8),
			  TRAN_LOG            	CHAR(1) DEFAULT 'N',
			  TRAN_GRPSEQ			INT,
			  CONSTRAINT [pk_#TABLE_NAME#] PRIMARY KEY  CLUSTERED (TRAN_PR)
			)
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table-idx1"><![CDATA[
			create index idx1_#TABLE_NAME# on #TABLE_NAME# (TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table-idx3"><![CDATA[
			create index idx3_#TABLE_NAME# on #TABLE_NAME# (TRAN_STATUS)
		]]></sql-hash>		
		<sql-hash id="mms-create-source-msg-table-idx4"><![CDATA[
			create index idx4_#TABLE_NAME# on #TABLE_NAME# (TRAN_REFKEY)
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table-idx5"><![CDATA[
			create index idx5_#TABLE_NAME# on #TABLE_NAME# (TRAN_GRPSEQ)
		]]></sql-hash>

		<sql-hash id="mms-create-migration-msg-table"><![CDATA[
			CREATE TABLE #TABLE_NAME#
			(
			  TRAN_PR				INT,
			  TRAN_REFKEY			VARCHAR(20),
			  TRAN_ID				VARCHAR(20),			  
			  TRAN_CALLBACK			VARCHAR(15),
			  TRAN_PHONE			VARCHAR(15)          NOT NULL,
			  TRAN_SUBJECT			VARCHAR(100),
			  TRAN_MSG				VARCHAR(2000)        NOT NULL,
			  TRAN_DATE				DATETIME             NOT NULL,
			  TRAN_TYPE				INT                  NOT NULL,
			  TRAN_STATUS			CHAR(1) DEFAULT '1'  NOT NULL,
			  TRAN_SENDDATE			DATETIME,
			  TRAN_REPORTDATE		DATETIME,
			  TRAN_RSLTDATE			DATETIME,
			  TRAN_RSLT				VARCHAR(5),
			  TRAN_ETC1            	VARCHAR(160),
			  TRAN_ETC2           	VARCHAR(160),
			  TRAN_ETC3           	VARCHAR(160),
			  TRAN_ETC4            	VARCHAR(160),			  
			  TRAN_END_TELCO       	VARCHAR(8),
			  TRAN_LOG            	CHAR(1) DEFAULT 'N',
			  TRAN_GRPSEQ			INT			  
			)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx1"><![CDATA[
			create index idx1_#TABLE_NAME# on #TABLE_NAME# (TRAN_PR)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx3"><![CDATA[
			create index idx3_#TABLE_NAME# on #TABLE_NAME# (TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx4"><![CDATA[
			create index idx4_#TABLE_NAME# on #TABLE_NAME# (TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx5"><![CDATA[
			create index idx5_#TABLE_NAME# on #TABLE_NAME# (TRAN_REFKEY)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx6"><![CDATA[
			create index idx6_#TABLE_NAME# on #TABLE_NAME# (TRAN_GRPSEQ)
		]]></sql-hash>
		<sql-hash id="mms-create-source-content-table"><![CDATA[
			CREATE TABLE #TABLE_NAME#
			(
				TRAN_PR       VARCHAR(16)   NOT NULL,
				CONTENT_SEQ   INT						NOT NULL,
				CONTENT_TYPE  VARCHAR(3)    NOT NULL,
				CONTENT_NAME  VARCHAR(255)  NOT NULL,
				CONTENT_SVC   VARCHAR(3),
				TRAN_LOG      CHAR(1)       DEFAULT 'N',
				CONSTRAINT [pk_#TABLE_NAME#] PRIMARY KEY  CLUSTERED (TRAN_PR, CONTENT_SEQ)
			)
		]]></sql-hash>
		<sql-hash id="mms-create-source-content-table-idx"><![CDATA[
			create index  idx_#TABLE_NAME# on #TABLE_NAME# (TRAN_PR)
		]]></sql-hash>

		<sql-hash id="mms-create-migration-content-table"><![CDATA[
			CREATE TABLE #TABLE_NAME#
			(
				TRAN_PR       VARCHAR(16)   NOT NULL,
				CONTENT_SEQ   INT			NOT NULL,
				CONTENT_TYPE  VARCHAR(3)    NOT NULL,
				CONTENT_NAME  VARCHAR(255)  NOT NULL,
				CONTENT_SVC   VARCHAR(3),
				TRAN_LOG      CHAR(1)       DEFAULT 'N'
			)
		]]></sql-hash>

		<!-- MmsSourceTableScanTask -->
		<sql-hash id="mms-query-update-out-of-ttl-msg"><![CDATA[
			UPDATE	#MSG_TABLE# WITH (TABLOCK)
			SET		TRAN_STATUS='3', TRAN_RSLT='OTTL'
			WHERE	DATEADD(MINUTE,  ${mms_send_ttl}, TRAN_DATE) < GETDATE()
		]]></sql-hash>

		<!-- MmsSourceTableScanTask -->
		<sql-hash id="mms-query-select-send-target"><![CDATA[
			SELECT	TOP ${r"${LIMIT}"} 	TRAN_PR	TRAN_PR,
					TRAN_ID				TRAN_ID,
					TRAN_CALLBACK		TRAN_CALLBACK,
					TRAN_PHONE     		TRAN_PHONE,
					TRAN_SUBJECT	  	TRAN_SUBJECT,
					TRAN_MSG			TRAN_MSG,
					TRAN_TYPE		  	TRAN_TYPE,					
					CONVERT(varchar(8),  DATEADD(MINUTE,  ${mms_send_ttl}, TRAN_DATE), 112)
					+ REPLACE( CONVERT(varchar(10), DATEADD(MINUTE,  ${mms_send_ttl}, TRAN_DATE), 108),':','') TTL,
					CONVERT(varchar(8), TRAN_DATE , 112) + REPLACE( CONVERT(varchar(10), TRAN_DATE , 108),':','') TRAN_DATE
			FROM	#MSG_TABLE# WITH(NOLOCK)
			WHERE	TRAN_STATUS = '1'
			AND		TRAN_TYPE = 4
			AND		TRAN_DATE < GETDATE()
			AND		TRAN_DATE > CONVERT(DATETIME,GETDATE())-${mms_send_ttl}/1440
			<#if mms_select_target_addon_query_mms??>${mms_select_target_addon_query_mms}</#if>
		]]></sql-hash>

		<sql-hash id="mms-query-select-contents-target"><![CDATA[
			SELECT	CONTENT_TYPE	CONTENT_TYPE,
      <#if mms_content_dir??>'${mms_content_dir}' + CONTENT_NAME<#else>CONTENT_NAME</#if> AS
					CONTENT_NAME,
					CONTENT_SVC		CONTENT_SVC,
					CONTENT_SEQ		CONTENT_SEQ
			FROM	#CONTENT_TABLE# WITH(NOLOCK)
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
			ORDER BY CONTENT_SEQ
		]]></sql-hash>
		<!-- 전송하지 못한 상태값이 6번인 경우 상태값을 1로 업데이트 한다. -->
		<sql-hash id="mms-query-update-pushed-nosend-target"><![CDATA[
			UPDATE	#MSG_TABLE# WITH (TABLOCK)
			SET		TRAN_STATUS='1'
			WHERE	TRAN_STATUS='6'
			AND   	TRAN_TYPE ='5'
		]]></sql-hash>		
		<sql-hash id="mms-query-update-pushed-target"><![CDATA[
			UPDATE	#MSG_TABLE# WITH (TABLOCK)
			SET		TRAN_STATUS='6',
			      	TRAN_SENDDATE=CONVERT(datetime,
										SUBSTRING('${r"${TRAN_SENDDATE}"}',1,4) + '-'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',5,2) + '-'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',7,2) + ' '
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',9,2) + ':'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',11,2) + ':'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',13,2) , 120)			
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'	
		]]></sql-hash>

		<sql-hash id="mms-query-msg-start-log"><![CDATA[
			UPDATE	#MSG_TABLE# WITH (TABLOCK)
			SET		TRAN_DATE = CONVERT(datetime,
										SUBSTRING('${r"${TRAN_DATE}"}',1,4) + '-'
										+ SUBSTRING('${r"${TRAN_DATE}"}',5,2) + '-'
										+ SUBSTRING('${r"${TRAN_DATE}"}',7,2) + ' '
										+ SUBSTRING('${r"${TRAN_DATE}"}',9,2) + ':'
										+ SUBSTRING('${r"${TRAN_DATE}"}',11,2) + ':'
										+ SUBSTRING('${r"${TRAN_DATE}"}',13,2) , 120)
			WHERE	TRAN_PR	='${r"${TRAN_PR}"}'  AND TRAN_STATUS !='3'
		]]></sql-hash>

		<sql-hash id="mms-query-msg-log"><![CDATA[
			UPDATE	#MSG_TABLE# WITH (TABLOCK)
			SET		TRAN_STATUS 	= '${r"${TRAN_STATUS}"}',
					TRAN_SENDDATE	= CONVERT(datetime,
										SUBSTRING('${r"${TRAN_SENDDATE}"}',1,4) + '-'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',5,2) + '-'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',7,2) + ' '
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',9,2) + ':'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',11,2) + ':'
										+ SUBSTRING('${r"${TRAN_SENDDATE}"}',13,2) , 120)
			WHERE	TRAN_PR	='${r"${TRAN_PR}"}'  AND TRAN_STATUS !='3'
		]]></sql-hash>

		<sql-hash id="mms-query-update-result"><![CDATA[
			UPDATE	#MSG_TABLE# WITH (TABLOCK)
			SET		TRAN_STATUS	  	='${r"${TRAN_STATUS}"}',
					TRAN_RSLT	  	='${r"${TRAN_RSLT}"}',
					TRAN_END_TELCO	='${r"${TRAN_END_TELCO}"}',
					TRAN_RSLTDATE	= CONVERT(datetime,
										SUBSTRING('${r"${TRAN_RSLTDATE}"}',1,4) + '-'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',5,2) + '-'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',7,2) + ' '
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',9,2) + ':'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',11,2) + ':'
										+ SUBSTRING('${r"${TRAN_RSLTDATE}"}',13,2) , 120),
  					TRAN_REPORTDATE	= CONVERT(datetime,
										SUBSTRING('${r"${TRAN_REPORTDATE}"}',1,4) + '-'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',5,2) + '-'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',7,2) + ' '
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',9,2) + ':'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',11,2) + ':'
										+ SUBSTRING('${r"${TRAN_REPORTDATE}"}',13,2) , 120)
			WHERE	TRAN_PR	='${r"${TRAN_PR}"}'
		]]></sql-hash>
		<sql-hash id="mms-query-select-validationQuery"><![CDATA[
				SELECT 1
		]]></sql-hash>
		
		
		<!--
		<sql-hash id="mms-query-update-content"><![CDATA[
			UPDATE	#CONTENT_TABLE#
			SET		LOG = 'T'
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
		]]></sql-hash>
		-->

		<!-- MmsLogMigrationTask : Msg -->
		<sql-hash id="mms-update-msg-migration-target"><![CDATA[
			UPDATE	#MSG_SOURCE_TABLE# WITH (TABLOCK)
			SET		TRAN_LOG='Y'
			WHERE	( TRAN_STATUS in ('3','5', '4', '9', 'X') )
			OR		( TRAN_STATUS = '2' AND TRAN_SENDDATE < DATEADD (HOUR, - ${mms_migrate_ttl}, GetDate() )  )
		]]></sql-hash>
		<sql-hash id="mms-update-content-migration-target"><![CDATA[
		    UPDATE  #CONTENT_SOURCE_TABLE# WITH (TABLOCK)
			  SET     TRAN_LOG='Y'
			  WHERE   TRAN_PR IN ( SELECT TRAN_PR FROM #MSG_SOURCE_TABLE# WITH(NOLOCK) WHERE TRAN_LOG='Y' )
		]]></sql-hash>

		<sql-hash id="mms-copy-msg-migration-target-before"><![CDATA[
			INSERT INTO #MSG_BEFORE_TARGET_TABLE#
			SELECT	*
			FROM	#MSG_SOURCE_TABLE# WITH(NOLOCK)
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE < CONVERT(datetime,  '#TIME_STAMP#-01 00:00:00' , 120)
		]]></sql-hash>
		<sql-hash id="mms-copy-msg-migration-target-current"><![CDATA[
			INSERT INTO #MSG_CURRENT_TARGET_TABLE#
			SELECT	*
			FROM	#MSG_SOURCE_TABLE# WITH(NOLOCK)
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= CONVERT(datetime,  '#TIME_STAMP#-01 00:00:00' , 120)
			AND		TRAN_DATE < CONVERT(datetime,  '#AFTER_TIME_STAMP#-01 00:00:00' , 120)
		]]></sql-hash>
		<sql-hash id="mms-copy-msg-migration-target-after"><![CDATA[
			INSERT INTO #MSG_AFTER_TARGET_TABLE#
			SELECT	*
			FROM	#MSG_SOURCE_TABLE# WITH(NOLOCK)
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= CONVERT(datetime,  '#AFTER_TIME_STAMP#-01 00:00:00' , 120)
		]]></sql-hash>

		<sql-hash id="mms-copy-content-migration-target-before"><![CDATA[
			INSERT INTO #CONTENT_BEFORE_TARGET_TABLE#
			( TRAN_PR, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_PR, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#MSG_SOURCE_TABLE# A WITH(NOLOCK),
					#CONTENT_SOURCE_TABLE# B WITH(NOLOCK)
			WHERE	A.TRAN_DATE < CONVERT(datetime,  '#TIME_STAMP#-01 00:00:00' , 120)
			AND		A.TRAN_LOG='Y'
			AND		A.TRAN_PR = B.TRAN_PR
			AND		B.TRAN_LOG='Y'
		]]></sql-hash>
		
		<sql-hash id="mms-copy-content-migration-target-current"><![CDATA[
			INSERT INTO #CONTENT_CURRENT_TARGET_TABLE#
			( TRAN_PR, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_PR, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#MSG_SOURCE_TABLE# A WITH(NOLOCK),
					#CONTENT_SOURCE_TABLE# B WITH(NOLOCK)
			WHERE	A.TRAN_LOG='Y'
			AND		A.TRAN_DATE >= CONVERT(datetime,  '#TIME_STAMP#-01 00:00:00' , 120)
			AND		A.TRAN_DATE < CONVERT(datetime,  '#AFTER_TIME_STAMP#-01 00:00:00' , 120)
			AND		A.TRAN_PR = B.TRAN_PR
			AND		B.TRAN_LOG='Y'
		]]></sql-hash>

		<sql-hash id="mms-copy-content-migration-target-after"><![CDATA[
			INSERT INTO #CONTENT_AFTER_TARGET_TABLE#
			( TRAN_PR, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_PR, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#MSG_SOURCE_TABLE# A WITH(NOLOCK),
					#CONTENT_SOURCE_TABLE# B WITH(NOLOCK)
			WHERE	A.TRAN_LOG='Y'
			AND		A.TRAN_DATE >= CONVERT(datetime,  '#AFTER_TIME_STAMP#-01 00:00:00' , 120)
			AND		A.TRAN_PR = B.TRAN_PR
			AND		B.TRAN_LOG='Y'
		]]></sql-hash>

		<sql-hash id="mms-delete-msg-migration-target"><![CDATA[
			DELETE FROM #MSG_SOURCE_TABLE# WHERE TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-delete-content-migration-target"><![CDATA[
			DELETE FROM #CONTENT_SOURCE_TABLE# WHERE TRAN_LOG='Y'
		]]></sql-hash>
	</sql>
</module>
</CONF>

<!--
insert into kb_tran
(
TRAN_REFKEY,
TRAN_ID,
TRAN_PHONE,
TRAN_CALLBACK,
TRAN_MSG,
TRAN_DATE,
TRAN_TYPE,
TRAN_STATUS
)
values
('dragon','1234','01199716837','1588-3000','test msg',
now(),0,'1')
-->
