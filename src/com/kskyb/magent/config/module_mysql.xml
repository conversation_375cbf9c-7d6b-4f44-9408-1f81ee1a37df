<?xml version="1.0" encoding="EUC-KR"?>
<#if sms_migrate_ttl??>
<#else>
	<#assign sms_migrate_ttl="36"/>
</#if>
<#if mms_migrate_ttl??>
<#else>
	<#assign mms_migrate_ttl="36"/>
</#if>
<CONF>

<!--
<module id="${db_type}" clazz="com.kskyb.broker.service.pool.jdbc.CommonPooledConnectionPool"
		broadcast="connection.local.pool"
		max="20" min="1" factor="0.5" emptyWait="-1" defaultEntry="true">
	<factory clazz="com.kskyb.broker.service.pool.jdbc.CommonPooledConnectionFactory">
		<connection-config clazz="com.kskyb.broker.service.pool.jdbc.PoolConfig" authCommit="true">
			<driver>${db_connect_driver}</driver>
			<url>${db_connect_url}</url>
			<#if db_connect_id??><user>${db_connect_id}</user></#if>
			<#if db_connect_pass??><password><![CDATA[${db_connect_pass}]]></password></#if>
			<#if db_base_charset??><base-charset>${db_base_charset}</base-charset></#if>
			<#if db_in_charset??><incoming-charset>${db_in_charset}</incoming-charset></#if>
			<#if db_out_charset??><outgoing-charset>${db_out_charset}</outgoing-charset></#if>
			<wait-on-handshake-fail>${wait_on_handshake_fail}</wait-on-handshake-fail>
			<wait-on-handshake-fail-interval>${wait_on_handshake_fail_interval}000</wait-on-handshake-fail-interval>
			<validation-query>SELECT NOW()</validation-query>
		</connection-config>
	</factory>
</module>
-->
<!-- <#if db_base_charset??><#else><#assign db_base_charset="KSC5601"></#if> -->
<!-- <#if db_in_charset??><#else><#assign db_in_charset="KSC5601"></#if> -->
<!-- <#if db_out_charset??><#else><#assign db_out_charset="KSC5601"></#if> -->
<module
	clazz="com.kskyb.broker.service.pool.db.DataSourcePoolService"
	serviceName="${db_type}"
	startOnRegist="true"
	defaultEntry="true"
	max="20"
	min="1"
	limit="20"
	idleLimit="60000"
	emptyWait="1000">
	<desc>${db_type}</desc>
	<note>${db_type}</note>
	<thread-usage-expire-interval>3600000</thread-usage-expire-interval>
	<thread-usage-monitor-interval>360000</thread-usage-monitor-interval>
	<data-source
		clazz="com.kskyb.broker.util.jdbc.datasource.DataSourceDelegate"
		initial="${db_name}"
		autoCommit="true">
		<driver>${db_connect_driver}</driver>
		<url>${db_connect_url}</url>
		<#if db_connect_id??><user>${db_connect_id}</user></#if>
		<#if db_connect_pass??><password><![CDATA[${db_connect_pass}]]></password></#if>
		<charsets>${db_base_charset},${db_in_charset},${db_out_charset}</charsets>
		<activate-query>SELECT NOW()</activate-query>		
		<#if extractor??>
		<extractor>${extractor}</extractor>
		</#if>
	</data-source>
	<handler
		clazz="com.kskyb.broker.service.pool.PoolBoundaryHandler">
	</handler>
</module>

<module broadcast="sql.exception.codes" clazz="com.kskyb.broker.adapter.jdbc.SQLExceptionFilter">
	<code>
		<code-hash id="0">connect</code-hash>
		<code-hash id="1146">object</code-hash>
		<code-hash id="1064">syntax</code-hash>
	</code>
</module>

<#assign sms_send_ttl><#if sms_send_ttl??>${sms_send_ttl}<#else>0</#if></#assign>
<#assign mms_send_ttl><#if mms_send_ttl??>${mms_send_ttl}<#else>0</#if></#assign>

<module broadcast="sql.set.sms" clazz="com.kskyb.broker.util.jdbc.SQLSet">
	<sql>
		<sql-hash id="table-exists-scan"><![CDATA[
			SHOW TABLES LIKE '#TABLE_NAME#'
		]]></sql-hash>
		<sql-hash id="sms-create-source-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# (
				 TRAN_PR            bigint(16) auto_increment NOT NULL ,
				 TRAN_REFKEY        VARCHAR(20) ,
				 TRAN_ID            VARCHAR(20) ,
				 TRAN_PHONE         VARCHAR(15) NOT NULL ,
				 TRAN_CALLBACK      VARCHAR(15) NOT NULL ,
				 TRAN_MSG           VARCHAR(150) NOT NULL ,
				 TRAN_DATE          DATETIME NOT NULL ,
				 TRAN_TYPE          int  DEFAULT 0 NOT NULL ,
				 TRAN_STATUS        CHAR(1) DEFAULT '1' NOT NULL ,
				 TRAN_SENDDATE      DATETIME ,
				 TRAN_REPORTDATE    DATETIME ,
				 TRAN_RSLTDATE      DATETIME ,
				 TRAN_RSLT          CHAR(2) ,
				 TRAN_ETC1          VARCHAR(160) ,
				 TRAN_ETC2          VARCHAR(160) ,
				 TRAN_ETC3          VARCHAR(160) ,
				 TRAN_ETC4          VARCHAR(160) ,				 
				 TRAN_END_TELCO     VARCHAR(8) ,
				 TRAN_LOG			CHAR(1) DEFAULT 'N',
				 TRAN_GRPSEQ 		INT,
				 constraint #TABLE_NAME#_pk primary key (TRAN_PR)
				 ) DEFAULT CHARACTER SET euckr COLLATE euckr_korean_ci
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx1"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX1 ON #TABLE_NAME#(TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx2"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX2 ON #TABLE_NAME#(TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="sms-create-source-table-idx3"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX3 ON #TABLE_NAME#(TRAN_STATUS)
		]]></sql-hash>		
		<sql-hash id="sms-create-source-table-idx4"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX4 ON #TABLE_NAME#(TRAN_REFKEY)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table"><![CDATA[
			 CREATE TABLE #TABLE_NAME# (
				 TRAN_PR            bigint(16) NOT NULL ,
				 TRAN_REFKEY        VARCHAR(20),
				 TRAN_ID            VARCHAR(20),
				 TRAN_PHONE         VARCHAR(15) NOT NULL ,
				 TRAN_CALLBACK      VARCHAR(15) NOT NULL ,
				 TRAN_MSG           VARCHAR(150) NOT NULL ,
				 TRAN_DATE          DATETIME NOT NULL ,
				 TRAN_TYPE          int  DEFAULT 0 NOT NULL ,
				 TRAN_STATUS        CHAR(1) DEFAULT '1' NOT NULL,
				 TRAN_SENDDATE      DATETIME ,
				 TRAN_REPORTDATE    DATETIME ,
				 TRAN_RSLTDATE      DATETIME ,
				 TRAN_RSLT          CHAR(2) ,
				 TRAN_ETC1          VARCHAR(160) ,
				 TRAN_ETC2          VARCHAR(160) ,
				 TRAN_ETC3          VARCHAR(160) ,
				 TRAN_ETC4          VARCHAR(160) ,				 
				 TRAN_END_TELCO     VARCHAR(8) ,
				 TRAN_LOG			CHAR(1) DEFAULT 'N',
				 TRAN_GRPSEQ 		INT	
				 ) DEFAULT CHARACTER SET euckr COLLATE euckr_korean_ci			 
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx1"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX1 ON #TABLE_NAME#(TRAN_PR)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx2"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX2 ON #TABLE_NAME#(TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx3"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX3 ON #TABLE_NAME#(TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx4"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX4 ON #TABLE_NAME#(TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="sms-create-migration-table-idx5"><![CDATA[
			CREATE INDEX #TABLE_NAME#_IDX5 ON #TABLE_NAME#(TRAN_REFKEY)
		]]></sql-hash>
		<!-- 대상을 추출한다. SmsSourceTableScanTask -->
		<sql-hash id="sms-query-update-out-of-ttl-target"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS='5',
					TRAN_RSLT='OT',
					TRAN_SENDDATE	= NOW(),
					TRAN_RSLTDATE	= NOW(),
					TRAN_REPORTDATE	= NOW()
			WHERE	TRAN_STATUS='1'
			AND		DATE_ADD(TRAN_DATE,INTERVAL ${sms_send_ttl} MINUTE) < NOW()
		]]></sql-hash>
		<sql-hash id="sms-query-select-checked-target"><![CDATA[
			SELECT	TRAN_PR			TRAN_PR,
					TRAN_PHONE		TRAN_PHONE,
					TRAN_CALLBACK	TRAN_CALLBACK,
					TRAN_MSG		TRAN_MSG,
<#if sms_select_additional_info??>${sms_select_additional_info}<#else>' '</#if>	TRAN_REVERSE,
					TRAN_ID			TRAN_REVERSE,
					TRAN_TYPE		TRAN_TYPE,
					DATE_FORMAT(
						DATE_ADD(
							TRAN_DATE,INTERVAL ${sms_send_ttl} MINUTE
						) ,'%Y%m%d%H%i%s'
					)				TRAN_TTL
			FROM	#TABLE_NAME#
			WHERE	TRAN_STATUS='1'
			AND TRAN_DATE < NOW()
			AND		TRAN_DATE > DATE_SUB(NOW(), INTERVAL ${sms_send_ttl} MINUTE)
			<#if sms_select_target_addon_query??>${sms_select_target_addon_query}</#if> LIMIT	${r"${LIMIT}"}
		]]></sql-hash>
		<!-- 전송하지 못한 상태값이 4,6번인 경우 상태값을 1로 업데이트 한다. -->
		<sql-hash id="sms-query-update-checked-nosend-target"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS = '1'
			WHERE	TRAN_STATUS IN ('4','6')
		]]></sql-hash>		
		<!-- 버퍼로 넣고나서 업데이트 한다. -->
		<sql-hash id="sms-query-update-checked-target"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS='6',
					TRAN_SENDDATE= NOW()
			WHERE	TRAN_PR = '${r"${TRAN_PR}"}'
		]]></sql-hash>
		
		<sql-hash id="sms-query-select-validationQuery"><![CDATA[
				SELECT 1
		]]></sql-hash>


		<!-- -->
		<sql-hash id="sms-query-update-log"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS='${r"${TRAN_STATUS}"}',
					TRAN_RSLT='${r"${TRAN_RSLT}"}'
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
			AND		TRAN_STATUS != '3'
		]]></sql-hash>
		<sql-hash id="sms-query-update-result"><![CDATA[
			UPDATE	#TABLE_NAME#
			SET		TRAN_STATUS		='3',
					TRAN_RSLT		='${r"${TRAN_RSLT}"}',
					TRAN_END_TELCO	='${r"${TRAN_END_TELCO}"}',
					TRAN_RSLTDATE	=DATE_FORMAT('${r"${TRAN_RSLTDATE}"}','%Y%m%d%H%i%s'),
					TRAN_REPORTDATE	=DATE_FORMAT('${r"${TRAN_REPORTDATE}"}','%Y%m%d%H%i%s')
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
		]]></sql-hash>
		 <!-- table migration : SmsLogMigrationTask -->
		<sql-hash id="sms-query-update-migration-log"><![CDATA[
			UPDATE	#SOURCE_TABLE_NAME#
			SET		TRAN_LOG		='Y'
			WHERE	( TRAN_STATUS in ('3','5', '4', '9', 'X') )
			OR		( TRAN_STATUS = '2' AND DATE_ADD( TRAN_SENDDATE , INTERVAL ${sms_migrate_ttl} HOUR ) < NOW() )
		]]></sql-hash>

		<sql-hash id="sms-query-copy-migration-log-before"><![CDATA[
			INSERT INTO #BEFORE_TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE < DATE_FORMAT( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
		]]></sql-hash>
		<sql-hash id="sms-query-copy-migration-log-current"><![CDATA[
			INSERT INTO #TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= DATE_FORMAT( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
			AND		TRAN_DATE < DATE_FORMAT( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
		]]></sql-hash>
		<sql-hash id="sms-query-copy-migration-log-after"><![CDATA[
			INSERT INTO #AFTER_TARGET_TABLE_NAME#
			SELECT	*
			FROM	#SOURCE_TABLE_NAME#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= DATE_FORMAT( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
		]]></sql-hash>

		<sql-hash id="sms-query-delete-migration-log"><![CDATA[
			DELETE FROM #SOURCE_TABLE_NAME# WHERE TRAN_LOG='Y'
		]]></sql-hash>
	</sql>
</module>
<module broadcast="sql.set.mms" clazz="com.kskyb.broker.util.jdbc.SQLSet">
	<sql>
		<sql-hash id="table-exists-scan"><![CDATA[
			SHOW TABLES LIKE '#TABLE_NAME#'
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table"><![CDATA[
			CREATE TABLE #TABLE_NAME# 
			(
				TRAN_PR				BIGINT(16) auto_increment NOT NULL,
				TRAN_REFKEY			VARCHAR(20),
				TRAN_ID				VARCHAR(20),			  		
				TRAN_CALLBACK		VARCHAR(15)		NOT NULL,
				TRAN_PHONE    		VARCHAR(15)	  	NOT NULL,
				TRAN_SUBJECT		VARCHAR(100),
				TRAN_MSG			VARCHAR(2000)			NOT NULL,
				TRAN_DATE			DATETIME		NOT NULL,
				TRAN_TYPE      		INT				NOT NULL,
				TRAN_STATUS			CHAR(1) 		DEFAULT '1' NOT NULL,
				TRAN_SENDDATE		DATETIME,
				TRAN_REPORTDATE		DATETIME,
        		TRAN_RSLTDATE		DATETIME,	
        		TRAN_RSLT			VARCHAR(5),        		
				TRAN_ETC1			VARCHAR(160),
				TRAN_ETC2			VARCHAR(160),
				TRAN_ETC3			VARCHAR(160),
				TRAN_ETC4			VARCHAR(160),				
				TRAN_END_TELCO		VARCHAR(8),
				TRAN_LOG			CHAR(1) DEFAULT 'N',
				TRAN_GRPSEQ 		INT,
				constraint #TABLE_NAME#_pk primary key (TRAN_PR)				          
			) DEFAULT CHARACTER SET euckr COLLATE euckr_korean_ci
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table-idx1"><![CDATA[
			create index idx1_#TABLE_NAME# on #TABLE_NAME# (TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (TRAN_DATE)
		]]></sql-hash>
		<sql-hash id="mms-create-source-msg-table-idx3"><![CDATA[
			create index idx3_#TABLE_NAME# on #TABLE_NAME# (TRAN_STATUS)
		]]></sql-hash>		
		<sql-hash id="mms-create-source-msg-table-idx4"><![CDATA[
			create index idx4_#TABLE_NAME# on #TABLE_NAME# (TRAN_REFKEY)
		]]></sql-hash>

		<sql-hash id="mms-create-migration-msg-table"><![CDATA[
			  CREATE TABLE #TABLE_NAME# (
				TRAN_PR				BIGINT(16)		NOT NULL,
				TRAN_REFKEY			VARCHAR(20),
				TRAN_ID				VARCHAR(20),												
				TRAN_CALLBACK		VARCHAR(15)		NOT NULL,
				TRAN_PHONE    		VARCHAR(15)	  	NOT NULL,
				TRAN_SUBJECT		VARCHAR(100),
				TRAN_MSG			VARCHAR(2000)			NOT NULL,
				TRAN_DATE			DATETIME		NOT NULL,
				TRAN_TYPE      		INT				NOT NULL,
				TRAN_STATUS			CHAR(1) 		DEFAULT '1' NOT NULL,
				TRAN_SENDDATE		DATETIME,
				TRAN_REPORTDATE		DATETIME,
        		TRAN_RSLTDATE		DATETIME,
        		TRAN_RSLT			VARCHAR(5),        		
				TRAN_ETC1			VARCHAR(160),
				TRAN_ETC2			VARCHAR(160),
				TRAN_ETC3			VARCHAR(160),
				TRAN_ETC4			VARCHAR(160),				
				TRAN_END_TELCO		VARCHAR(8),
				TRAN_LOG			CHAR(1) DEFAULT 'N',
				TRAN_GRPSEQ 		INT
			) DEFAULT CHARACTER SET euckr COLLATE euckr_korean_ci
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx1"><![CDATA[
			create index idx1_#TABLE_NAME# on #TABLE_NAME# (TRAN_PR)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx2"><![CDATA[
			create index idx2_#TABLE_NAME# on #TABLE_NAME# (TRAN_PHONE)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx3"><![CDATA[
			create index idx3_#TABLE_NAME# on #TABLE_NAME# (TRAN_DATE)
		]]></sql-hash>		
		<sql-hash id="mms-create-migration-msg-table-idx4"><![CDATA[
			create index idx4_#TABLE_NAME# on #TABLE_NAME# (TRAN_STATUS)
		]]></sql-hash>
		<sql-hash id="mms-create-migration-msg-table-idx5"><![CDATA[
			create index idx5_#TABLE_NAME# on #TABLE_NAME# (TRAN_REFKEY)
		]]></sql-hash>
		
		<sql-hash id="mms-create-source-content-table"><![CDATA[
			CREATE TABLE #TABLE_NAME#
			(
				TRAN_PR  		BIGINT(16)			NOT NULL,
				CONTENT_SEQ		INT					NOT NULL,
				CONTENT_TYPE	VARCHAR(3)			NOT NULL,
				CONTENT_NAME	VARCHAR(255)		NOT NULL,
				CONTENT_SVC		VARCHAR(3),
				TRAN_LOG		CHAR(1) DEFAULT 'N',
				PRIMARY KEY (TRAN_PR, CONTENT_SEQ)
			) DEFAULT CHARACTER SET euckr COLLATE euckr_korean_ci
		]]></sql-hash>
		<sql-hash id="mms-create-source-content-table-idx"><![CDATA[
			create idex  idx_#TABLE_NAME# on #TABLE_NAME# (TRAN_PR)
		]]></sql-hash>

		<sql-hash id="mms-create-migration-content-table"><![CDATA[
			CREATE TABLE #TABLE_NAME#
			(
				TRAN_PR  		BIGINT(16)			NOT NULL,
				CONTENT_SEQ		INT					NOT NULL,
				CONTENT_TYPE	VARCHAR(3)			NOT NULL,
				CONTENT_NAME	VARCHAR(255)		NOT NULL,
				CONTENT_SVC		VARCHAR(3),
				TRAN_LOG		CHAR(1) DEFAULT 'N'
			) DEFAULT CHARACTER SET euckr COLLATE euckr_korean_ci
		]]></sql-hash>
		<!-- MmsSourceTableScanTask -->
		<sql-hash id="mms-query-update-out-of-ttl-msg"><![CDATA[
			UPDATE	#MSG_TABLE#
			SET		TRAN_STATUS='3',
			      	TRAN_RSLT='OTTL'
			WHERE	DATE_ADD(TRAN_DATE, INTERVAL ${mms_send_ttl} MINUTE) < NOW()
		]]></sql-hash>

		<!-- MmsSourceTableScanTask -->
		<sql-hash id="mms-query-select-send-target"><![CDATA[
			SELECT	TRAN_PR			TRAN_PR,
					TRAN_ID			TRAN_ID,
            		TRAN_CALLBACK	TRAN_CALLBACK,
              		TRAN_PHONE		TRAN_PHONE,
		          	TRAN_SUBJECT	TRAN_SUBJECT,
        		  	TRAN_MSG		TRAN_MSG,
		          	TRAN_TYPE		TRAN_TYPE,		          
		          	DATE_FORMAT(
       		  			DATE_ADD(TRAN_DATE,INTERVAL ${mms_send_ttl} MINUTE), '%Y%m%d%H%i%s' 
       		  		) TTL,
      	  	  		DATE_FORMAT(TRAN_DATE,'%Y%m%d%H%i%s') TRAN_DATE
      		FROM	#MSG_TABLE# 
      		WHERE	TRAN_STATUS = '1'
      		AND		TRAN_TYPE = 4
      		AND 	TRAN_DATE < NOW()
      		AND 	TRAN_DATE > DATE_SUB(NOW(), INTERVAL ${mms_send_ttl} MINUTE)
      		<#if mms_select_target_addon_query_mms??>${mms_select_target_addon_query_mms}</#if> LIMIT	${r"${LIMIT}"}					
		]]></sql-hash>
		<sql-hash id="mms-query-select-contents-target"><![CDATA[
			SELECT	CONTENT_TYPE	CONTENT_TYPE,
      <#if mms_content_dir??>CONCAT('${mms_content_dir}',CONTENT_NAME)<#else>CONTENT_NAME</#if> AS
    				CONTENT_NAME,
		   			CONTENT_SVC		CONTENT_SVC,
			    	CONTENT_SEQ		CONTENT_SEQ
			FROM	#CONTENT_TABLE#
			WHERE	TRAN_PR = ${r"${TRAN_PR}"}
			ORDER BY CONTENT_SEQ
		]]></sql-hash>
		<!-- 전송하지 못한 상태값이 6번인 경우 상태값을 1로 업데이트 한다. -->
		<sql-hash id="mms-query-update-pushed-nosend-target"><![CDATA[
			UPDATE	#MSG_TABLE#
			SET		TRAN_STATUS='1'
			WHERE	TRAN_STATUS='6'
			AND   	TRAN_TYPE='5'
		]]></sql-hash>		
		<sql-hash id="mms-query-update-pushed-target"><![CDATA[
			UPDATE	#MSG_TABLE#
			SET		TRAN_STATUS='6',
			        TRAN_SENDDATE = DATE_FORMAT('${r"${TRAN_SENDDATE}"}','%Y%m%d%H%i%s')
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
		]]></sql-hash>
		<sql-hash id="mms-query-msg-start-log"><![CDATA[
			UPDATE	#MSG_TABLE#
			SET		TRAN_DATE = DATE_FORMAT('${r"${TRAN_DATE}"}','%Y%m%d%H%i%s')
			WHERE	TRAN_PR	='${r"${TRAN_PR}"}'  AND TRAN_STATUS !='3'
		]]></sql-hash>
		<sql-hash id="mms-query-msg-log"><![CDATA[
			UPDATE	#MSG_TABLE#
			SET	  	TRAN_STATUS 	= '${r"${TRAN_STATUS}"}',
					TRAN_SENDDATE	= DATE_FORMAT('${r"${TRAN_SENDDATE}"}','%Y%m%d%H%i%s')
			WHERE	TRAN_PR		='${r"${TRAN_PR}"}'  AND TRAN_STATUS !='3'
		]]></sql-hash>
		<sql-hash id="mms-query-update-result"><![CDATA[
			UPDATE	#MSG_TABLE#
			SET		TRAN_STATUS		= '${r"${TRAN_STATUS}"}',
			  		TRAN_RSLT	    = '${r"${TRAN_RSLT}"}',
			  		TRAN_END_TELCO  = '${r"${TRAN_END_TELCO}"}',
				  	TRAN_RSLTDATE	= DATE_FORMAT('${r"${TRAN_RSLTDATE}"}','%Y%m%d%H%i%s'),
					TRAN_REPORTDATE = DATE_FORMAT('${r"${TRAN_REPORTDATE}"}','%Y%m%d%H%i%s')
			WHERE	TRAN_PR	    	='${r"${TRAN_PR}"}'
		]]></sql-hash>
		<sql-hash id="mms-query-select-validationQuery"><![CDATA[
				SELECT 1
		]]></sql-hash>
		
		<!--
		<sql-hash id="mms-query-update-content"><![CDATA[
			UPDATE	#CONTENT_TABLE#
			SET		LOG = 'T'
			WHERE	TRAN_PR='${r"${TRAN_PR}"}'
		]]></sql-hash>
		-->
		<!-- MmsLogMigrationTask : Msg -->
		<sql-hash id="mms-update-msg-migration-target"><![CDATA[
			UPDATE	#MSG_SOURCE_TABLE#
			SET		TRAN_LOG='Y'
			WHERE	( TRAN_STATUS in ('3','5', '4', '9', 'X') )
			OR		( TRAN_STATUS = '2' AND DATE_ADD( TRAN_SENDDATE , INTERVAL ${mms_migrate_ttl} HOUR ) < NOW() )
		]]></sql-hash>
		<sql-hash id="mms-update-content-migration-target"><![CDATA[
			UPDATE	#MSG_SOURCE_TABLE# A,
				    #CONTENT_SOURCE_TABLE# B
			SET		B.TRAN_LOG='Y'
			WHERE	A.TRAN_PR = B.TRAN_PR
			AND		A.TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-copy-msg-migration-target-before"><![CDATA[
			INSERT INTO #MSG_BEFORE_TARGET_TABLE#
			SELECT	*
			FROM	#MSG_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE < DATE_FORMAT( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
		]]></sql-hash>
		<sql-hash id="mms-copy-msg-migration-target-current"><![CDATA[
			INSERT INTO #MSG_CURRENT_TARGET_TABLE#
			SELECT	*
			FROM	#MSG_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= DATE_FORMAT( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
			AND		TRAN_DATE < DATE_FORMAT( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
		]]></sql-hash>
		<sql-hash id="mms-copy-msg-migration-target-after"><![CDATA[
			INSERT INTO #MSG_AFTER_TARGET_TABLE#
			SELECT	*
			FROM	#MSG_SOURCE_TABLE#
			WHERE	TRAN_LOG='Y'
			AND		TRAN_DATE >= DATE_FORMAT( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
		]]></sql-hash>

		<sql-hash id="mms-copy-content-migration-target-before"><![CDATA[
			INSERT INTO #CONTENT_BEFORE_TARGET_TABLE#
			( TRAN_PR, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_PR, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM  	#MSG_SOURCE_TABLE# A,
		  			#CONTENT_SOURCE_TABLE# B
			WHERE	A.TRAN_DATE < DATE_FORMAT( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
			AND	  	A.TRAN_LOG='Y'
			AND	  	A.TRAN_PR = B.TRAN_PR
			AND	  	B.TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-copy-content-migration-target-current"><![CDATA[
			INSERT INTO #CONTENT_CURRENT_TARGET_TABLE#
			( TRAN_PR, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_PR, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#MSG_SOURCE_TABLE# A,
    				#CONTENT_SOURCE_TABLE# B
			WHERE	A.TRAN_LOG='Y'
			AND		A.TRAN_DATE >= DATE_FORMAT( '#TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
			AND		A.TRAN_DATE < DATE_FORMAT( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
			AND		A.TRAN_PR = B.TRAN_PR
			AND		B.TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-copy-content-migration-target-after"><![CDATA[
			INSERT INTO #CONTENT_AFTER_TARGET_TABLE#
			( TRAN_PR, CONTENT_SEQ, CONTENT_TYPE, CONTENT_NAME, CONTENT_SVC, TRAN_LOG )
			SELECT	B.TRAN_PR, B.CONTENT_SEQ, B.CONTENT_TYPE, B.CONTENT_NAME, B.CONTENT_SVC, B.TRAN_LOG
			FROM	#MSG_SOURCE_TABLE# A,
				    #CONTENT_SOURCE_TABLE# B
			WHERE	A.TRAN_LOG='Y'
			AND		A.TRAN_DATE >= DATE_FORMAT( '#AFTER_TIME_STAMP#-01 00:00:00','%Y-%m-%d %H:%i:%s' )
			AND	  	A.TRAN_PR = B.TRAN_PR
			AND	  	B.TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-delete-msg-migration-target"><![CDATA[
			DELETE FROM #MSG_SOURCE_TABLE# WHERE TRAN_LOG='Y'
		]]></sql-hash>
		<sql-hash id="mms-delete-content-migration-target"><![CDATA[
			DELETE FROM #CONTENT_SOURCE_TABLE# WHERE TRAN_LOG='Y'
		]]></sql-hash>
	</sql>
</module>
</CONF>

<!--
insert into kb_tran
(
TRAN_REFKEY,
TRAN_ID,
TRAN_PHONE,
TRAN_CALLBACK,
TRAN_MSG,
TRAN_DATE,
TRAN_TYPE,
TRAN_STATUS
)
values
('dragon','1234','01199716837','1588-3000','test msg',
now(),0,'1')
-->
