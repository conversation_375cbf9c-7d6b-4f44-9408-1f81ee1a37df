package com.kskyb.magent.config;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;


import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.event.DebugEchoEvent;
import com.kskyb.broker.lang.ExceptionGW;
import com.kskyb.broker.logging.CommonLogGW;
import com.kskyb.broker.service.pool.db.ConnectionPoolMasterService;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.broker.util.StringUtils;
import com.kskyb.broker.util.jdbc.SQLSet;
import com.kskyb.magent.MessageResource;

/**
 * 가변적으로 변하는 테이블이름을 생성하는 정보를 관리함.
 */
public class ResourceTableInfo {
	/**
	 * 
	 */
	private static final MessageResource resource = MessageResource.getResource(ResourceTableInfo.class);

	/**
	 * query for check exist<br>
	 * 리소스 존재 유무 파악 쿼리
	 */
	String checkQuery;

	/**
	 * query list for create table and indexes<br>
	 * 테이블 및 인덱스 생성 쿼리 인덱스 저장 리스트
	 */
	ArrayList<String> createQueryList = new ArrayList<String>();

	/**
	 * append date format<br>
	 * 리소스 이름 변환 날짜 포멧
	 */
	SimpleDateFormat format = null;

	/**
	 * current table name<br>
	 * 마지막 반환 테이블 이름
	 */
	String currentTableName = null;

	/**
	 * return current resouce name ( table name )<br>
	 * 현재 리소스 이름 반환
	 * 
	 * @param force
	 *            true : check force <br>
	 *            false : if same with
	 * @return resource name
	 */
	public String getResourceName(	long time,
									boolean force) {
		if (this.singletone) {
			if (force) {
				try {
					createTableOnNotExist(	this,
											this.tableName);
				}
				catch (Throwable e) {
					throw new RuntimeException(	"table.check.error",
												e);
				}
			}
			return this.tableName;
		}

		final String nowStamp = DateUtil.getFormatDate(	this.format,
														time);

		if (DebugEchoEvent.isEnable()) {
			CommonLogGW.debug("before name >> " + this.currentTableName);
			CommonLogGW.debug("current name >> " + nowStamp);
		}

		if (nowStamp.equals(this.currentTableName)) {
			if (force) {
				try {
					createTableOnNotExist(	this,
											this.currentTableName);
				}
				catch (Throwable e) {
					throw new RuntimeException(	"table.check.error",
												e);
				}
			}
			return this.currentTableName;
		}

		this.currentTableName = nowStamp;

		try {
			createTableOnNotExist(	this,
									this.currentTableName);
		}
		catch (Throwable e) {
			throw new RuntimeException(	"table.check.error",
										e);
		}

		return this.currentTableName;
	}

	/**
	 * create table after check table exist<br>
	 * 테이블이 존재하지 않을 경우 생성하는 루틴
	 * 
	 * @param info
	 *            resource info
	 * @param targetTableName
	 *            target table name
	 * @throws Throwable
	 *             error occur
	 */
	static final void createTableOnNotExist(ResourceTableInfo info,
											String targetTableName) throws Throwable {
		Statement stmt = null;
		ResultSet rs = null;
		Connection conn = null;
		String query = null;

		try {
			// conn = ConnectionPoolIndicator.getContainer().getInstance(info.connId);
			conn = ConnectionPoolMasterService.getService().getConnection(info.connId);
			stmt = conn.createStatement();

			// final String checkQuery = StringUtils.convertString(info.checkQuery,
			// "#TABLE_NAME#",
			// targetTableName);
			final String checkQuery = StringUtils.convertString(info.checkQuery,
																"#TABLE_NAME#",
																targetTableName);

			if (DebugEchoEvent.isEnable()) {
				CommonLogGW.debug("createTableOnNotExist execute >> " + checkQuery);
			}

			rs = stmt.executeQuery(checkQuery);

			if (DebugEchoEvent.isEnable()) {
				CommonLogGW.debug("createTableOnNotExist execute DONE >> ");
			}

			if (rs.next()) {
				if (DebugEchoEvent.isEnable()) {
					CommonLogGW.debug("createTableOnNotExist OK skip >> " + targetTableName);
				}
				return;
			}

			final List<String> querys = info.createQueryList;

			if (DebugEchoEvent.isEnable()) {
				CommonLogGW.debug("try create >> " + targetTableName);
			}
			for (int i = 0; i < querys.size(); i++) {
				query = StringUtils.convertString(	querys.get(i),
													"#TABLE_NAME#",
													targetTableName);

				if (DebugEchoEvent.isEnable()) {
					CommonLogGW.debug("try create query >> " + query);
				}

				stmt.execute(query);

				if (DebugEchoEvent.isEnable()) {
					CommonLogGW.debug("try create DONE >> ");
				}
			}

		}
		catch (Throwable thw) {
			ExceptionGW.filterThrowable(resource.getBundle(	"create.table.error",
															targetTableName,
															query),
										thw);
			return;
		}
		finally {
			if (rs != null) {
				rs.close();
				rs = null;
			}

			if (stmt != null) {
				stmt.close();
				stmt = null;
			}

			if (conn != null) {
				conn.close();
				conn = null;
			}
		}
	}

	/**
	 * 
	 */
	SQLSet sql = null;

	/**
	 * validation when initailize<br>
	 * 초기화 검증
	 */
	@ValidationMethod
	public final void validateResourceTableInfo() {
		this.sql = SQLSet.lookup(this.sqlName);
		this.checkQuery = this.sql.getSql(this.checkQueryId);

		StringTokenizer token = new StringTokenizer(this.createQueryId,
													",");

		while (token.hasMoreTokens()) {
			final String str = token.nextToken();

			if (str == null) {
				continue;
			}

			this.createQueryList.add(this.sql.getSql(str.trim()));
		}

		if (this.singletone) {
			try {
				createTableOnNotExist(	this,
										this.tableName);
			}
			catch (Throwable e) {
				throw new RuntimeException(	"table.check.error",
											e);
			}
			if (DebugEchoEvent.isEnable()) {
				CommonLogGW.debug("validation name >> " + this.tableName);
			}
			return;
		}

		this.currentTableName = new String();
		this.format = new SimpleDateFormat(this.tableName);

		final String firstName = this.getResourceName(	System.currentTimeMillis(),
														false);

		if (DebugEchoEvent.isEnable()) {
			CommonLogGW.debug("validation name >> " + firstName);
		}

	}

	/**
	 * target connection id<br>
	 * 대상 연결 아이디
	 */
	@MemberField(elementName = "connect-id", nullable = false)
	private String connId = null;

	/**
	 * sql set indicator<br>
	 * 쿼리셋 지정자
	 */
	@MemberField(elementName = "sql-set", nullable = false)
	private String sqlName = null;

	/**
	 * table name<br>
	 * 대상 테이블 이름
	 */
	@MemberField(elementName = "table-name", nullable = false)
	private String tableName;

	/**
	 * table exist check query<br>
	 * 테이블 존재여부 체크 쿼리
	 */
	@MemberField(elementName = "check-query", nullable = false)
	private String checkQueryId;

	/**
	 * create query list indexes<br>
	 * 실행쿼리 리스트 인덱스 문자열
	 */
	@MemberField(elementName = "create-query", nullable = false)
	private String createQueryId;

	/**
	 * flag fixed table name or dynamic table name<br>
	 * 고정 테이블 / 가변테이블 지정자
	 */
	@MemberField
	private boolean singletone = true;
}
