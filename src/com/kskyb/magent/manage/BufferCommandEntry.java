package com.kskyb.magent.manage;

/**
 * 모니터링 기본 인덱스를 정의함
 */
public interface BufferCommandEntry {
	/**
	 * SMS send buffer name<br>
	 * 발송버퍼 이름
	 */
	public static final String SMS_SEND_BUFFER = "SRQB";

	/**
	 * SMS log buffer name<br>
	 * 로그 버퍼 이름
	 */
	public static final String SMS_LOG_BUFFER = "SRSB";

	/**
	 * MMS send buffer name<br>
	 * 발송버퍼 이름
	 */
	public static final String MMS_SEND_BUFFER = "MRQB";

	/**
	 * MMS log buffer name<br>
	 * 로그버퍼 이름
	 */
	public static final String MMS_LOG_BUFFER = "MRSB";

	/**
	 * SMS send buffer index<br>
	 * 발송버퍼 인덱스
	 */
	public static final int SRQB = 0;

	/**
	 * SMS log buffer index<br>
	 * 로그버퍼 인덱스
	 */
	public static final int SRSB = 1;

	/**
	 * MMS send buffer index<br>
	 * 발송버퍼 인덱스
	 */
	public static final int MRQB = 2;

	/**
	 * MMS log buffer index<br>
	 * 로그버퍼 인덱스
	 */
	public static final int MRSB = 3;

	/**
	 * buffer name array<br>
	 * 버퍼 이름 배열
	 */
	public static final String[] BUFFER_NAMES = new String[] {
		SMS_SEND_BUFFER, SMS_LOG_BUFFER, MMS_SEND_BUFFER, MMS_LOG_BUFFER
	};

	/**
	 * SMS send buffer limit name<br>
	 * 발송버퍼 제한 이름
	 */
	public static final String SMS_SEND_BUFFER_LIMIT = SMS_SEND_BUFFER + "L";

	/**
	 * SMS log buffer limit name<br>
	 * 로그버퍼 제한 이름
	 */
	public static final String SMS_LOG_BUFFER_LIMIT = SMS_LOG_BUFFER + "L";

	/**
	 * MMS send buffer limit name<br>
	 * 발송버퍼 제한 이름
	 */
	public static final String MMS_SEND_BUFFER_LIMIT = MMS_SEND_BUFFER + "L";

	/**
	 * MMS log buffer limit name<br>
	 * 로그버퍼 제한 이름
	 */
	public static final String MMS_LOG_BUFFER_LIMIT = MMS_LOG_BUFFER + "L";

	/**
	 * SMS send buffer size name<br>
	 * 발송버퍼 사이즈 이름
	 */
	public static final String SMS_SEND_BUFFER_SIZE = SMS_SEND_BUFFER + "S";

	/**
	 * SMS log buffer size name<br>
	 * 로그버퍼 사이즈 이름
	 */
	public static final String SMS_LOG_BUFFER_SIZE = SMS_LOG_BUFFER + "S";

	/**
	 * MMS send buffer size name<br>
	 * 발송버퍼 사이즈 이름
	 */
	public static final String MMS_SEND_BUFFER_SIZE = MMS_SEND_BUFFER + "S";

	/**
	 * MMS log buffer size name<br>
	 * 로그버퍼 사이즈 이름
	 */
	public static final String MMS_LOG_BUFFER_SIZE = MMS_LOG_BUFFER + "S";

}
