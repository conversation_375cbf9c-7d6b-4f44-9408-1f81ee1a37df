package com.kskyb.magent.util;

import java.io.UnsupportedEncodingException;

import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.ValidationMethod;


public class Convertor
// implements
// IEncoder
{
	@MemberField
	protected String base;

	@MemberField
	protected String in;

	@MemberField
	protected String out;

	protected boolean inSkip;

	protected boolean outSkip;

	/**
	 * @return the base
	 */
	public String getBase() {
		return this.base;
	}

	/**
	 * @param base
	 *            the base to set
	 */
	public void setBase(String base) {
		this.base = base;
	}

	/**
	 * @return the in
	 */
	public String getIn() {
		return this.in;
	}

	/**
	 * @param in
	 *            the in to set
	 */
	public void setIn(String in) {
		this.in = in;
	}

	/**
	 * @return the out
	 */
	public String getOut() {
		return this.out;
	}

	/**
	 * @param out
	 *            the out to set
	 */
	public void setOut(String out) {
		this.out = out;
	}

	@ValidationMethod
	public void validate() throws Exception {
		this.inSkip = this.base.equalsIgnoreCase(this.in);
		this.outSkip = this.base.equalsIgnoreCase(this.out);

		"".getBytes(this.base);
		"".getBytes(this.in);
		"".getBytes(this.out);
	}

	public String decode(String src) {
		if (src == null) {
			return new String();
		}
		if (this.outSkip) {
			return src;
		}
		try {
			return new String(	src.getBytes(this.base),
								this.out);
		}
		catch (UnsupportedEncodingException e) {
			return src;
		}
	}

	public String encode(String src) {
		if (src == null) {
			return new String();
		}
		if (this.inSkip) {
			return src;
		}
		try {
			return new String(	src.getBytes(this.in),
								this.base);
		}
		catch (UnsupportedEncodingException e) {
			return src;
		}
	}

}
