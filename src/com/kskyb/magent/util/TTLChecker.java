package com.kskyb.magent.util;


import com.kskyb.broker.annotation.MemberField;
import com.kskyb.broker.annotation.StaticInstanciateMethod;
import com.kskyb.broker.annotation.ValidationMethod;
import com.kskyb.broker.util.DateUtil;
import com.kskyb.magent.struct.MMSSendStruct;
import com.kskyb.magent.struct.SMSSendStruct;

/**
 * TTL value를 비교하는 로직을 담당한다.
 */
public class TTLChecker {
	/**
	 * single tone instance
	 */
	private static final TTLChecker instance = new TTLChecker();

	/**
	 * get instance
	 * 
	 * @return checker instance
	 */
	@StaticInstanciateMethod
	public static final TTLChecker getInstance() {
		return instance;
	}

	/**
	 * sms ttl value <br>
	 * SMS ttl 시간 값.
	 */
	@MemberField(elementName = "sms-ttl", nullable = false)
	int smsTtl = 0;

	/**
	 * mms ttl value <br>
	 * MMS ttl 시간 값.
	 */
	@MemberField(elementName = "mms-ttl", nullable = false)
	int mmsTtl = 0;

	/**
	 * flag sms check <br>
	 * SMS 체크 여부.
	 */
	boolean smsBypass = true;

	/**
	 * flag mms check <br>
	 * MMS 체크여부.
	 */
	boolean mmsBypass = true;

	/**
	 * @return the smsTtl
	 */
	public final int getSmsTtl() {
		return this.smsTtl;
	}

	/**
	 * @param smsTtl
	 *            the smsTtl to set
	 */
	public final void setSmsTtl(int smsTtl) {
		this.smsTtl = smsTtl;
	}

	/**
	 * @return the mmsTtl
	 */
	public final int getMmsTtl() {
		return this.mmsTtl;
	}

	/**
	 * @param mmsTtl
	 *            the mmsTtl to set
	 */
	public final void setMmsTtl(int mmsTtl) {
		this.mmsTtl = mmsTtl;
	}

	/**
	 * @return the smsBypass
	 */
	public final boolean isSmsBypass() {
		return this.smsBypass;
	}

	/**
	 * @return the mmsBypass
	 */
	public final boolean isMmsBypass() {
		return this.mmsBypass;
	}

	/**
	 * validation when startup <br>
	 * 초기화시 유효성 검증.
	 */
	@ValidationMethod
	public final void validateTTLChecker() {
		this.smsBypass = (this.smsTtl <= 0);
		this.mmsBypass = (this.mmsTtl <= 0);
	}

	/**
	 * validation sms send info <br>
	 * SMS 정보 검증.
	 * 
	 * @param struct
	 *            send info
	 * @return true : valid / false : invalid
	 */
	public boolean isValidate(SMSSendStruct struct) {
		if (this.smsBypass) {
			return true;
		}

		String ttl = struct.getValue(SMSSendStruct.TRAN_TTL);

		String now = DateUtil.getFormatDate(DateUtil.serial_time);

		return now.compareTo(ttl) < 0;
	}

	/**
	 * validation mms send info <br>
	 * MMS 발송정보 검증.
	 * 
	 * @param struct
	 *            send info
	 * @return true : valid / false : invalid
	 */
	public boolean isValidate(MMSSendStruct struct) {
		if (this.mmsBypass) {
			return true;
		}
		String ttl = struct.getTtlDate();

		String now = DateUtil.getFormatDate(DateUtil.serial_time);

		return now.compareTo(ttl) < 0;
	}

	/**
	 * construct
	 */
	private TTLChecker() {
		this.validateTTLChecker();
	}
}
