package com.kskyb.magent.util;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;


public class Test {
	public static void main(String[] args){
		try{
			Class.forName("com.informix.jdbc.IfxDriver");
			Connection conn = DriverManager.getConnection("");
			StringBuffer sb = new StringBuffer();
			sb.append("SELECT * FROM SYSTABLES");
			PreparedStatement ps = conn.prepareStatement(sb.toString());
			ps.executeUpdate();
			ResultSet rs = ps.executeQuery();
			while(rs.next()){
				System.out.println(rs.getString(1));
			}
			
		}catch(Exception e){
			e.printStackTrace();
		}
	}
}
