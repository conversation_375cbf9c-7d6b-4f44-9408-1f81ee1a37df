package com.kskyb.magent.util;

import java.util.LinkedList;

import com.kskyb.broker.event.DebugEchoEvent;

/**
 * <AUTHOR>
 * 
 * @param <T>
 */
public abstract class ReuseArrayBuffer<T> {
	/**
	 * 
	 */
	protected final LinkedList<T> arrays = new LinkedList();

	/**
	 * 
	 */
	private final Object push = new Object();

	/**
	 * 
	 */
	private final Object pop = new Object();

	/**
	 * @return
	 */
	public int size() {
		synchronized (this.arrays) {
			return this.arrays.size();
		}
	}

	/**
	 * 
	 */
	public void validation() {
		// synchronized (this.mod) {
		// if (this.sourceIndex == this.targetIndex && this.size != 0 && this.size != this.limit) {
		// StringBuffer buffer = null;
		// try {
		// buffer = new StringBuffer();
		// buffer.append("crash :");
		// buffer.append(",SI/");
		// buffer.append(String.valueOf(this.sourceIndex));
		// buffer.append(",TI/");
		// buffer.append(String.valueOf(this.targetIndex));
		// buffer.append(",SIZE/");
		// buffer.append(String.valueOf(this.size));
		// buffer.append(",LIMIT/");
		// buffer.append(String.valueOf(this.limit));
		//
		// throw new RuntimeException(buffer.toString());
		// }
		// finally {
		// buffer = null;
		// }
		// }
		// }
	}

	public boolean isEmpty() {
		if (DebugEchoEvent.isEnable()) {
			this.validation();
		}
		synchronized (this.arrays) {
			return this.arrays.size() == 0;
		}
	}

	/**
	 * @return
	 */
	public boolean isFull() {
		if (DebugEchoEvent.isEnable()) {
			this.validation();
		}
		synchronized (this.arrays) {
			return this.arrays.size() >= this.limit;
		}
	}

	/**
	 * @param timeout
	 */
	public final void waitOnPush(final long timeout) {
		synchronized (this.push) {
			try {
				this.push.wait(timeout);
			}
			catch (InterruptedException e) {
				//
			}
		}
	}

	/**
	 * 
	 */
	private void notifyPush() {
		synchronized (this.push) {
			this.push.notifyAll();
		}
	}

	/**
	 * @param timeout
	 */
	public final void waitOnPop(final long timeout) {
		synchronized (this.pop) {
			try {
				this.pop.wait(timeout);
			}
			catch (InterruptedException e) {
				//
			}
		}
	}

	/**
	 * 
	 */
	private void notifyPop() {
		synchronized (this.pop) {
			this.pop.notifyAll();
		}
	}

	protected int limit;

	/**
	 * @return the limit
	 */
	public final int getLimit() {
		return this.limit;
	}

	/**
	 * @param _limit
	 *            the limit to set
	 */
	public void setLimit(int _limit) {
		this.limit = _limit;
		this.notifyPop();
		this.notifyPush();
	}

	// private void initArray() {
	// final int targetArrayLimit = this.limit + ISOLATE_CNT;
	// if (this.arrayLimit > targetArrayLimit) {
	// return;
	// }
	//
	// this.arrayLimit = targetArrayLimit;
	//
	// T[] backup = this.arrays;
	// Class type = this.getBufferElementClass();
	// this.arrays = (T[]) java.lang.reflect.Array.newInstance(type,
	// this.arrayLimit);
	// // arrays = (T[]) new Object[limit];
	// int backupSize = 0;
	// if (backup != null) {
	// backupSize = this.arrayLimit > backup.length ? backup.length
	// : this.arrayLimit;
	// System.arraycopy( backup,
	// 0,
	// this.arrays,
	// 0,
	// backupSize);
	// }
	// }

	/**
	 * 
	 */
	public final void destroy() {
		this.arrays.clear();
	}

	// public abstract T popLog();
	public final T pop(final long timeout) {
		final long limit = System.currentTimeMillis() + timeout;
		while (limit > System.currentTimeMillis()) {
			synchronized (this.arrays) {
				if (this.arrays.size() > 0) {
					try {
						return this.arrays.removeFirst();
					}
					finally {
						this.notifyPop();
					}
				}
			}
			this.waitOnPush(500);
		}
		return null;
	}

	/**
	 * @return
	 */
	protected abstract long getFullWaitTime(T elm);

	/**
	 * @param elm
	 */
	protected final void push(T elm) {
		while (true) {
			synchronized (this.arrays) {
				if (this.arrays.size() < this.limit) {
					try {
						this.arrays.addLast(elm);
						return;
					}
					finally {
						this.notifyPush();
					}
				}
			}

			this.waitOnPop(this.getFullWaitTime(elm));
		}
	}

	/**
	 * @param elm
	 */
	public final void pushBack(T elm) {
		synchronized (this.arrays) {
			this.arrays.addFirst(elm);
			return;
		}
	}

	// protected T popTarget() {
	// if (this.arrays[this.targetIndex] == null) {
	// this.arrays[this.targetIndex] = this.getBufferElement();
	// }
	//
	// if (this.arrays[this.targetIndex].isLock()) {
	// this.arrays[this.targetIndex].rejectBuffer();
	// this.arrays[this.targetIndex] = this.getBufferElement();
	// }
	//
	// return this.arrays[this.targetIndex];
	// }
	//
	// protected void commitPopTarget() {
	// this.targetIndex++;
	// this.size++;
	//
	// if (this.targetIndex == this.arrayLimit) {
	// this.targetIndex = 0;
	// }
	// }
	//
	// protected abstract T getBufferElement();
	//
	// protected abstract Class getBufferElementClass();
}
