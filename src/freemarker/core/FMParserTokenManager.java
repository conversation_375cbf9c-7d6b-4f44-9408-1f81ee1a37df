/* Generated By:JavaCC: Do not edit this line. FMParserTokenManager.java */
package freemarker.core;

import java.io.IOException;
import java.util.StringTokenizer;

class FMParserTokenManager
							implements
							FMParserConstants {
	/**
	 * The noparseTag is set when we enter a block of text that the parser more or less ignores. These are <noparse> and <comment>. This variable
	 * tells us what the closing tag should be, and when we hit that, we resume parsing. Note that with this scheme, <comment> and <noparse> tags
	 * cannot nest recursively, but it is not clear how important that is.
	 */
	String noparseTag;

	/**
	 * Keeps track of how deeply nested we have the hash literals. This is necessary since we need to be able to distinguish the } used to close a
	 * hash literal and the one used to close a ${
	 */
	private int hashLiteralNesting;

	private int parenthesisNesting;

	private int bracketNesting;

	private boolean inFTLHeader;

	boolean strictEscapeSyntax, onlyTextOutput, altDirectiveSyntax, autodetectTagSyntax,
		directiveSyntaxEstablished, inInvocation;

	String templateName;

	// This method checks if we are in a strict mode where all
	// FreeMarker directives must start with <#

	private void strictSyntaxCheck(	Token tok,
									int newLexState) {
		if (this.onlyTextOutput) {
			tok.kind = PRINTABLE_CHARS;
			return;
		}
		char firstChar = tok.image.charAt(0);
		if (this.autodetectTagSyntax && !this.directiveSyntaxEstablished) {
			this.altDirectiveSyntax = (firstChar == '[');
		}
		if ((firstChar == '[' && !this.altDirectiveSyntax)
			|| (firstChar == '<' && this.altDirectiveSyntax)) {
			tok.kind = PRINTABLE_CHARS;
			return;
		}
		if (!this.strictEscapeSyntax) {
			this.SwitchTo(newLexState);
			return;
		}
		if (!this.altDirectiveSyntax) {
			if (!tok.image.startsWith("<#") && !tok.image.startsWith("</#")) {
				tok.kind = PRINTABLE_CHARS;
				return;
			}
		}
		this.directiveSyntaxEstablished = true;
		this.SwitchTo(newLexState);
	}

	private void unifiedCall(Token tok) {
		char firstChar = tok.image.charAt(0);
		if (this.autodetectTagSyntax && !this.directiveSyntaxEstablished) {
			this.altDirectiveSyntax = (firstChar == '[');
		}
		if (this.altDirectiveSyntax && firstChar == '<') {
			tok.kind = PRINTABLE_CHARS;
			return;
		}
		if (!this.altDirectiveSyntax && firstChar == '[') {
			tok.kind = PRINTABLE_CHARS;
			return;
		}
		this.directiveSyntaxEstablished = true;
		this.SwitchTo(NO_SPACE_EXPRESSION);
	}

	private void unifiedCallEnd(Token tok) {
		char firstChar = tok.image.charAt(0);
		if (this.altDirectiveSyntax && firstChar == '<') {
			tok.kind = PRINTABLE_CHARS;
			return;
		}
		if (!this.altDirectiveSyntax && firstChar == '[') {
			tok.kind = PRINTABLE_CHARS;
			return;
		}
	}

	private void closeBracket(Token tok) {
		if (this.bracketNesting > 0) {
			--this.bracketNesting;
		}
		else {
			tok.kind = DIRECTIVE_END;
			if (this.inFTLHeader) {
				this.eatNewline();
				this.inFTLHeader = false;
			}
			this.SwitchTo(DEFAULT);
		}
	}

	private void eatNewline() {
		int charsRead = 0;
		try {
			while (true) {
				char c = this.input_stream.readChar();
				++charsRead;
				if (!Character.isWhitespace(c)) {
					this.input_stream.backup(charsRead);
					return;
				}
				else if (c == '\r') {
					char next = this.input_stream.readChar();
					++charsRead;
					if (next != '\n') {
						this.input_stream.backup(1);
					}
					return;
				}
				else if (c == '\n') {
					return;
				}
			}
		}
		catch (IOException ioe) {
			this.input_stream.backup(charsRead);
		}
	}

	private void ftlHeader(Token matchedToken) {
		if (!this.directiveSyntaxEstablished) {
			this.altDirectiveSyntax = matchedToken.image.charAt(0) == '[';
			this.directiveSyntaxEstablished = true;
			this.autodetectTagSyntax = false;
		}
		String img = matchedToken.image;
		char firstChar = img.charAt(0);
		char lastChar = img.charAt(img.length() - 1);
		if ((firstChar == '[' && !this.altDirectiveSyntax)
			|| (firstChar == '<' && this.altDirectiveSyntax)) {
			matchedToken.kind = PRINTABLE_CHARS;
		}
		if (matchedToken.kind != PRINTABLE_CHARS) {
			if (lastChar != '>' && lastChar != ']') {
				this.SwitchTo(FM_EXPRESSION);
				this.inFTLHeader = true;
			}
			else {
				this.eatNewline();
			}
		}
	}

	public java.io.PrintStream debugStream = System.out;

	public void setDebugStream(java.io.PrintStream ds) {
		this.debugStream = ds;
	}

	private final int jjMoveStringLiteralDfa0_7() {
		return this.jjMoveNfa_7(0,
								0);
	}

	private final void jjCheckNAdd(int state) {
		if (this.jjrounds[state] != this.jjround) {
			this.jjstateSet[this.jjnewStateCnt++] = state;
			this.jjrounds[state] = this.jjround;
		}
	}

	private final void jjAddStates(	int start,
									int end) {
		do {
			this.jjstateSet[this.jjnewStateCnt++] = jjnextStates[start];
		} while (start++ != end);
	}

	private final void jjCheckNAddTwoStates(int state1,
											int state2) {
		this.jjCheckNAdd(state1);
		this.jjCheckNAdd(state2);
	}

	private final void jjCheckNAddStates(	int start,
											int end) {
		do {
			this.jjCheckNAdd(jjnextStates[start]);
		} while (start++ != end);
	}

	// private final void jjCheckNAddStates(int start) {
	// this.jjCheckNAdd(jjnextStates[start]);
	// this.jjCheckNAdd(jjnextStates[start + 1]);
	// }

	static final long[] jjbitVec0 = {
		0xfffffffffffffffeL,
		0xffffffffffffffffL,
		0xffffffffffffffffL,
		0xffffffffffffffffL
	};

	static final long[] jjbitVec2 = {
		0x0L,
		0x0L,
		0xffffffffffffffffL,
		0xffffffffffffffffL
	};

	private final int jjMoveNfa_7(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 13;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j, kind = 0x7fffffff;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 0:
							if ((0xefffdfffffffffffL & l) != 0L) {
								if (kind > 131) {
									kind = 131;
								}
								this.jjCheckNAdd(6);
							}
							else if ((0x1000200000000000L & l) != 0L) {
								if (kind > 132) {
									kind = 132;
								}
							}
							if (this.curChar == 45) {
								this.jjAddStates(	0,
													1);
							}
							else if (this.curChar == 60) {
								this.jjstateSet[this.jjnewStateCnt++] = 1;
							}
							break;
						case 1:
							if (this.curChar == 47) {
								this.jjCheckNAddTwoStates(	2,
															3);
							}
							break;
						case 2:
							if (this.curChar == 35) {
								this.jjCheckNAdd(3);
							}
							break;
						case 4:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	2,
													3);
							}
							break;
						case 5:
							if (this.curChar == 62 && kind > 130) {
								kind = 130;
							}
							break;
						case 6:
							if ((0xefffdfffffffffffL & l) == 0L) {
								break;
							}
							if (kind > 131) {
								kind = 131;
							}
							this.jjCheckNAdd(6);
							break;
						case 7:
							if ((0x1000200000000000L & l) != 0L && kind > 132) {
								kind = 132;
							}
							break;
						case 8:
							if (this.curChar == 45) {
								this.jjAddStates(	0,
													1);
							}
							break;
						case 9:
							if (this.curChar == 62 && kind > 129) {
								kind = 129;
							}
							break;
						case 10:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 9;
							}
							break;
						case 12:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 11;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 0:
							if ((0xfffffffff7ffffffL & l) != 0L) {
								if (kind > 131) {
									kind = 131;
								}
								this.jjCheckNAdd(6);
							}
							else if (this.curChar == 91) {
								if (kind > 132) {
									kind = 132;
								}
							}
							if (this.curChar == 91) {
								this.jjstateSet[this.jjnewStateCnt++] = 1;
							}
							break;
						case 3:
							if ((0x7fffffe07fffffeL & l) != 0L) {
								this.jjAddStates(	4,
													6);
							}
							break;
						case 5:
							if (this.curChar == 93 && kind > 130) {
								kind = 130;
							}
							break;
						case 6:
							if ((0xfffffffff7ffffffL & l) == 0L) {
								break;
							}
							if (kind > 131) {
								kind = 131;
							}
							this.jjCheckNAdd(6);
							break;
						case 7:
							if (this.curChar == 91 && kind > 132) {
								kind = 132;
							}
							break;
						case 11:
							if (this.curChar == 93 && kind > 129) {
								kind = 129;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 0:
						case 6:
							if (!jjCanMove_0(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 131) {
								kind = 131;
							}
							this.jjCheckNAdd(6);
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 13 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	private final int jjStopStringLiteralDfa_1(	int pos,
												long active0,
												long active1) {
		switch (pos) {
			case 0:
				if ((active1 & 0x180L) != 0L) {
					this.jjmatchedKind = 70;
					return -1;
				}
				return -1;
			default:
				return -1;
		}
	}

	private final int jjStartNfa_1(	int pos,
									long active0,
									long active1) {
		return this.jjMoveNfa_1(this.jjStopStringLiteralDfa_1(	pos,
																active0,
																active1),
								pos + 1);
	}

	private final int jjStopAtPos(	int pos,
									int kind) {
		this.jjmatchedKind = kind;
		this.jjmatchedPos = pos;
		return pos + 1;
	}

	// private final int jjStartNfaWithStates_1( int pos,
	// int kind,
	// int state) {
	// this.jjmatchedKind = kind;
	// this.jjmatchedPos = pos;
	// try {
	// this.curChar = this.input_stream.readChar();
	// }
	// catch (java.io.IOException e) {
	// return pos + 1;
	// }
	// return this.jjMoveNfa_1(state,
	// pos + 1);
	// }

	private final int jjMoveStringLiteralDfa0_1() {
		switch (this.curChar) {
			case 35:
				return this.jjMoveStringLiteralDfa1_1(0x100L);
			case 36:
				return this.jjMoveStringLiteralDfa1_1(0x80L);
			default:
				return this.jjMoveNfa_1(2,
										0);
		}
	}

	private final int jjMoveStringLiteralDfa1_1(long active1) {
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_1(	0,
											0L,
											active1);
			return 1;
		}
		switch (this.curChar) {
			case 123:
				if ((active1 & 0x80L) != 0L) {
					return this.jjStopAtPos(1,
											71);
				}
				else if ((active1 & 0x100L) != 0L) {
					return this.jjStopAtPos(1,
											72);
				}
				break;
			default:
				break;
		}
		return this.jjStartNfa_1(	0,
									0L,
									active1);
	}

	private final int jjMoveNfa_1(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 3;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j, kind = 0x7fffffff;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 2:
							if ((0xefffffe6ffffd9ffL & l) != 0L) {
								if (kind > 69) {
									kind = 69;
								}
								this.jjCheckNAdd(1);
							}
							else if ((0x100002600L & l) != 0L) {
								if (kind > 68) {
									kind = 68;
								}
								this.jjCheckNAdd(0);
							}
							else if ((0x1000001800000000L & l) != 0L) {
								if (kind > 70) {
									kind = 70;
								}
							}
							break;
						case 0:
							if ((0x100002600L & l) == 0L) {
								break;
							}
							kind = 68;
							this.jjCheckNAdd(0);
							break;
						case 1:
							if ((0xefffffe6ffffd9ffL & l) == 0L) {
								break;
							}
							kind = 69;
							this.jjCheckNAdd(1);
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 2:
							if ((0xf7fffffff7ffffffL & l) != 0L) {
								if (kind > 69) {
									kind = 69;
								}
								this.jjCheckNAdd(1);
							}
							else if ((0x800000008000000L & l) != 0L) {
								if (kind > 70) {
									kind = 70;
								}
							}
							break;
						case 1:
							if ((0xf7fffffff7ffffffL & l) == 0L) {
								break;
							}
							kind = 69;
							this.jjCheckNAdd(1);
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 2:
						case 1:
							if (!jjCanMove_0(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 69) {
								kind = 69;
							}
							this.jjCheckNAdd(1);
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 3 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	private final int jjStopStringLiteralDfa_0(	int pos,
												long active0,
												long active1) {
		switch (pos) {
			case 0:
				if ((active1 & 0x180L) != 0L) {
					this.jjmatchedKind = 70;
					return -1;
				}
				return -1;
			default:
				return -1;
		}
	}

	private final int jjStartNfa_0(	int pos,
									long active0,
									long active1) {
		return this.jjMoveNfa_0(this.jjStopStringLiteralDfa_0(	pos,
																active0,
																active1),
								pos + 1);
	}

	// private final int jjStartNfaWithStates_0( int pos,
	// int kind,
	// int state) {
	// this.jjmatchedKind = kind;
	// this.jjmatchedPos = pos;
	// try {
	// this.curChar = this.input_stream.readChar();
	// }
	// catch (java.io.IOException e) {
	// return pos + 1;
	// }
	// return this.jjMoveNfa_0(state,
	// pos + 1);
	// }

	private final int jjMoveStringLiteralDfa0_0() {
		switch (this.curChar) {
			case 35:
				return this.jjMoveStringLiteralDfa1_0(0x100L);
			case 36:
				return this.jjMoveStringLiteralDfa1_0(0x80L);
			default:
				return this.jjMoveNfa_0(2,
										0);
		}
	}

	private final int jjMoveStringLiteralDfa1_0(long active1) {
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_0(	0,
											0L,
											active1);
			return 1;
		}
		switch (this.curChar) {
			case 123:
				if ((active1 & 0x80L) != 0L) {
					return this.jjStopAtPos(1,
											71);
				}
				else if ((active1 & 0x100L) != 0L) {
					return this.jjStopAtPos(1,
											72);
				}
				break;
			default:
				break;
		}
		return this.jjStartNfa_0(	0,
									0L,
									active1);
	}

	static final long[] jjbitVec3 = {
		0x1ff00000fffffffeL,
		0xffffffffffffc000L,
		0xffffffffL,
		0x600000000000000L
	};

	static final long[] jjbitVec4 = {
		0x0L,
		0x0L,
		0x0L,
		0xff7fffffff7fffffL
	};

	static final long[] jjbitVec5 = {
		0x0L,
		0xffffffffffffffffL,
		0xffffffffffffffffL,
		0xffffffffffffffffL
	};

	static final long[] jjbitVec6 = {
		0xffffffffffffffffL,
		0xffffffffffffffffL,
		0xffffL,
		0x0L
	};

	static final long[] jjbitVec7 = {
		0xffffffffffffffffL,
		0xffffffffffffffffL,
		0x0L,
		0x0L
	};

	static final long[] jjbitVec8 = {
		0x3fffffffffffL,
		0x0L,
		0x0L,
		0x0L
	};

	private final int jjMoveNfa_0(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 567;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j, kind = 0x7fffffff;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 2:
							if ((0xefffffe6ffffd9ffL & l) != 0L) {
								if (kind > 69) {
									kind = 69;
								}
								this.jjCheckNAdd(1);
							}
							else if ((0x100002600L & l) != 0L) {
								if (kind > 68) {
									kind = 68;
								}
								this.jjCheckNAdd(0);
							}
							else if ((0x1000001800000000L & l) != 0L) {
								if (kind > 70) {
									kind = 70;
								}
							}
							if (this.curChar == 60) {
								this.jjAddStates(	7,
													8);
							}
							if (this.curChar == 60) {
								this.jjCheckNAddStates(	9,
														84);
							}
							if (this.curChar == 60) {
								this.jjCheckNAddStates(	85,
														125);
							}
							break;
						case 0:
							if ((0x100002600L & l) == 0L) {
								break;
							}
							if (kind > 68) {
								kind = 68;
							}
							this.jjCheckNAdd(0);
							break;
						case 1:
							if ((0xefffffe6ffffd9ffL & l) == 0L) {
								break;
							}
							if (kind > 69) {
								kind = 69;
							}
							this.jjCheckNAdd(1);
							break;
						case 3:
							if (this.curChar == 60) {
								this.jjCheckNAddStates(	85,
														125);
							}
							break;
						case 5:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	126,
													127);
							}
							break;
						case 6:
							if (this.curChar == 62 && kind > 6) {
								kind = 6;
							}
							break;
						case 14:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	128,
													129);
							}
							break;
						case 15:
							if (this.curChar == 62 && kind > 7) {
								kind = 7;
							}
							break;
						case 23:
							if ((0x100002600L & l) != 0L && kind > 8) {
								kind = 8;
							}
							break;
						case 26:
							if ((0x100002600L & l) != 0L && kind > 9) {
								kind = 9;
							}
							break;
						case 33:
							if ((0x100002600L & l) != 0L && kind > 10) {
								kind = 10;
							}
							break;
						case 38:
							if ((0x100002600L & l) != 0L && kind > 11) {
								kind = 11;
							}
							break;
						case 46:
							if ((0x100002600L & l) != 0L && kind > 12) {
								kind = 12;
							}
							break;
						case 53:
							if ((0x100002600L & l) != 0L && kind > 13) {
								kind = 13;
							}
							break;
						case 58:
							if ((0x100002600L & l) != 0L && kind > 14) {
								kind = 14;
							}
							break;
						case 65:
							if ((0x100002600L & l) != 0L && kind > 15) {
								kind = 15;
							}
							break;
						case 72:
							if ((0x100002600L & l) != 0L && kind > 16) {
								kind = 16;
							}
							break;
						case 78:
							if ((0x100002600L & l) != 0L && kind > 17) {
								kind = 17;
							}
							break;
						case 86:
							if ((0x100002600L & l) != 0L && kind > 18) {
								kind = 18;
							}
							break;
						case 93:
							if ((0x100002600L & l) != 0L && kind > 19) {
								kind = 19;
							}
							break;
						case 102:
							if ((0x100002600L & l) != 0L && kind > 20) {
								kind = 20;
							}
							break;
						case 108:
							if ((0x100002600L & l) != 0L && kind > 21) {
								kind = 21;
							}
							break;
						case 118:
							if ((0x100002600L & l) != 0L && kind > 22) {
								kind = 22;
							}
							break;
						case 124:
							if ((0x100002600L & l) != 0L && kind > 23) {
								kind = 23;
							}
							break;
						case 129:
							if ((0x100002600L & l) != 0L && kind > 24) {
								kind = 24;
							}
							break;
						case 136:
							if ((0x100002600L & l) != 0L && kind > 25) {
								kind = 25;
							}
							break;
						case 141:
							if ((0x100002600L & l) != 0L && kind > 26) {
								kind = 26;
							}
							break;
						case 149:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	130,
													131);
							}
							break;
						case 150:
							if (this.curChar == 62 && kind > 27) {
								kind = 27;
							}
							break;
						case 159:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	132,
													133);
							}
							break;
						case 160:
							if (this.curChar == 62 && kind > 28) {
								kind = 28;
							}
							break;
						case 168:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	134,
													135);
							}
							break;
						case 169:
							if (this.curChar == 62 && kind > 30) {
								kind = 30;
							}
							break;
						case 177:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	136,
														138);
							}
							break;
						case 178:
							if (this.curChar == 47) {
								this.jjCheckNAdd(179);
							}
							break;
						case 179:
							if (this.curChar == 62 && kind > 44) {
								kind = 44;
							}
							break;
						case 184:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	139,
														141);
							}
							break;
						case 185:
							if (this.curChar == 47) {
								this.jjCheckNAdd(186);
							}
							break;
						case 186:
							if (this.curChar == 62 && kind > 45) {
								kind = 45;
							}
							break;
						case 192:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	142,
														144);
							}
							break;
						case 193:
							if (this.curChar == 47) {
								this.jjCheckNAdd(194);
							}
							break;
						case 194:
							if (this.curChar == 62 && kind > 46) {
								kind = 46;
							}
							break;
						case 201:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	145,
														147);
							}
							break;
						case 202:
							if (this.curChar == 47) {
								this.jjCheckNAdd(203);
							}
							break;
						case 203:
							if (this.curChar == 62 && kind > 47) {
								kind = 47;
							}
							break;
						case 208:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	148,
														150);
							}
							break;
						case 209:
							if (this.curChar == 47) {
								this.jjCheckNAdd(210);
							}
							break;
						case 210:
							if (this.curChar == 62 && kind > 48) {
								kind = 48;
							}
							break;
						case 216:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	151,
														153);
							}
							break;
						case 217:
							if (this.curChar == 47) {
								this.jjCheckNAdd(218);
							}
							break;
						case 218:
							if (this.curChar == 62 && kind > 49) {
								kind = 49;
							}
							break;
						case 220:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	154,
														156);
							}
							break;
						case 221:
							if (this.curChar == 47) {
								this.jjCheckNAdd(222);
							}
							break;
						case 222:
							if (this.curChar == 62 && kind > 50) {
								kind = 50;
							}
							break;
						case 225:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	157,
														159);
							}
							break;
						case 226:
							if (this.curChar == 47) {
								this.jjCheckNAdd(227);
							}
							break;
						case 227:
							if (this.curChar == 62 && kind > 51) {
								kind = 51;
							}
							break;
						case 230:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	160,
														162);
							}
							break;
						case 231:
							if (this.curChar == 47) {
								this.jjCheckNAdd(232);
							}
							break;
						case 232:
							if (this.curChar == 62 && kind > 52) {
								kind = 52;
							}
							break;
						case 235:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	163,
													164);
							}
							break;
						case 236:
							if (this.curChar == 62 && kind > 53) {
								kind = 53;
							}
							break;
						case 244:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	165,
														167);
							}
							break;
						case 245:
							if (this.curChar == 47) {
								this.jjCheckNAdd(246);
							}
							break;
						case 246:
							if (this.curChar == 62 && kind > 54) {
								kind = 54;
							}
							break;
						case 253:
							if ((0x100002600L & l) != 0L && kind > 55) {
								kind = 55;
							}
							break;
						case 260:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	168,
														170);
							}
							break;
						case 261:
							if (this.curChar == 47) {
								this.jjCheckNAdd(262);
							}
							break;
						case 262:
							if (this.curChar == 62 && kind > 56) {
								kind = 56;
							}
							break;
						case 270:
							if ((0x100002600L & l) != 0L && kind > 57) {
								kind = 57;
							}
							break;
						case 278:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddStates(	171,
														173);
							}
							break;
						case 279:
							if (this.curChar == 47) {
								this.jjCheckNAdd(280);
							}
							break;
						case 280:
							if (this.curChar == 62 && kind > 58) {
								kind = 58;
							}
							break;
						case 289:
							if ((0x100002600L & l) != 0L && kind > 59) {
								kind = 59;
							}
							break;
						case 296:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	174,
													175);
							}
							break;
						case 297:
							if (this.curChar == 62 && kind > 61) {
								kind = 61;
							}
							break;
						case 305:
							if (this.curChar == 60) {
								this.jjCheckNAddStates(	9,
														84);
							}
							break;
						case 306:
							if (this.curChar == 35) {
								this.jjCheckNAdd(12);
							}
							break;
						case 307:
							if (this.curChar == 35) {
								this.jjCheckNAdd(21);
							}
							break;
						case 308:
							if (this.curChar == 35) {
								this.jjCheckNAdd(24);
							}
							break;
						case 309:
							if (this.curChar == 35) {
								this.jjCheckNAdd(31);
							}
							break;
						case 310:
							if (this.curChar == 35) {
								this.jjCheckNAdd(36);
							}
							break;
						case 311:
							if (this.curChar == 35) {
								this.jjCheckNAdd(44);
							}
							break;
						case 312:
							if (this.curChar == 35) {
								this.jjCheckNAdd(51);
							}
							break;
						case 313:
							if (this.curChar == 35) {
								this.jjCheckNAdd(56);
							}
							break;
						case 314:
							if (this.curChar == 35) {
								this.jjCheckNAdd(63);
							}
							break;
						case 315:
							if (this.curChar == 35) {
								this.jjCheckNAdd(70);
							}
							break;
						case 316:
							if (this.curChar == 35) {
								this.jjCheckNAdd(76);
							}
							break;
						case 317:
							if (this.curChar == 35) {
								this.jjCheckNAdd(84);
							}
							break;
						case 318:
							if (this.curChar == 35) {
								this.jjCheckNAdd(91);
							}
							break;
						case 319:
							if (this.curChar == 35) {
								this.jjCheckNAdd(100);
							}
							break;
						case 320:
							if (this.curChar == 35) {
								this.jjCheckNAdd(106);
							}
							break;
						case 321:
							if (this.curChar == 35) {
								this.jjCheckNAdd(116);
							}
							break;
						case 322:
							if (this.curChar == 35) {
								this.jjCheckNAdd(122);
							}
							break;
						case 323:
							if (this.curChar == 35) {
								this.jjCheckNAdd(127);
							}
							break;
						case 324:
							if (this.curChar == 35) {
								this.jjCheckNAdd(134);
							}
							break;
						case 325:
							if (this.curChar == 35) {
								this.jjCheckNAdd(139);
							}
							break;
						case 326:
							if (this.curChar == 35) {
								this.jjCheckNAdd(147);
							}
							break;
						case 327:
							if (this.curChar == 35) {
								this.jjCheckNAdd(157);
							}
							break;
						case 328:
							if (this.curChar == 35) {
								this.jjCheckNAdd(166);
							}
							break;
						case 329:
							if (this.curChar == 35) {
								this.jjCheckNAdd(175);
							}
							break;
						case 330:
							if (this.curChar == 47) {
								this.jjCheckNAdd(334);
							}
							break;
						case 332:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	176,
													177);
							}
							break;
						case 333:
							if (this.curChar == 62 && kind > 31) {
								kind = 31;
							}
							break;
						case 335:
							if (this.curChar == 35) {
								this.jjCheckNAdd(334);
							}
							break;
						case 336:
						case 532:
							if (this.curChar == 47) {
								this.jjCheckNAdd(335);
							}
							break;
						case 337:
							if (this.curChar == 47) {
								this.jjCheckNAdd(343);
							}
							break;
						case 339:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	178,
													179);
							}
							break;
						case 340:
							if (this.curChar == 62 && kind > 32) {
								kind = 32;
							}
							break;
						case 344:
							if (this.curChar == 35) {
								this.jjCheckNAdd(343);
							}
							break;
						case 345:
						case 533:
							if (this.curChar == 47) {
								this.jjCheckNAdd(344);
							}
							break;
						case 346:
							if (this.curChar == 47) {
								this.jjCheckNAdd(355);
							}
							break;
						case 348:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	180,
													181);
							}
							break;
						case 349:
							if (this.curChar == 62 && kind > 33) {
								kind = 33;
							}
							break;
						case 356:
							if (this.curChar == 35) {
								this.jjCheckNAdd(355);
							}
							break;
						case 357:
						case 534:
							if (this.curChar == 47) {
								this.jjCheckNAdd(356);
							}
							break;
						case 358:
							if (this.curChar == 47) {
								this.jjCheckNAdd(367);
							}
							break;
						case 360:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	182,
													183);
							}
							break;
						case 361:
							if (this.curChar == 62 && kind > 34) {
								kind = 34;
							}
							break;
						case 368:
							if (this.curChar == 35) {
								this.jjCheckNAdd(367);
							}
							break;
						case 369:
						case 535:
							if (this.curChar == 47) {
								this.jjCheckNAdd(368);
							}
							break;
						case 370:
							if (this.curChar == 47) {
								this.jjCheckNAdd(379);
							}
							break;
						case 372:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	184,
													185);
							}
							break;
						case 373:
							if (this.curChar == 62 && kind > 35) {
								kind = 35;
							}
							break;
						case 380:
							if (this.curChar == 35) {
								this.jjCheckNAdd(379);
							}
							break;
						case 381:
						case 536:
							if (this.curChar == 47) {
								this.jjCheckNAdd(380);
							}
							break;
						case 382:
							if (this.curChar == 47) {
								this.jjCheckNAdd(389);
							}
							break;
						case 384:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	186,
													187);
							}
							break;
						case 385:
							if (this.curChar == 62 && kind > 36) {
								kind = 36;
							}
							break;
						case 390:
							if (this.curChar == 35) {
								this.jjCheckNAdd(389);
							}
							break;
						case 391:
						case 537:
							if (this.curChar == 47) {
								this.jjCheckNAdd(390);
							}
							break;
						case 392:
							if (this.curChar == 47) {
								this.jjCheckNAdd(400);
							}
							break;
						case 394:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	188,
													189);
							}
							break;
						case 395:
							if (this.curChar == 62 && kind > 37) {
								kind = 37;
							}
							break;
						case 401:
							if (this.curChar == 35) {
								this.jjCheckNAdd(400);
							}
							break;
						case 402:
						case 538:
							if (this.curChar == 47) {
								this.jjCheckNAdd(401);
							}
							break;
						case 403:
							if (this.curChar == 47) {
								this.jjCheckNAdd(411);
							}
							break;
						case 405:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	190,
													191);
							}
							break;
						case 406:
							if (this.curChar == 62 && kind > 38) {
								kind = 38;
							}
							break;
						case 412:
							if (this.curChar == 35) {
								this.jjCheckNAdd(411);
							}
							break;
						case 413:
						case 539:
							if (this.curChar == 47) {
								this.jjCheckNAdd(412);
							}
							break;
						case 414:
							if (this.curChar == 47) {
								this.jjCheckNAdd(424);
							}
							break;
						case 416:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	192,
													193);
							}
							break;
						case 417:
							if (this.curChar == 62 && kind > 39) {
								kind = 39;
							}
							break;
						case 425:
							if (this.curChar == 35) {
								this.jjCheckNAdd(424);
							}
							break;
						case 426:
						case 540:
							if (this.curChar == 47) {
								this.jjCheckNAdd(425);
							}
							break;
						case 427:
							if (this.curChar == 47) {
								this.jjCheckNAdd(434);
							}
							break;
						case 429:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	194,
													195);
							}
							break;
						case 430:
							if (this.curChar == 62 && kind > 40) {
								kind = 40;
							}
							break;
						case 435:
							if (this.curChar == 35) {
								this.jjCheckNAdd(434);
							}
							break;
						case 436:
						case 541:
							if (this.curChar == 47) {
								this.jjCheckNAdd(435);
							}
							break;
						case 437:
							if (this.curChar == 47) {
								this.jjCheckNAdd(447);
							}
							break;
						case 439:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	196,
													197);
							}
							break;
						case 440:
							if (this.curChar == 62 && kind > 41) {
								kind = 41;
							}
							break;
						case 448:
							if (this.curChar == 35) {
								this.jjCheckNAdd(447);
							}
							break;
						case 449:
						case 542:
							if (this.curChar == 47) {
								this.jjCheckNAdd(448);
							}
							break;
						case 450:
							if (this.curChar == 47) {
								this.jjCheckNAdd(461);
							}
							break;
						case 452:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	198,
													199);
							}
							break;
						case 453:
							if (this.curChar == 62 && kind > 42) {
								kind = 42;
							}
							break;
						case 462:
							if (this.curChar == 35) {
								this.jjCheckNAdd(461);
							}
							break;
						case 463:
						case 543:
							if (this.curChar == 47) {
								this.jjCheckNAdd(462);
							}
							break;
						case 464:
							if (this.curChar == 47) {
								this.jjCheckNAdd(472);
							}
							break;
						case 466:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	200,
													201);
							}
							break;
						case 467:
							if (this.curChar == 62 && kind > 43) {
								kind = 43;
							}
							break;
						case 473:
							if (this.curChar == 35) {
								this.jjCheckNAdd(472);
							}
							break;
						case 474:
						case 544:
							if (this.curChar == 47) {
								this.jjCheckNAdd(473);
							}
							break;
						case 475:
							if (this.curChar == 35) {
								this.jjCheckNAdd(182);
							}
							break;
						case 476:
							if (this.curChar == 35) {
								this.jjCheckNAdd(190);
							}
							break;
						case 477:
							if (this.curChar == 35) {
								this.jjCheckNAdd(199);
							}
							break;
						case 478:
							if (this.curChar == 35) {
								this.jjCheckNAdd(206);
							}
							break;
						case 479:
							if (this.curChar == 35) {
								this.jjCheckNAdd(214);
							}
							break;
						case 480:
							if (this.curChar == 35) {
								this.jjCheckNAdd(215);
							}
							break;
						case 481:
							if (this.curChar == 35) {
								this.jjCheckNAdd(223);
							}
							break;
						case 482:
							if (this.curChar == 35) {
								this.jjCheckNAdd(228);
							}
							break;
						case 483:
							if (this.curChar == 35) {
								this.jjCheckNAdd(233);
							}
							break;
						case 484:
							if (this.curChar == 35) {
								this.jjCheckNAdd(242);
							}
							break;
						case 485:
							if (this.curChar == 35) {
								this.jjCheckNAdd(251);
							}
							break;
						case 486:
							if (this.curChar == 35) {
								this.jjCheckNAdd(258);
							}
							break;
						case 487:
							if (this.curChar == 35) {
								this.jjCheckNAdd(268);
							}
							break;
						case 488:
							if (this.curChar == 35) {
								this.jjCheckNAdd(276);
							}
							break;
						case 489:
							if (this.curChar == 35) {
								this.jjCheckNAdd(287);
							}
							break;
						case 490:
							if (this.curChar == 35) {
								this.jjCheckNAdd(294);
							}
							break;
						case 491:
							if (this.curChar == 47) {
								this.jjCheckNAdd(499);
							}
							break;
						case 493:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	202,
													203);
							}
							break;
						case 494:
							if (this.curChar == 62 && kind > 60) {
								kind = 60;
							}
							break;
						case 500:
							if (this.curChar == 35) {
								this.jjCheckNAdd(499);
							}
							break;
						case 501:
						case 545:
							if (this.curChar == 47) {
								this.jjCheckNAdd(500);
							}
							break;
						case 502:
							if (this.curChar == 35) {
								this.jjCheckNAdd(304);
							}
							break;
						case 503:
							if (this.curChar == 47) {
								this.jjCheckNAdd(513);
							}
							break;
						case 505:
							if ((0x100002600L & l) != 0L) {
								this.jjAddStates(	204,
													205);
							}
							break;
						case 506:
							if (this.curChar == 62 && kind > 62) {
								kind = 62;
							}
							break;
						case 514:
							if (this.curChar == 35) {
								this.jjCheckNAdd(513);
							}
							break;
						case 515:
						case 546:
							if (this.curChar == 47) {
								this.jjCheckNAdd(514);
							}
							break;
						case 518:
							if ((0x100002600L & l) != 0L && kind > 65) {
								kind = 65;
							}
							break;
						case 521:
							if (this.curChar == 35) {
								this.jjstateSet[this.jjnewStateCnt++] = 520;
							}
							break;
						case 523:
							if (this.curChar == 47) {
								this.jjstateSet[this.jjnewStateCnt++] = 524;
							}
							break;
						case 524:
							if (this.curChar == 62 && kind > 66) {
								kind = 66;
							}
							break;
						case 527:
							if (this.curChar == 35) {
								this.jjstateSet[this.jjnewStateCnt++] = 526;
							}
							break;
						case 528:
							if (this.curChar == 35) {
								this.jjstateSet[this.jjnewStateCnt++] = 529;
							}
							break;
						case 530:
						case 553:
							if (this.curChar == 47) {
								this.jjCheckNAdd(528);
							}
							break;
						case 549:
							if (this.curChar == 35) {
								this.jjstateSet[this.jjnewStateCnt++] = 548;
							}
							break;
						case 552:
							if (this.curChar == 35) {
								this.jjstateSet[this.jjnewStateCnt++] = 551;
							}
							break;
						case 554:
							if (this.curChar == 60) {
								this.jjAddStates(	7,
													8);
							}
							break;
						case 555:
							if (this.curChar == 45 && kind > 29) {
								kind = 29;
							}
							break;
						case 556:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 555;
							}
							break;
						case 557:
							if (this.curChar == 35) {
								this.jjstateSet[this.jjnewStateCnt++] = 556;
							}
							break;
						case 559:
							if (this.curChar == 36) {
								this.jjCheckNAddStates(	206,
														209);
							}
							break;
						case 560:
							if ((0x3ff001000000000L & l) != 0L) {
								this.jjCheckNAddStates(	206,
														209);
							}
							break;
						case 561:
							if (this.curChar == 46) {
								this.jjstateSet[this.jjnewStateCnt++] = 562;
							}
							break;
						case 562:
							if (this.curChar == 36) {
								this.jjCheckNAddStates(	210,
														213);
							}
							break;
						case 563:
							if ((0x3ff001000000000L & l) != 0L) {
								this.jjCheckNAddStates(	210,
														213);
							}
							break;
						case 564:
							if ((0x100002600L & l) != 0L) {
								this.jjCheckNAddTwoStates(	564,
															565);
							}
							break;
						case 565:
							if (this.curChar == 62 && kind > 64) {
								kind = 64;
							}
							break;
						case 566:
							if (this.curChar == 47) {
								this.jjstateSet[this.jjnewStateCnt++] = 558;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 2:
							if ((0xf7fffffff7ffffffL & l) != 0L) {
								if (kind > 69) {
									kind = 69;
								}
								this.jjCheckNAdd(1);
							}
							else if ((0x800000008000000L & l) != 0L) {
								if (kind > 70) {
									kind = 70;
								}
							}
							if (this.curChar == 91) {
								this.jjAddStates(	7,
													8);
							}
							if (this.curChar == 91) {
								this.jjAddStates(	214,
													274);
							}
							break;
						case 1:
							if ((0xf7fffffff7ffffffL & l) == 0L) {
								break;
							}
							if (kind > 69) {
								kind = 69;
							}
							this.jjCheckNAdd(1);
							break;
						case 4:
							if (this.curChar == 116) {
								this.jjAddStates(	126,
													127);
							}
							break;
						case 6:
							if (this.curChar == 93 && kind > 6) {
								kind = 6;
							}
							break;
						case 7:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 4;
							}
							break;
						case 8:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 7;
							}
							break;
						case 9:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 8;
							}
							break;
						case 10:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 9;
							}
							break;
						case 11:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 10;
							}
							break;
						case 12:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 11;
							}
							break;
						case 13:
							if (this.curChar == 114) {
								this.jjAddStates(	128,
													129);
							}
							break;
						case 15:
							if (this.curChar == 93 && kind > 7) {
								kind = 7;
							}
							break;
						case 16:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 13;
							}
							break;
						case 17:
							if (this.curChar == 118) {
								this.jjstateSet[this.jjnewStateCnt++] = 16;
							}
							break;
						case 18:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 17;
							}
							break;
						case 19:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 18;
							}
							break;
						case 20:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 19;
							}
							break;
						case 21:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 20;
							}
							break;
						case 22:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 23;
							}
							break;
						case 24:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 22;
							}
							break;
						case 25:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 26;
							}
							break;
						case 27:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 25;
							}
							break;
						case 28:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 27;
							}
							break;
						case 29:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 28;
							}
							break;
						case 30:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 29;
							}
							break;
						case 31:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 30;
							}
							break;
						case 32:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 33;
							}
							break;
						case 34:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 32;
							}
							break;
						case 35:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 34;
							}
							break;
						case 36:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 35;
							}
							break;
						case 37:
							if (this.curChar == 104) {
								this.jjstateSet[this.jjnewStateCnt++] = 38;
							}
							break;
						case 39:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 37;
							}
							break;
						case 40:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 39;
							}
							break;
						case 41:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 40;
							}
							break;
						case 42:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 41;
							}
							break;
						case 43:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 42;
							}
							break;
						case 44:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 43;
							}
							break;
						case 45:
							if (this.curChar == 104) {
								this.jjstateSet[this.jjnewStateCnt++] = 46;
							}
							break;
						case 47:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 45;
							}
							break;
						case 48:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 47;
							}
							break;
						case 49:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 48;
							}
							break;
						case 50:
							if (this.curChar == 119) {
								this.jjstateSet[this.jjnewStateCnt++] = 49;
							}
							break;
						case 51:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 50;
							}
							break;
						case 52:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 53;
							}
							break;
						case 54:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 52;
							}
							break;
						case 55:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 54;
							}
							break;
						case 56:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 55;
							}
							break;
						case 57:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 58;
							}
							break;
						case 59:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 57;
							}
							break;
						case 60:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 59;
							}
							break;
						case 61:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 60;
							}
							break;
						case 62:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 61;
							}
							break;
						case 63:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 62;
							}
							break;
						case 64:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 65;
							}
							break;
						case 66:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 64;
							}
							break;
						case 67:
							if (this.curChar == 98) {
								this.jjstateSet[this.jjnewStateCnt++] = 66;
							}
							break;
						case 68:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 67;
							}
							break;
						case 69:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 68;
							}
							break;
						case 70:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 69;
							}
							break;
						case 71:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 72;
							}
							break;
						case 73:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 71;
							}
							break;
						case 74:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 73;
							}
							break;
						case 75:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 74;
							}
							break;
						case 76:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 75;
							}
							break;
						case 77:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 78;
							}
							break;
						case 79:
							if (this.curChar == 100) {
								this.jjstateSet[this.jjnewStateCnt++] = 77;
							}
							break;
						case 80:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 79;
							}
							break;
						case 81:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 80;
							}
							break;
						case 82:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 81;
							}
							break;
						case 83:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 82;
							}
							break;
						case 84:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 83;
							}
							break;
						case 85:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 86;
							}
							break;
						case 87:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 85;
							}
							break;
						case 88:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 87;
							}
							break;
						case 89:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 88;
							}
							break;
						case 90:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 89;
							}
							break;
						case 91:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 90;
							}
							break;
						case 92:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 93;
							}
							break;
						case 94:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 92;
							}
							break;
						case 95:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 94;
							}
							break;
						case 96:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 95;
							}
							break;
						case 97:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 96;
							}
							break;
						case 98:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 97;
							}
							break;
						case 99:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 98;
							}
							break;
						case 100:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 99;
							}
							break;
						case 101:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 102;
							}
							break;
						case 103:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 101;
							}
							break;
						case 104:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 103;
							}
							break;
						case 105:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 104;
							}
							break;
						case 106:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 105;
							}
							break;
						case 107:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 108;
							}
							break;
						case 109:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 107;
							}
							break;
						case 110:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 109;
							}
							break;
						case 111:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 110;
							}
							break;
						case 112:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 111;
							}
							break;
						case 113:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 112;
							}
							break;
						case 114:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 113;
							}
							break;
						case 115:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 114;
							}
							break;
						case 116:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 115;
							}
							break;
						case 117:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 118;
							}
							break;
						case 119:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 117;
							}
							break;
						case 120:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 119;
							}
							break;
						case 121:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 120;
							}
							break;
						case 122:
							if (this.curChar == 118) {
								this.jjstateSet[this.jjnewStateCnt++] = 121;
							}
							break;
						case 123:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 124;
							}
							break;
						case 125:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 123;
							}
							break;
						case 126:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 125;
							}
							break;
						case 127:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 126;
							}
							break;
						case 128:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 129;
							}
							break;
						case 130:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 128;
							}
							break;
						case 131:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 130;
							}
							break;
						case 132:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 131;
							}
							break;
						case 133:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 132;
							}
							break;
						case 134:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 133;
							}
							break;
						case 135:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 136;
							}
							break;
						case 137:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 135;
							}
							break;
						case 138:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 137;
							}
							break;
						case 139:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 138;
							}
							break;
						case 140:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 141;
							}
							break;
						case 142:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 140;
							}
							break;
						case 143:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 142;
							}
							break;
						case 144:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 143;
							}
							break;
						case 145:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 144;
							}
							break;
						case 146:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 145;
							}
							break;
						case 147:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 146;
							}
							break;
						case 148:
							if (this.curChar == 115) {
								this.jjAddStates(	130,
													131);
							}
							break;
						case 150:
							if (this.curChar == 93 && kind > 27) {
								kind = 27;
							}
							break;
						case 151:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 148;
							}
							break;
						case 152:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 151;
							}
							break;
						case 153:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 152;
							}
							break;
						case 154:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 153;
							}
							break;
						case 155:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 154;
							}
							break;
						case 156:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 155;
							}
							break;
						case 157:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 156;
							}
							break;
						case 158:
							if (this.curChar == 116) {
								this.jjAddStates(	132,
													133);
							}
							break;
						case 160:
							if (this.curChar == 93 && kind > 28) {
								kind = 28;
							}
							break;
						case 161:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 158;
							}
							break;
						case 162:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 161;
							}
							break;
						case 163:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 162;
							}
							break;
						case 164:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 163;
							}
							break;
						case 165:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 164;
							}
							break;
						case 166:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 165;
							}
							break;
						case 167:
							if (this.curChar == 101) {
								this.jjAddStates(	134,
													135);
							}
							break;
						case 169:
							if (this.curChar == 93 && kind > 30) {
								kind = 30;
							}
							break;
						case 170:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 167;
							}
							break;
						case 171:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 170;
							}
							break;
						case 172:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 171;
							}
							break;
						case 173:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 172;
							}
							break;
						case 174:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 173;
							}
							break;
						case 175:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 174;
							}
							break;
						case 176:
							if (this.curChar == 101) {
								this.jjAddStates(	136,
													138);
							}
							break;
						case 179:
							if (this.curChar == 93 && kind > 44) {
								kind = 44;
							}
							break;
						case 180:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 176;
							}
							break;
						case 181:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 180;
							}
							break;
						case 182:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 181;
							}
							break;
						case 183:
							if (this.curChar == 107) {
								this.jjAddStates(	139,
													141);
							}
							break;
						case 186:
							if (this.curChar == 93 && kind > 45) {
								kind = 45;
							}
							break;
						case 187:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 183;
							}
							break;
						case 188:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 187;
							}
							break;
						case 189:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 188;
							}
							break;
						case 190:
							if (this.curChar == 98) {
								this.jjstateSet[this.jjnewStateCnt++] = 189;
							}
							break;
						case 191:
							if (this.curChar == 110) {
								this.jjAddStates(	142,
													144);
							}
							break;
						case 194:
							if (this.curChar == 93 && kind > 46) {
								kind = 46;
							}
							break;
						case 195:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 191;
							}
							break;
						case 196:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 195;
							}
							break;
						case 197:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 196;
							}
							break;
						case 198:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 197;
							}
							break;
						case 199:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 198;
							}
							break;
						case 200:
							if (this.curChar == 112) {
								this.jjAddStates(	145,
													147);
							}
							break;
						case 203:
							if (this.curChar == 93 && kind > 47) {
								kind = 47;
							}
							break;
						case 204:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 200;
							}
							break;
						case 205:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 204;
							}
							break;
						case 206:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 205;
							}
							break;
						case 207:
							if (this.curChar == 104) {
								this.jjAddStates(	148,
													150);
							}
							break;
						case 210:
							if (this.curChar == 93 && kind > 48) {
								kind = 48;
							}
							break;
						case 211:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 207;
							}
							break;
						case 212:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 211;
							}
							break;
						case 213:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 212;
							}
							break;
						case 214:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 213;
							}
							break;
						case 215:
							if (this.curChar == 116) {
								this.jjAddStates(	151,
													153);
							}
							break;
						case 218:
							if (this.curChar == 93 && kind > 49) {
								kind = 49;
							}
							break;
						case 219:
							if (this.curChar == 116) {
								this.jjAddStates(	154,
													156);
							}
							break;
						case 222:
							if (this.curChar == 93 && kind > 50) {
								kind = 50;
							}
							break;
						case 223:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 219;
							}
							break;
						case 224:
							if (this.curChar == 116) {
								this.jjAddStates(	157,
													159);
							}
							break;
						case 227:
							if (this.curChar == 93 && kind > 51) {
								kind = 51;
							}
							break;
						case 228:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 224;
							}
							break;
						case 229:
							if (this.curChar == 116) {
								this.jjAddStates(	160,
													162);
							}
							break;
						case 232:
							if (this.curChar == 93 && kind > 52) {
								kind = 52;
							}
							break;
						case 233:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 229;
							}
							break;
						case 234:
							if (this.curChar == 116) {
								this.jjAddStates(	163,
													164);
							}
							break;
						case 236:
							if (this.curChar == 93 && kind > 53) {
								kind = 53;
							}
							break;
						case 237:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 234;
							}
							break;
						case 238:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 237;
							}
							break;
						case 239:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 238;
							}
							break;
						case 240:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 239;
							}
							break;
						case 241:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 240;
							}
							break;
						case 242:
							if (this.curChar == 100) {
								this.jjstateSet[this.jjnewStateCnt++] = 241;
							}
							break;
						case 243:
							if (this.curChar == 100) {
								this.jjAddStates(	165,
													167);
							}
							break;
						case 246:
							if (this.curChar == 93 && kind > 54) {
								kind = 54;
							}
							break;
						case 247:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 243;
							}
							break;
						case 248:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 247;
							}
							break;
						case 249:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 248;
							}
							break;
						case 250:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 249;
							}
							break;
						case 251:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 250;
							}
							break;
						case 252:
							if (this.curChar == 100) {
								this.jjstateSet[this.jjnewStateCnt++] = 253;
							}
							break;
						case 254:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 252;
							}
							break;
						case 255:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 254;
							}
							break;
						case 256:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 255;
							}
							break;
						case 257:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 256;
							}
							break;
						case 258:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 257;
							}
							break;
						case 259:
							if (this.curChar == 101) {
								this.jjAddStates(	168,
													170);
							}
							break;
						case 262:
							if (this.curChar == 93 && kind > 56) {
								kind = 56;
							}
							break;
						case 263:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 259;
							}
							break;
						case 264:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 263;
							}
							break;
						case 265:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 264;
							}
							break;
						case 266:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 265;
							}
							break;
						case 267:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 266;
							}
							break;
						case 268:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 267;
							}
							break;
						case 269:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 270;
							}
							break;
						case 271:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 269;
							}
							break;
						case 272:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 271;
							}
							break;
						case 273:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 272;
							}
							break;
						case 274:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 273;
							}
							break;
						case 275:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 274;
							}
							break;
						case 276:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 275;
							}
							break;
						case 277:
							if (this.curChar == 107) {
								this.jjAddStates(	171,
													173);
							}
							break;
						case 280:
							if (this.curChar == 93 && kind > 58) {
								kind = 58;
							}
							break;
						case 281:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 277;
							}
							break;
						case 282:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 281;
							}
							break;
						case 283:
							if (this.curChar == 98) {
								this.jjstateSet[this.jjnewStateCnt++] = 282;
							}
							break;
						case 284:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 283;
							}
							break;
						case 285:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 284;
							}
							break;
						case 286:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 285;
							}
							break;
						case 287:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 286;
							}
							break;
						case 288:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 289;
							}
							break;
						case 290:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 288;
							}
							break;
						case 291:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 290;
							}
							break;
						case 292:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 291;
							}
							break;
						case 293:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 292;
							}
							break;
						case 294:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 293;
							}
							break;
						case 295:
							if (this.curChar == 101) {
								this.jjAddStates(	174,
													175);
							}
							break;
						case 297:
							if (this.curChar == 93 && kind > 61) {
								kind = 61;
							}
							break;
						case 298:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 295;
							}
							break;
						case 299:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 298;
							}
							break;
						case 300:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 299;
							}
							break;
						case 301:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 300;
							}
							break;
						case 302:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 301;
							}
							break;
						case 303:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 302;
							}
							break;
						case 304:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 303;
							}
							break;
						case 331:
							if (this.curChar == 102) {
								this.jjAddStates(	176,
													177);
							}
							break;
						case 333:
							if (this.curChar == 93 && kind > 31) {
								kind = 31;
							}
							break;
						case 334:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 331;
							}
							break;
						case 338:
							if (this.curChar == 116) {
								this.jjAddStates(	178,
													179);
							}
							break;
						case 340:
							if (this.curChar == 93 && kind > 32) {
								kind = 32;
							}
							break;
						case 341:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 338;
							}
							break;
						case 342:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 341;
							}
							break;
						case 343:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 342;
							}
							break;
						case 347:
							if (this.curChar == 114) {
								this.jjAddStates(	180,
													181);
							}
							break;
						case 349:
							if (this.curChar == 93 && kind > 33) {
								kind = 33;
							}
							break;
						case 350:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 347;
							}
							break;
						case 351:
							if (this.curChar == 118) {
								this.jjstateSet[this.jjnewStateCnt++] = 350;
							}
							break;
						case 352:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 351;
							}
							break;
						case 353:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 352;
							}
							break;
						case 354:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 353;
							}
							break;
						case 355:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 354;
							}
							break;
						case 359:
							if (this.curChar == 116) {
								this.jjAddStates(	182,
													183);
							}
							break;
						case 361:
							if (this.curChar == 93 && kind > 34) {
								kind = 34;
							}
							break;
						case 362:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 359;
							}
							break;
						case 363:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 362;
							}
							break;
						case 364:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 363;
							}
							break;
						case 365:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 364;
							}
							break;
						case 366:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 365;
							}
							break;
						case 367:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 366;
							}
							break;
						case 371:
							if (this.curChar == 104) {
								this.jjAddStates(	184,
													185);
							}
							break;
						case 373:
							if (this.curChar == 93 && kind > 35) {
								kind = 35;
							}
							break;
						case 374:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 371;
							}
							break;
						case 375:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 374;
							}
							break;
						case 376:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 375;
							}
							break;
						case 377:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 376;
							}
							break;
						case 378:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 377;
							}
							break;
						case 379:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 378;
							}
							break;
						case 383:
							if (this.curChar == 108) {
								this.jjAddStates(	186,
													187);
							}
							break;
						case 385:
							if (this.curChar == 93 && kind > 36) {
								kind = 36;
							}
							break;
						case 386:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 383;
							}
							break;
						case 387:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 386;
							}
							break;
						case 388:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 387;
							}
							break;
						case 389:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 388;
							}
							break;
						case 393:
							if (this.curChar == 108) {
								this.jjAddStates(	188,
													189);
							}
							break;
						case 395:
							if (this.curChar == 93 && kind > 37) {
								kind = 37;
							}
							break;
						case 396:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 393;
							}
							break;
						case 397:
							if (this.curChar == 98) {
								this.jjstateSet[this.jjnewStateCnt++] = 396;
							}
							break;
						case 398:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 397;
							}
							break;
						case 399:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 398;
							}
							break;
						case 400:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 399;
							}
							break;
						case 404:
							if (this.curChar == 110) {
								this.jjAddStates(	190,
													191);
							}
							break;
						case 406:
							if (this.curChar == 93 && kind > 38) {
								kind = 38;
							}
							break;
						case 407:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 404;
							}
							break;
						case 408:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 407;
							}
							break;
						case 409:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 408;
							}
							break;
						case 410:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 409;
							}
							break;
						case 411:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 410;
							}
							break;
						case 415:
							if (this.curChar == 110) {
								this.jjAddStates(	192,
													193);
							}
							break;
						case 417:
							if (this.curChar == 93 && kind > 39) {
								kind = 39;
							}
							break;
						case 418:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 415;
							}
							break;
						case 419:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 418;
							}
							break;
						case 420:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 419;
							}
							break;
						case 421:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 420;
							}
							break;
						case 422:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 421;
							}
							break;
						case 423:
							if (this.curChar == 117) {
								this.jjstateSet[this.jjnewStateCnt++] = 422;
							}
							break;
						case 424:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 423;
							}
							break;
						case 428:
							if (this.curChar == 111) {
								this.jjAddStates(	194,
													195);
							}
							break;
						case 430:
							if (this.curChar == 93 && kind > 40) {
								kind = 40;
							}
							break;
						case 431:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 428;
							}
							break;
						case 432:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 431;
							}
							break;
						case 433:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 432;
							}
							break;
						case 434:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 433;
							}
							break;
						case 438:
							if (this.curChar == 115) {
								this.jjAddStates(	196,
													197);
							}
							break;
						case 440:
							if (this.curChar == 93 && kind > 41) {
								kind = 41;
							}
							break;
						case 441:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 438;
							}
							break;
						case 442:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 441;
							}
							break;
						case 443:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 442;
							}
							break;
						case 444:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 443;
							}
							break;
						case 445:
							if (this.curChar == 109) {
								this.jjstateSet[this.jjnewStateCnt++] = 444;
							}
							break;
						case 446:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 445;
							}
							break;
						case 447:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 446;
							}
							break;
						case 451:
							if (this.curChar == 109) {
								this.jjAddStates(	198,
													199);
							}
							break;
						case 453:
							if (this.curChar == 93 && kind > 42) {
								kind = 42;
							}
							break;
						case 454:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 451;
							}
							break;
						case 455:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 454;
							}
							break;
						case 456:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 455;
							}
							break;
						case 457:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 456;
							}
							break;
						case 458:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 457;
							}
							break;
						case 459:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 458;
							}
							break;
						case 460:
							if (this.curChar == 114) {
								this.jjstateSet[this.jjnewStateCnt++] = 459;
							}
							break;
						case 461:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 460;
							}
							break;
						case 465:
							if (this.curChar == 104) {
								this.jjAddStates(	200,
													201);
							}
							break;
						case 467:
							if (this.curChar == 93 && kind > 43) {
								kind = 43;
							}
							break;
						case 468:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 465;
							}
							break;
						case 469:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 468;
							}
							break;
						case 470:
							if (this.curChar == 105) {
								this.jjstateSet[this.jjnewStateCnt++] = 469;
							}
							break;
						case 471:
							if (this.curChar == 119) {
								this.jjstateSet[this.jjnewStateCnt++] = 470;
							}
							break;
						case 472:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 471;
							}
							break;
						case 492:
							if (this.curChar == 101) {
								this.jjAddStates(	202,
													203);
							}
							break;
						case 494:
							if (this.curChar == 93 && kind > 60) {
								kind = 60;
							}
							break;
						case 495:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 492;
							}
							break;
						case 496:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 495;
							}
							break;
						case 497:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 496;
							}
							break;
						case 498:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 497;
							}
							break;
						case 499:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 498;
							}
							break;
						case 504:
							if (this.curChar == 101) {
								this.jjAddStates(	204,
													205);
							}
							break;
						case 506:
							if (this.curChar == 93 && kind > 62) {
								kind = 62;
							}
							break;
						case 507:
							if (this.curChar == 112) {
								this.jjstateSet[this.jjnewStateCnt++] = 504;
							}
							break;
						case 508:
							if (this.curChar == 97) {
								this.jjstateSet[this.jjnewStateCnt++] = 507;
							}
							break;
						case 509:
							if (this.curChar == 99) {
								this.jjstateSet[this.jjnewStateCnt++] = 508;
							}
							break;
						case 510:
							if (this.curChar == 115) {
								this.jjstateSet[this.jjnewStateCnt++] = 509;
							}
							break;
						case 511:
							if (this.curChar == 101) {
								this.jjstateSet[this.jjnewStateCnt++] = 510;
							}
							break;
						case 512:
							if (this.curChar == 111) {
								this.jjstateSet[this.jjnewStateCnt++] = 511;
							}
							break;
						case 513:
							if (this.curChar == 110) {
								this.jjstateSet[this.jjnewStateCnt++] = 512;
							}
							break;
						case 516:
							if (this.curChar == 64 && kind > 63) {
								kind = 63;
							}
							break;
						case 517:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 518;
							}
							break;
						case 519:
						case 547:
							if (this.curChar == 116) {
								this.jjCheckNAdd(517);
							}
							break;
						case 520:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 519;
							}
							break;
						case 522:
							if (this.curChar == 108) {
								this.jjAddStates(	275,
													276);
							}
							break;
						case 524:
							if (this.curChar == 93 && kind > 66) {
								kind = 66;
							}
							break;
						case 525:
						case 550:
							if (this.curChar == 116) {
								this.jjCheckNAdd(522);
							}
							break;
						case 526:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 525;
							}
							break;
						case 529:
							if ((0x7fffffe87fffffeL & l) == 0L) {
								break;
							}
							if (kind > 67) {
								kind = 67;
							}
							this.jjstateSet[this.jjnewStateCnt++] = 529;
							break;
						case 531:
							if (this.curChar == 91) {
								this.jjAddStates(	214,
													274);
							}
							break;
						case 548:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 547;
							}
							break;
						case 551:
							if (this.curChar == 102) {
								this.jjstateSet[this.jjnewStateCnt++] = 550;
							}
							break;
						case 554:
							if (this.curChar == 91) {
								this.jjAddStates(	7,
													8);
							}
							break;
						case 558:
							if (this.curChar == 64) {
								this.jjCheckNAddStates(	277,
														279);
							}
							break;
						case 559:
						case 560:
							if ((0x7fffffe87ffffffL & l) != 0L) {
								this.jjCheckNAddStates(	206,
														209);
							}
							break;
						case 562:
						case 563:
							if ((0x7fffffe87ffffffL & l) != 0L) {
								this.jjCheckNAddStates(	210,
														213);
							}
							break;
						case 565:
							if (this.curChar == 93 && kind > 64) {
								kind = 64;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 2:
						case 1:
							if (!jjCanMove_0(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 69) {
								kind = 69;
							}
							this.jjCheckNAdd(1);
							break;
						case 559:
						case 560:
							if (jjCanMove_1(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjCheckNAddStates(	206,
														209);
							}
							break;
						case 562:
						case 563:
							if (jjCanMove_1(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjCheckNAddStates(	210,
														213);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 567 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	private final int jjStopStringLiteralDfa_2(	int pos,
												long active0,
												long active1) {
		switch (pos) {
			case 0:
				if ((active1 & 0x800000000000L) != 0L) {
					return 2;
				}
				if ((active1 & 0xe0000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					return 34;
				}
				if ((active1 & 0x8000000000L) != 0L) {
					return 36;
				}
				return -1;
			case 1:
				if ((active1 & 0x60000000000000L) != 0L) {
					return 34;
				}
				if ((active1 & 0x80000000180000L) != 0L) {
					if (this.jjmatchedPos != 1) {
						this.jjmatchedKind = 120;
						this.jjmatchedPos = 1;
					}
					return 34;
				}
				return -1;
			case 2:
				if ((active1 & 0x80000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 2;
					return 34;
				}
				return -1;
			case 3:
				if ((active1 & 0x100000L) != 0L) {
					return 34;
				}
				if ((active1 & 0x80000000080000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 3;
					return 34;
				}
				return -1;
			default:
				return -1;
		}
	}

	private final int jjStartNfa_2(	int pos,
									long active0,
									long active1) {
		return this.jjMoveNfa_2(this.jjStopStringLiteralDfa_2(	pos,
																active0,
																active1),
								pos + 1);
	}

	private final int jjStartNfaWithStates_2(	int pos,
												int kind,
												int state) {
		this.jjmatchedKind = kind;
		this.jjmatchedPos = pos;
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			return pos + 1;
		}
		return this.jjMoveNfa_2(state,
								pos + 1);
	}

	private final int jjMoveStringLiteralDfa0_2() {
		switch (this.curChar) {
			case 33:
				this.jjmatchedKind = 107;
				return this.jjMoveStringLiteralDfa1_2(0x20000000L);
			case 37:
				return this.jjStopAtPos(0,
										104);
			case 40:
				return this.jjStopAtPos(0,
										113);
			case 41:
				return this.jjStopAtPos(0,
										114);
			case 42:
				this.jjmatchedKind = 100;
				return this.jjMoveStringLiteralDfa1_2(0x2000000000L);
			case 43:
				return this.jjStopAtPos(0,
										98);
			case 44:
				return this.jjStopAtPos(0,
										108);
			case 45:
				return this.jjStopAtPos(0,
										99);
			case 46:
				this.jjmatchedKind = 87;
				return this.jjMoveStringLiteralDfa1_2(0x4001000000L);
			case 47:
				return this.jjStartNfaWithStates_2(	0,
													103,
													36);
			case 58:
				return this.jjStopAtPos(0,
										110);
			case 59:
				return this.jjStopAtPos(0,
										109);
			case 61:
				this.jjmatchedKind = 91;
				return this.jjMoveStringLiteralDfa1_2(0x10000000L);
			case 62:
				return this.jjStopAtPos(0,
										123);
			case 63:
				this.jjmatchedKind = 89;
				return this.jjMoveStringLiteralDfa1_2(0x4000000L);
			case 91:
				return this.jjStartNfaWithStates_2(	0,
													111,
													2);
			case 93:
				return this.jjStopAtPos(0,
										112);
			case 97:
				return this.jjMoveStringLiteralDfa1_2(0x40000000000000L);
			case 102:
				return this.jjMoveStringLiteralDfa1_2(0x80000L);
			case 105:
				return this.jjMoveStringLiteralDfa1_2(0x20000000000000L);
			case 116:
				return this.jjMoveStringLiteralDfa1_2(0x100000L);
			case 117:
				return this.jjMoveStringLiteralDfa1_2(0x80000000000000L);
			case 123:
				return this.jjStopAtPos(0,
										115);
			case 125:
				return this.jjStopAtPos(0,
										116);
			default:
				return this.jjMoveNfa_2(1,
										0);
		}
	}

	private final int jjMoveStringLiteralDfa1_2(long active1) {
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_2(	0,
											0L,
											active1);
			return 1;
		}
		switch (this.curChar) {
			case 42:
				if ((active1 & 0x2000000000L) != 0L) {
					return this.jjStopAtPos(1,
											101);
				}
				break;
			case 46:
				if ((active1 & 0x1000000L) != 0L) {
					this.jjmatchedKind = 88;
					this.jjmatchedPos = 1;
				}
				return this.jjMoveStringLiteralDfa2_2(	active1,
														0x4000000000L);
			case 61:
				if ((active1 & 0x10000000L) != 0L) {
					return this.jjStopAtPos(1,
											92);
				}
				else if ((active1 & 0x20000000L) != 0L) {
					return this.jjStopAtPos(1,
											93);
				}
				break;
			case 63:
				if ((active1 & 0x4000000L) != 0L) {
					return this.jjStopAtPos(1,
											90);
				}
				break;
			case 97:
				return this.jjMoveStringLiteralDfa2_2(	active1,
														0x80000L);
			case 110:
				if ((active1 & 0x20000000000000L) != 0L) {
					return this.jjStartNfaWithStates_2(	1,
														117,
														34);
				}
				break;
			case 114:
				return this.jjMoveStringLiteralDfa2_2(	active1,
														0x100000L);
			case 115:
				if ((active1 & 0x40000000000000L) != 0L) {
					return this.jjStartNfaWithStates_2(	1,
														118,
														34);
				}
				return this.jjMoveStringLiteralDfa2_2(	active1,
														0x80000000000000L);
			default:
				break;
		}
		return this.jjStartNfa_2(	0,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa2_2(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_2(	0,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_2(	1,
											0L,
											active1);
			return 2;
		}
		switch (this.curChar) {
			case 46:
				if ((active1 & 0x4000000000L) != 0L) {
					return this.jjStopAtPos(2,
											102);
				}
				break;
			case 105:
				return this.jjMoveStringLiteralDfa3_2(	active1,
														0x80000000000000L);
			case 108:
				return this.jjMoveStringLiteralDfa3_2(	active1,
														0x80000L);
			case 117:
				return this.jjMoveStringLiteralDfa3_2(	active1,
														0x100000L);
			default:
				break;
		}
		return this.jjStartNfa_2(	1,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa3_2(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_2(	1,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_2(	2,
											0L,
											active1);
			return 3;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x100000L) != 0L) {
					return this.jjStartNfaWithStates_2(	3,
														84,
														34);
				}
				break;
			case 110:
				return this.jjMoveStringLiteralDfa4_2(	active1,
														0x80000000000000L);
			case 115:
				return this.jjMoveStringLiteralDfa4_2(	active1,
														0x80000L);
			default:
				break;
		}
		return this.jjStartNfa_2(	2,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa4_2(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_2(	2,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_2(	3,
											0L,
											active1);
			return 4;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x80000L) != 0L) {
					return this.jjStartNfaWithStates_2(	4,
														83,
														34);
				}
				break;
			case 103:
				if ((active1 & 0x80000000000000L) != 0L) {
					return this.jjStartNfaWithStates_2(	4,
														119,
														34);
				}
				break;
			default:
				break;
		}
		return this.jjStartNfa_2(	3,
									0L,
									active1);
	}

	private final int jjMoveNfa_2(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 73;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 36:
							if (this.curChar == 62 && kind > 124) {
								kind = 124;
							}
							break;
						case 1:
							if ((0x3ff000000000000L & l) != 0L) {
								if (kind > 85) {
									kind = 85;
								}
								this.jjCheckNAddStates(	280,
														282);
							}
							else if ((0x100002600L & l) != 0L) {
								if (kind > 73) {
									kind = 73;
								}
								this.jjCheckNAdd(0);
							}
							else if (this.curChar == 38) {
								this.jjAddStates(	283,
													287);
							}
							else if (this.curChar == 47) {
								this.jjAddStates(	288,
													289);
							}
							else if (this.curChar == 36) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(34);
							}
							else if (this.curChar == 60) {
								this.jjCheckNAdd(27);
							}
							else if (this.curChar == 39) {
								this.jjCheckNAddStates(	290,
														292);
							}
							else if (this.curChar == 34) {
								this.jjCheckNAddStates(	293,
														295);
							}
							if (this.curChar == 38) {
								if (kind > 105) {
									kind = 105;
								}
							}
							else if (this.curChar == 60) {
								if (kind > 94) {
									kind = 94;
								}
							}
							if (this.curChar == 60) {
								this.jjstateSet[this.jjnewStateCnt++] = 2;
							}
							break;
						case 0:
							if ((0x100002600L & l) == 0L) {
								break;
							}
							if (kind > 73) {
								kind = 73;
							}
							this.jjCheckNAdd(0);
							break;
						case 2:
							if ((0xa00000000L & l) != 0L) {
								this.jjstateSet[this.jjnewStateCnt++] = 4;
							}
							break;
						case 3:
							if (this.curChar == 45 && kind > 74) {
								kind = 74;
							}
							break;
						case 4:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 3;
							}
							break;
						case 5:
							if (this.curChar == 34) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 6:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 8:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 9:
							if (this.curChar == 34 && kind > 81) {
								kind = 81;
							}
							break;
						case 11:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 12:
							if (this.curChar == 39) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 13:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 15:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 16:
							if (this.curChar == 39 && kind > 81) {
								kind = 81;
							}
							break;
						case 18:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 20:
							if (this.curChar == 34) {
								this.jjCheckNAddTwoStates(	21,
															22);
							}
							break;
						case 21:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	21,
															22);
							}
							break;
						case 22:
							if (this.curChar == 34 && kind > 82) {
								kind = 82;
							}
							break;
						case 23:
							if (this.curChar == 39) {
								this.jjCheckNAddTwoStates(	24,
															25);
							}
							break;
						case 24:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	24,
															25);
							}
							break;
						case 25:
							if (this.curChar == 39 && kind > 82) {
								kind = 82;
							}
							break;
						case 26:
							if (this.curChar == 60 && kind > 94) {
								kind = 94;
							}
							break;
						case 27:
							if (this.curChar == 61 && kind > 95) {
								kind = 95;
							}
							break;
						case 28:
							if (this.curChar == 60) {
								this.jjCheckNAdd(27);
							}
							break;
						case 29:
						case 70:
							if (this.curChar == 38 && kind > 105) {
								kind = 105;
							}
							break;
						case 33:
							if (this.curChar != 36) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 34:
							if ((0x3ff001000000000L & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 35:
							if (this.curChar == 47) {
								this.jjAddStates(	288,
													289);
							}
							break;
						case 38:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAddStates(	280,
													282);
							break;
						case 39:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAdd(39);
							break;
						case 40:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddTwoStates(	40,
															41);
							}
							break;
						case 41:
							if (this.curChar == 46) {
								this.jjCheckNAdd(42);
							}
							break;
						case 42:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 86) {
								kind = 86;
							}
							this.jjCheckNAdd(42);
							break;
						case 56:
							if (this.curChar == 38) {
								this.jjAddStates(	283,
													287);
							}
							break;
						case 57:
							if (this.curChar == 59 && kind > 94) {
								kind = 94;
							}
							break;
						case 60:
							if (this.curChar == 59) {
								this.jjCheckNAdd(27);
							}
							break;
						case 63:
							if (this.curChar == 59 && kind > 96) {
								kind = 96;
							}
							break;
						case 66:
							if (this.curChar == 61 && kind > 97) {
								kind = 97;
							}
							break;
						case 67:
							if (this.curChar == 59) {
								this.jjstateSet[this.jjnewStateCnt++] = 66;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 36:
							if (this.curChar == 93 && kind > 124) {
								kind = 124;
							}
							break;
						case 1:
							if ((0x7fffffe87ffffffL & l) != 0L) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(34);
							}
							else if (this.curChar == 92) {
								this.jjAddStates(	296,
													299);
							}
							else if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 31;
							}
							else if (this.curChar == 91) {
								this.jjstateSet[this.jjnewStateCnt++] = 2;
							}
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	51,
															72);
							}
							else if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	44,
															46);
							}
							else if (this.curChar == 124) {
								if (kind > 106) {
									kind = 106;
								}
							}
							else if (this.curChar == 114) {
								this.jjAddStates(	300,
													301);
							}
							break;
						case 6:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 7:
							if (this.curChar == 92) {
								this.jjAddStates(	302,
													303);
							}
							break;
						case 8:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 10:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 11;
							}
							break;
						case 11:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 13:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 14:
							if (this.curChar == 92) {
								this.jjAddStates(	304,
													305);
							}
							break;
						case 15:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 17:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 18;
							}
							break;
						case 18:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 19:
							if (this.curChar == 114) {
								this.jjAddStates(	300,
													301);
							}
							break;
						case 21:
							this.jjAddStates(	306,
												307);
							break;
						case 24:
							this.jjAddStates(	308,
												309);
							break;
						case 30:
						case 31:
							if (this.curChar == 124 && kind > 106) {
								kind = 106;
							}
							break;
						case 32:
							if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 31;
							}
							break;
						case 33:
						case 34:
							if ((0x7fffffe87ffffffL & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 43:
							if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	44,
															46);
							}
							break;
						case 44:
							if (this.curChar == 116 && kind > 94) {
								kind = 94;
							}
							break;
						case 45:
							if (this.curChar == 101 && kind > 95) {
								kind = 95;
							}
							break;
						case 46:
						case 49:
							if (this.curChar == 116) {
								this.jjCheckNAdd(45);
							}
							break;
						case 47:
							if (this.curChar == 92) {
								this.jjAddStates(	296,
													299);
							}
							break;
						case 48:
							if (this.curChar == 108) {
								this.jjCheckNAdd(44);
							}
							break;
						case 50:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 49;
							}
							break;
						case 51:
							if (this.curChar == 116 && kind > 96) {
								kind = 96;
							}
							break;
						case 52:
							if (this.curChar == 103) {
								this.jjCheckNAdd(51);
							}
							break;
						case 53:
							if (this.curChar == 101 && kind > 97) {
								kind = 97;
							}
							break;
						case 54:
						case 72:
							if (this.curChar == 116) {
								this.jjCheckNAdd(53);
							}
							break;
						case 55:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 54;
							}
							break;
						case 58:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 57;
							}
							break;
						case 59:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 58;
							}
							break;
						case 61:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 60;
							}
							break;
						case 62:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 61;
							}
							break;
						case 64:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 63;
							}
							break;
						case 65:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 64;
							}
							break;
						case 68:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 67;
							}
							break;
						case 69:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 68;
							}
							break;
						case 71:
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	51,
															72);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
						case 34:
							if (!jjCanMove_1(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 6:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	293,
													295);
							}
							break;
						case 13:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	290,
													292);
							}
							break;
						case 21:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	306,
													307);
							}
							break;
						case 24:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	308,
													309);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 73 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	private final int jjStopStringLiteralDfa_3(	int pos,
												long active0,
												long active1) {
		switch (pos) {
			case 0:
				if ((active1 & 0x800000000000L) != 0L) {
					return 2;
				}
				if ((active1 & 0xe0000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					return 34;
				}
				return -1;
			case 1:
				if ((active1 & 0x60000000000000L) != 0L) {
					return 34;
				}
				if ((active1 & 0x80000000180000L) != 0L) {
					if (this.jjmatchedPos != 1) {
						this.jjmatchedKind = 120;
						this.jjmatchedPos = 1;
					}
					return 34;
				}
				return -1;
			case 2:
				if ((active1 & 0x80000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 2;
					return 34;
				}
				return -1;
			case 3:
				if ((active1 & 0x100000L) != 0L) {
					return 34;
				}
				if ((active1 & 0x80000000080000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 3;
					return 34;
				}
				return -1;
			default:
				return -1;
		}
	}

	private final int jjStartNfa_3(	int pos,
									long active0,
									long active1) {
		return this.jjMoveNfa_3(this.jjStopStringLiteralDfa_3(	pos,
																active0,
																active1),
								pos + 1);
	}

	private final int jjStartNfaWithStates_3(	int pos,
												int kind,
												int state) {
		this.jjmatchedKind = kind;
		this.jjmatchedPos = pos;
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			return pos + 1;
		}
		return this.jjMoveNfa_3(state,
								pos + 1);
	}

	private final int jjMoveStringLiteralDfa0_3() {
		switch (this.curChar) {
			case 33:
				this.jjmatchedKind = 107;
				return this.jjMoveStringLiteralDfa1_3(0x20000000L);
			case 37:
				return this.jjStopAtPos(0,
										104);
			case 40:
				return this.jjStopAtPos(0,
										113);
			case 41:
				return this.jjStopAtPos(0,
										114);
			case 42:
				this.jjmatchedKind = 100;
				return this.jjMoveStringLiteralDfa1_3(0x2000000000L);
			case 43:
				return this.jjStopAtPos(0,
										98);
			case 44:
				return this.jjStopAtPos(0,
										108);
			case 45:
				return this.jjStopAtPos(0,
										99);
			case 46:
				this.jjmatchedKind = 87;
				return this.jjMoveStringLiteralDfa1_3(0x4001000000L);
			case 47:
				return this.jjStopAtPos(0,
										103);
			case 58:
				return this.jjStopAtPos(0,
										110);
			case 59:
				return this.jjStopAtPos(0,
										109);
			case 61:
				this.jjmatchedKind = 91;
				return this.jjMoveStringLiteralDfa1_3(0x10000000L);
			case 62:
				this.jjmatchedKind = 125;
				return this.jjMoveStringLiteralDfa1_3(0x4000000000000000L);
			case 63:
				this.jjmatchedKind = 89;
				return this.jjMoveStringLiteralDfa1_3(0x4000000L);
			case 91:
				return this.jjStartNfaWithStates_3(	0,
													111,
													2);
			case 93:
				return this.jjStopAtPos(0,
										112);
			case 97:
				return this.jjMoveStringLiteralDfa1_3(0x40000000000000L);
			case 102:
				return this.jjMoveStringLiteralDfa1_3(0x80000L);
			case 105:
				return this.jjMoveStringLiteralDfa1_3(0x20000000000000L);
			case 116:
				return this.jjMoveStringLiteralDfa1_3(0x100000L);
			case 117:
				return this.jjMoveStringLiteralDfa1_3(0x80000000000000L);
			case 123:
				return this.jjStopAtPos(0,
										115);
			case 125:
				return this.jjStopAtPos(0,
										116);
			default:
				return this.jjMoveNfa_3(1,
										0);
		}
	}

	private final int jjMoveStringLiteralDfa1_3(long active1) {
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_3(	0,
											0L,
											active1);
			return 1;
		}
		switch (this.curChar) {
			case 42:
				if ((active1 & 0x2000000000L) != 0L) {
					return this.jjStopAtPos(1,
											101);
				}
				break;
			case 46:
				if ((active1 & 0x1000000L) != 0L) {
					this.jjmatchedKind = 88;
					this.jjmatchedPos = 1;
				}
				return this.jjMoveStringLiteralDfa2_3(	active1,
														0x4000000000L);
			case 61:
				if ((active1 & 0x10000000L) != 0L) {
					return this.jjStopAtPos(1,
											92);
				}
				else if ((active1 & 0x20000000L) != 0L) {
					return this.jjStopAtPos(1,
											93);
				}
				else if ((active1 & 0x4000000000000000L) != 0L) {
					return this.jjStopAtPos(1,
											126);
				}
				break;
			case 63:
				if ((active1 & 0x4000000L) != 0L) {
					return this.jjStopAtPos(1,
											90);
				}
				break;
			case 97:
				return this.jjMoveStringLiteralDfa2_3(	active1,
														0x80000L);
			case 110:
				if ((active1 & 0x20000000000000L) != 0L) {
					return this.jjStartNfaWithStates_3(	1,
														117,
														34);
				}
				break;
			case 114:
				return this.jjMoveStringLiteralDfa2_3(	active1,
														0x100000L);
			case 115:
				if ((active1 & 0x40000000000000L) != 0L) {
					return this.jjStartNfaWithStates_3(	1,
														118,
														34);
				}
				return this.jjMoveStringLiteralDfa2_3(	active1,
														0x80000000000000L);
			default:
				break;
		}
		return this.jjStartNfa_3(	0,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa2_3(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_3(	0,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_3(	1,
											0L,
											active1);
			return 2;
		}
		switch (this.curChar) {
			case 46:
				if ((active1 & 0x4000000000L) != 0L) {
					return this.jjStopAtPos(2,
											102);
				}
				break;
			case 105:
				return this.jjMoveStringLiteralDfa3_3(	active1,
														0x80000000000000L);
			case 108:
				return this.jjMoveStringLiteralDfa3_3(	active1,
														0x80000L);
			case 117:
				return this.jjMoveStringLiteralDfa3_3(	active1,
														0x100000L);
			default:
				break;
		}
		return this.jjStartNfa_3(	1,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa3_3(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_3(	1,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_3(	2,
											0L,
											active1);
			return 3;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x100000L) != 0L) {
					return this.jjStartNfaWithStates_3(	3,
														84,
														34);
				}
				break;
			case 110:
				return this.jjMoveStringLiteralDfa4_3(	active1,
														0x80000000000000L);
			case 115:
				return this.jjMoveStringLiteralDfa4_3(	active1,
														0x80000L);
			default:
				break;
		}
		return this.jjStartNfa_3(	2,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa4_3(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_3(	2,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_3(	3,
											0L,
											active1);
			return 4;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x80000L) != 0L) {
					return this.jjStartNfaWithStates_3(	4,
														83,
														34);
				}
				break;
			case 103:
				if ((active1 & 0x80000000000000L) != 0L) {
					return this.jjStartNfaWithStates_3(	4,
														119,
														34);
				}
				break;
			default:
				break;
		}
		return this.jjStartNfa_3(	3,
									0L,
									active1);
	}

	private final int jjMoveNfa_3(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 70;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j, kind = 0x7fffffff;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
							if ((0x3ff000000000000L & l) != 0L) {
								if (kind > 85) {
									kind = 85;
								}
								this.jjCheckNAddStates(	310,
														312);
							}
							else if ((0x100002600L & l) != 0L) {
								if (kind > 73) {
									kind = 73;
								}
								this.jjCheckNAdd(0);
							}
							else if (this.curChar == 38) {
								this.jjAddStates(	313,
													317);
							}
							else if (this.curChar == 36) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(34);
							}
							else if (this.curChar == 60) {
								this.jjCheckNAdd(27);
							}
							else if (this.curChar == 39) {
								this.jjCheckNAddStates(	290,
														292);
							}
							else if (this.curChar == 34) {
								this.jjCheckNAddStates(	293,
														295);
							}
							if (this.curChar == 38) {
								if (kind > 105) {
									kind = 105;
								}
							}
							else if (this.curChar == 60) {
								if (kind > 94) {
									kind = 94;
								}
							}
							if (this.curChar == 60) {
								this.jjstateSet[this.jjnewStateCnt++] = 2;
							}
							break;
						case 0:
							if ((0x100002600L & l) == 0L) {
								break;
							}
							if (kind > 73) {
								kind = 73;
							}
							this.jjCheckNAdd(0);
							break;
						case 2:
							if ((0xa00000000L & l) != 0L) {
								this.jjstateSet[this.jjnewStateCnt++] = 4;
							}
							break;
						case 3:
							if (this.curChar == 45 && kind > 74) {
								kind = 74;
							}
							break;
						case 4:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 3;
							}
							break;
						case 5:
							if (this.curChar == 34) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 6:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 8:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 9:
							if (this.curChar == 34 && kind > 81) {
								kind = 81;
							}
							break;
						case 11:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 12:
							if (this.curChar == 39) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 13:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 15:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 16:
							if (this.curChar == 39 && kind > 81) {
								kind = 81;
							}
							break;
						case 18:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 20:
							if (this.curChar == 34) {
								this.jjCheckNAddTwoStates(	21,
															22);
							}
							break;
						case 21:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	21,
															22);
							}
							break;
						case 22:
							if (this.curChar == 34 && kind > 82) {
								kind = 82;
							}
							break;
						case 23:
							if (this.curChar == 39) {
								this.jjCheckNAddTwoStates(	24,
															25);
							}
							break;
						case 24:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	24,
															25);
							}
							break;
						case 25:
							if (this.curChar == 39 && kind > 82) {
								kind = 82;
							}
							break;
						case 26:
							if (this.curChar == 60 && kind > 94) {
								kind = 94;
							}
							break;
						case 27:
							if (this.curChar == 61 && kind > 95) {
								kind = 95;
							}
							break;
						case 28:
							if (this.curChar == 60) {
								this.jjCheckNAdd(27);
							}
							break;
						case 29:
						case 67:
							if (this.curChar == 38 && kind > 105) {
								kind = 105;
							}
							break;
						case 33:
							if (this.curChar != 36) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 34:
							if ((0x3ff001000000000L & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 35:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAddStates(	310,
													312);
							break;
						case 36:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAdd(36);
							break;
						case 37:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddTwoStates(	37,
															38);
							}
							break;
						case 38:
							if (this.curChar == 46) {
								this.jjCheckNAdd(39);
							}
							break;
						case 39:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 86) {
								kind = 86;
							}
							this.jjCheckNAdd(39);
							break;
						case 53:
							if (this.curChar == 38) {
								this.jjAddStates(	313,
													317);
							}
							break;
						case 54:
							if (this.curChar == 59 && kind > 94) {
								kind = 94;
							}
							break;
						case 57:
							if (this.curChar == 59) {
								this.jjCheckNAdd(27);
							}
							break;
						case 60:
							if (this.curChar == 59 && kind > 96) {
								kind = 96;
							}
							break;
						case 63:
							if (this.curChar == 61 && kind > 97) {
								kind = 97;
							}
							break;
						case 64:
							if (this.curChar == 59) {
								this.jjstateSet[this.jjnewStateCnt++] = 63;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
							if ((0x7fffffe87ffffffL & l) != 0L) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(34);
							}
							else if (this.curChar == 92) {
								this.jjAddStates(	318,
													321);
							}
							else if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 31;
							}
							else if (this.curChar == 91) {
								this.jjstateSet[this.jjnewStateCnt++] = 2;
							}
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	48,
															69);
							}
							else if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	41,
															43);
							}
							else if (this.curChar == 124) {
								if (kind > 106) {
									kind = 106;
								}
							}
							else if (this.curChar == 114) {
								this.jjAddStates(	300,
													301);
							}
							break;
						case 6:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 7:
							if (this.curChar == 92) {
								this.jjAddStates(	302,
													303);
							}
							break;
						case 8:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 10:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 11;
							}
							break;
						case 11:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 13:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 14:
							if (this.curChar == 92) {
								this.jjAddStates(	304,
													305);
							}
							break;
						case 15:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 17:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 18;
							}
							break;
						case 18:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 19:
							if (this.curChar == 114) {
								this.jjAddStates(	300,
													301);
							}
							break;
						case 21:
							this.jjAddStates(	306,
												307);
							break;
						case 24:
							this.jjAddStates(	308,
												309);
							break;
						case 30:
						case 31:
							if (this.curChar == 124 && kind > 106) {
								kind = 106;
							}
							break;
						case 32:
							if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 31;
							}
							break;
						case 33:
						case 34:
							if ((0x7fffffe87ffffffL & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 40:
							if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	41,
															43);
							}
							break;
						case 41:
							if (this.curChar == 116 && kind > 94) {
								kind = 94;
							}
							break;
						case 42:
							if (this.curChar == 101 && kind > 95) {
								kind = 95;
							}
							break;
						case 43:
						case 46:
							if (this.curChar == 116) {
								this.jjCheckNAdd(42);
							}
							break;
						case 44:
							if (this.curChar == 92) {
								this.jjAddStates(	318,
													321);
							}
							break;
						case 45:
							if (this.curChar == 108) {
								this.jjCheckNAdd(41);
							}
							break;
						case 47:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 46;
							}
							break;
						case 48:
							if (this.curChar == 116 && kind > 96) {
								kind = 96;
							}
							break;
						case 49:
							if (this.curChar == 103) {
								this.jjCheckNAdd(48);
							}
							break;
						case 50:
							if (this.curChar == 101 && kind > 97) {
								kind = 97;
							}
							break;
						case 51:
						case 69:
							if (this.curChar == 116) {
								this.jjCheckNAdd(50);
							}
							break;
						case 52:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 51;
							}
							break;
						case 55:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 54;
							}
							break;
						case 56:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 55;
							}
							break;
						case 58:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 57;
							}
							break;
						case 59:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 58;
							}
							break;
						case 61:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 60;
							}
							break;
						case 62:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 61;
							}
							break;
						case 65:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 64;
							}
							break;
						case 66:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 65;
							}
							break;
						case 68:
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	48,
															69);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
						case 34:
							if (!jjCanMove_1(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 6:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	293,
													295);
							}
							break;
						case 13:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	290,
													292);
							}
							break;
						case 21:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	306,
													307);
							}
							break;
						case 24:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	308,
													309);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 70 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	// private final int jjStopStringLiteralDfa_5( int pos,
	// long active0,
	// long active1) {
	// switch (pos) {
	// default:
	// return -1;
	// }
	// }

	// private final int jjStartNfa_5( int pos,
	// long active0,
	// long active1) {
	// return this.jjMoveNfa_5(this.jjStopStringLiteralDfa_5( pos,
	// active0,
	// active1),
	// pos + 1);
	// }

	private final int jjStartNfaWithStates_5(	int pos,
												int kind,
												int state) {
		this.jjmatchedKind = kind;
		this.jjmatchedPos = pos;
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			return pos + 1;
		}
		return this.jjMoveNfa_5(state,
								pos + 1);
	}

	private final int jjMoveStringLiteralDfa0_5() {
		switch (this.curChar) {
			case 45:
				return this.jjStartNfaWithStates_5(	0,
													78,
													3);
			default:
				return this.jjMoveNfa_5(1,
										0);
		}
	}

	private final int jjMoveNfa_5(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 6;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j, kind = 0x7fffffff;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 3:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 4;
							}
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 2;
							}
							break;
						case 1:
							if ((0xbfffdfffffffffffL & l) != 0L) {
								if (kind > 75) {
									kind = 75;
								}
								this.jjCheckNAdd(0);
							}
							else if (this.curChar == 45) {
								this.jjAddStates(	322,
													323);
							}
							break;
						case 0:
							if ((0xbfffdfffffffffffL & l) == 0L) {
								break;
							}
							kind = 75;
							this.jjCheckNAdd(0);
							break;
						case 2:
							if (this.curChar == 62) {
								kind = 79;
							}
							break;
						case 5:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 4;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
						case 0:
							if ((0xffffffffdfffffffL & l) == 0L) {
								break;
							}
							kind = 75;
							this.jjCheckNAdd(0);
							break;
						case 4:
							if (this.curChar == 93) {
								kind = 79;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
						case 0:
							if (!jjCanMove_0(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 75) {
								kind = 75;
							}
							this.jjCheckNAdd(0);
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 6 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	private final int jjStopStringLiteralDfa_6(	int pos,
												long active0,
												long active1) {
		switch (pos) {
			case 0:
				if ((active1 & 0x8000000000L) != 0L) {
					return 32;
				}
				if ((active1 & 0xe0000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					return 29;
				}
				return -1;
			case 1:
				if ((active1 & 0x80000000180000L) != 0L) {
					if (this.jjmatchedPos != 1) {
						this.jjmatchedKind = 120;
						this.jjmatchedPos = 1;
					}
					return 29;
				}
				if ((active1 & 0x60000000000000L) != 0L) {
					return 29;
				}
				return -1;
			case 2:
				if ((active1 & 0x80000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 2;
					return 29;
				}
				return -1;
			case 3:
				if ((active1 & 0x100000L) != 0L) {
					return 29;
				}
				if ((active1 & 0x80000000080000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 3;
					return 29;
				}
				return -1;
			default:
				return -1;
		}
	}

	private final int jjStartNfa_6(	int pos,
									long active0,
									long active1) {
		return this.jjMoveNfa_6(this.jjStopStringLiteralDfa_6(	pos,
																active0,
																active1),
								pos + 1);
	}

	private final int jjStartNfaWithStates_6(	int pos,
												int kind,
												int state) {
		this.jjmatchedKind = kind;
		this.jjmatchedPos = pos;
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			return pos + 1;
		}
		return this.jjMoveNfa_6(state,
								pos + 1);
	}

	private final int jjMoveStringLiteralDfa0_6() {
		switch (this.curChar) {
			case 33:
				this.jjmatchedKind = 107;
				return this.jjMoveStringLiteralDfa1_6(0x20000000L);
			case 37:
				return this.jjStopAtPos(0,
										104);
			case 40:
				return this.jjStopAtPos(0,
										113);
			case 41:
				return this.jjStopAtPos(0,
										114);
			case 42:
				this.jjmatchedKind = 100;
				return this.jjMoveStringLiteralDfa1_6(0x2000000000L);
			case 43:
				return this.jjStopAtPos(0,
										98);
			case 44:
				return this.jjStopAtPos(0,
										108);
			case 45:
				return this.jjStopAtPos(0,
										99);
			case 46:
				this.jjmatchedKind = 87;
				return this.jjMoveStringLiteralDfa1_6(0x4001000000L);
			case 47:
				return this.jjStartNfaWithStates_6(	0,
													103,
													32);
			case 58:
				return this.jjStopAtPos(0,
										110);
			case 59:
				return this.jjStopAtPos(0,
										109);
			case 61:
				this.jjmatchedKind = 91;
				return this.jjMoveStringLiteralDfa1_6(0x10000000L);
			case 62:
				return this.jjStopAtPos(0,
										123);
			case 63:
				this.jjmatchedKind = 89;
				return this.jjMoveStringLiteralDfa1_6(0x4000000L);
			case 91:
				return this.jjStopAtPos(0,
										111);
			case 93:
				return this.jjStopAtPos(0,
										112);
			case 97:
				return this.jjMoveStringLiteralDfa1_6(0x40000000000000L);
			case 102:
				return this.jjMoveStringLiteralDfa1_6(0x80000L);
			case 105:
				return this.jjMoveStringLiteralDfa1_6(0x20000000000000L);
			case 116:
				return this.jjMoveStringLiteralDfa1_6(0x100000L);
			case 117:
				return this.jjMoveStringLiteralDfa1_6(0x80000000000000L);
			case 123:
				return this.jjStopAtPos(0,
										115);
			case 125:
				return this.jjStopAtPos(0,
										116);
			default:
				return this.jjMoveNfa_6(0,
										0);
		}
	}

	private final int jjMoveStringLiteralDfa1_6(long active1) {
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_6(	0,
											0L,
											active1);
			return 1;
		}
		switch (this.curChar) {
			case 42:
				if ((active1 & 0x2000000000L) != 0L) {
					return this.jjStopAtPos(1,
											101);
				}
				break;
			case 46:
				if ((active1 & 0x1000000L) != 0L) {
					this.jjmatchedKind = 88;
					this.jjmatchedPos = 1;
				}
				return this.jjMoveStringLiteralDfa2_6(	active1,
														0x4000000000L);
			case 61:
				if ((active1 & 0x10000000L) != 0L) {
					return this.jjStopAtPos(1,
											92);
				}
				else if ((active1 & 0x20000000L) != 0L) {
					return this.jjStopAtPos(1,
											93);
				}
				break;
			case 63:
				if ((active1 & 0x4000000L) != 0L) {
					return this.jjStopAtPos(1,
											90);
				}
				break;
			case 97:
				return this.jjMoveStringLiteralDfa2_6(	active1,
														0x80000L);
			case 110:
				if ((active1 & 0x20000000000000L) != 0L) {
					return this.jjStartNfaWithStates_6(	1,
														117,
														29);
				}
				break;
			case 114:
				return this.jjMoveStringLiteralDfa2_6(	active1,
														0x100000L);
			case 115:
				if ((active1 & 0x40000000000000L) != 0L) {
					return this.jjStartNfaWithStates_6(	1,
														118,
														29);
				}
				return this.jjMoveStringLiteralDfa2_6(	active1,
														0x80000000000000L);
			default:
				break;
		}
		return this.jjStartNfa_6(	0,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa2_6(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_6(	0,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_6(	1,
											0L,
											active1);
			return 2;
		}
		switch (this.curChar) {
			case 46:
				if ((active1 & 0x4000000000L) != 0L) {
					return this.jjStopAtPos(2,
											102);
				}
				break;
			case 105:
				return this.jjMoveStringLiteralDfa3_6(	active1,
														0x80000000000000L);
			case 108:
				return this.jjMoveStringLiteralDfa3_6(	active1,
														0x80000L);
			case 117:
				return this.jjMoveStringLiteralDfa3_6(	active1,
														0x100000L);
			default:
				break;
		}
		return this.jjStartNfa_6(	1,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa3_6(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_6(	1,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_6(	2,
											0L,
											active1);
			return 3;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x100000L) != 0L) {
					return this.jjStartNfaWithStates_6(	3,
														84,
														29);
				}
				break;
			case 110:
				return this.jjMoveStringLiteralDfa4_6(	active1,
														0x80000000000000L);
			case 115:
				return this.jjMoveStringLiteralDfa4_6(	active1,
														0x80000L);
			default:
				break;
		}
		return this.jjStartNfa_6(	2,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa4_6(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_6(	2,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_6(	3,
											0L,
											active1);
			return 4;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x80000L) != 0L) {
					return this.jjStartNfaWithStates_6(	4,
														83,
														29);
				}
				break;
			case 103:
				if ((active1 & 0x80000000000000L) != 0L) {
					return this.jjStartNfaWithStates_6(	4,
														119,
														29);
				}
				break;
			default:
				break;
		}
		return this.jjStartNfa_6(	3,
									0L,
									active1);
	}

	private final int jjMoveNfa_6(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 69;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j, kind = 0x7fffffff;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 32:
							if (this.curChar == 62 && kind > 124) {
								kind = 124;
							}
							break;
						case 0:
							if ((0x3ff000000000000L & l) != 0L) {
								if (kind > 85) {
									kind = 85;
								}
								this.jjCheckNAddStates(	324,
														326);
							}
							else if ((0x100002600L & l) != 0L) {
								if (kind > 127) {
									kind = 127;
								}
								this.jjCheckNAdd(30);
							}
							else if (this.curChar == 38) {
								this.jjAddStates(	327,
													331);
							}
							else if (this.curChar == 47) {
								this.jjAddStates(	332,
													333);
							}
							else if (this.curChar == 36) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(29);
							}
							else if (this.curChar == 60) {
								this.jjCheckNAdd(22);
							}
							else if (this.curChar == 39) {
								this.jjCheckNAddStates(	334,
														336);
							}
							else if (this.curChar == 34) {
								this.jjCheckNAddStates(	337,
														339);
							}
							if (this.curChar == 38) {
								if (kind > 105) {
									kind = 105;
								}
							}
							else if (this.curChar == 60) {
								if (kind > 94) {
									kind = 94;
								}
							}
							break;
						case 1:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	337,
														339);
							}
							break;
						case 3:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	337,
														339);
							}
							break;
						case 4:
							if (this.curChar == 34 && kind > 81) {
								kind = 81;
							}
							break;
						case 6:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	337,
														339);
							}
							break;
						case 7:
							if (this.curChar == 39) {
								this.jjCheckNAddStates(	334,
														336);
							}
							break;
						case 8:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	334,
														336);
							}
							break;
						case 10:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	334,
														336);
							}
							break;
						case 11:
							if (this.curChar == 39 && kind > 81) {
								kind = 81;
							}
							break;
						case 13:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	334,
														336);
							}
							break;
						case 15:
							if (this.curChar == 34) {
								this.jjCheckNAddTwoStates(	16,
															17);
							}
							break;
						case 16:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	16,
															17);
							}
							break;
						case 17:
							if (this.curChar == 34 && kind > 82) {
								kind = 82;
							}
							break;
						case 18:
							if (this.curChar == 39) {
								this.jjCheckNAddTwoStates(	19,
															20);
							}
							break;
						case 19:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	19,
															20);
							}
							break;
						case 20:
							if (this.curChar == 39 && kind > 82) {
								kind = 82;
							}
							break;
						case 21:
							if (this.curChar == 60 && kind > 94) {
								kind = 94;
							}
							break;
						case 22:
							if (this.curChar == 61 && kind > 95) {
								kind = 95;
							}
							break;
						case 23:
							if (this.curChar == 60) {
								this.jjCheckNAdd(22);
							}
							break;
						case 24:
						case 66:
							if (this.curChar == 38 && kind > 105) {
								kind = 105;
							}
							break;
						case 28:
							if (this.curChar != 36) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(29);
							break;
						case 29:
							if ((0x3ff001000000000L & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(29);
							break;
						case 30:
							if ((0x100002600L & l) == 0L) {
								break;
							}
							if (kind > 127) {
								kind = 127;
							}
							this.jjCheckNAdd(30);
							break;
						case 31:
							if (this.curChar == 47) {
								this.jjAddStates(	332,
													333);
							}
							break;
						case 34:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAddStates(	324,
													326);
							break;
						case 35:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAdd(35);
							break;
						case 36:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddTwoStates(	36,
															37);
							}
							break;
						case 37:
							if (this.curChar == 46) {
								this.jjCheckNAdd(38);
							}
							break;
						case 38:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 86) {
								kind = 86;
							}
							this.jjCheckNAdd(38);
							break;
						case 52:
							if (this.curChar == 38) {
								this.jjAddStates(	327,
													331);
							}
							break;
						case 53:
							if (this.curChar == 59 && kind > 94) {
								kind = 94;
							}
							break;
						case 56:
							if (this.curChar == 59) {
								this.jjCheckNAdd(22);
							}
							break;
						case 59:
							if (this.curChar == 59 && kind > 96) {
								kind = 96;
							}
							break;
						case 62:
							if (this.curChar == 61 && kind > 97) {
								kind = 97;
							}
							break;
						case 63:
							if (this.curChar == 59) {
								this.jjstateSet[this.jjnewStateCnt++] = 62;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 32:
							if (this.curChar == 93 && kind > 124) {
								kind = 124;
							}
							break;
						case 0:
							if ((0x7fffffe87ffffffL & l) != 0L) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(29);
							}
							else if (this.curChar == 92) {
								this.jjAddStates(	340,
													343);
							}
							else if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 26;
							}
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	47,
															68);
							}
							else if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	40,
															42);
							}
							else if (this.curChar == 124) {
								if (kind > 106) {
									kind = 106;
								}
							}
							else if (this.curChar == 114) {
								this.jjAddStates(	344,
													345);
							}
							break;
						case 1:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	337,
														339);
							}
							break;
						case 2:
							if (this.curChar == 92) {
								this.jjAddStates(	322,
													323);
							}
							break;
						case 3:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	337,
														339);
							}
							break;
						case 5:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 6;
							}
							break;
						case 6:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	337,
														339);
							}
							break;
						case 8:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	334,
														336);
							}
							break;
						case 9:
							if (this.curChar == 92) {
								this.jjAddStates(	0,
													1);
							}
							break;
						case 10:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	334,
														336);
							}
							break;
						case 12:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 13;
							}
							break;
						case 13:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	334,
														336);
							}
							break;
						case 14:
							if (this.curChar == 114) {
								this.jjAddStates(	344,
													345);
							}
							break;
						case 16:
							this.jjAddStates(	346,
												347);
							break;
						case 19:
							this.jjAddStates(	348,
												349);
							break;
						case 25:
						case 26:
							if (this.curChar == 124 && kind > 106) {
								kind = 106;
							}
							break;
						case 27:
							if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 26;
							}
							break;
						case 28:
						case 29:
							if ((0x7fffffe87ffffffL & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(29);
							break;
						case 39:
							if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	40,
															42);
							}
							break;
						case 40:
							if (this.curChar == 116 && kind > 94) {
								kind = 94;
							}
							break;
						case 41:
							if (this.curChar == 101 && kind > 95) {
								kind = 95;
							}
							break;
						case 42:
						case 45:
							if (this.curChar == 116) {
								this.jjCheckNAdd(41);
							}
							break;
						case 43:
							if (this.curChar == 92) {
								this.jjAddStates(	340,
													343);
							}
							break;
						case 44:
							if (this.curChar == 108) {
								this.jjCheckNAdd(40);
							}
							break;
						case 46:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 45;
							}
							break;
						case 47:
							if (this.curChar == 116 && kind > 96) {
								kind = 96;
							}
							break;
						case 48:
							if (this.curChar == 103) {
								this.jjCheckNAdd(47);
							}
							break;
						case 49:
							if (this.curChar == 101 && kind > 97) {
								kind = 97;
							}
							break;
						case 50:
						case 68:
							if (this.curChar == 116) {
								this.jjCheckNAdd(49);
							}
							break;
						case 51:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 50;
							}
							break;
						case 54:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 53;
							}
							break;
						case 55:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 54;
							}
							break;
						case 57:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 56;
							}
							break;
						case 58:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 57;
							}
							break;
						case 60:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 59;
							}
							break;
						case 61:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 60;
							}
							break;
						case 64:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 63;
							}
							break;
						case 65:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 64;
							}
							break;
						case 67:
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	47,
															68);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 0:
						case 29:
							if (!jjCanMove_1(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(29);
							break;
						case 1:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	337,
													339);
							}
							break;
						case 8:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	334,
													336);
							}
							break;
						case 16:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	346,
													347);
							}
							break;
						case 19:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	348,
													349);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 69 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	private final int jjStopStringLiteralDfa_4(	int pos,
												long active0,
												long active1) {
		switch (pos) {
			case 0:
				if ((active1 & 0x800000000000L) != 0L) {
					return 2;
				}
				if ((active1 & 0x80020000000L) != 0L) {
					return 36;
				}
				if ((active1 & 0xe0000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					return 34;
				}
				if ((active1 & 0x8000000000L) != 0L) {
					return 38;
				}
				return -1;
			case 1:
				if ((active1 & 0x60000000000000L) != 0L) {
					return 34;
				}
				if ((active1 & 0x80000000180000L) != 0L) {
					if (this.jjmatchedPos != 1) {
						this.jjmatchedKind = 120;
						this.jjmatchedPos = 1;
					}
					return 34;
				}
				return -1;
			case 2:
				if ((active1 & 0x80000000180000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 2;
					return 34;
				}
				return -1;
			case 3:
				if ((active1 & 0x100000L) != 0L) {
					return 34;
				}
				if ((active1 & 0x80000000080000L) != 0L) {
					this.jjmatchedKind = 120;
					this.jjmatchedPos = 3;
					return 34;
				}
				return -1;
			default:
				return -1;
		}
	}

	private final int jjStartNfa_4(	int pos,
									long active0,
									long active1) {
		return this.jjMoveNfa_4(this.jjStopStringLiteralDfa_4(	pos,
																active0,
																active1),
								pos + 1);
	}

	private final int jjStartNfaWithStates_4(	int pos,
												int kind,
												int state) {
		this.jjmatchedKind = kind;
		this.jjmatchedPos = pos;
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			return pos + 1;
		}
		return this.jjMoveNfa_4(state,
								pos + 1);
	}

	private final int jjMoveStringLiteralDfa0_4() {
		switch (this.curChar) {
			case 33:
				this.jjmatchedKind = 107;
				return this.jjMoveStringLiteralDfa1_4(0x20000000L);
			case 37:
				return this.jjStopAtPos(0,
										104);
			case 40:
				return this.jjStopAtPos(0,
										113);
			case 41:
				return this.jjStopAtPos(0,
										114);
			case 42:
				this.jjmatchedKind = 100;
				return this.jjMoveStringLiteralDfa1_4(0x2000000000L);
			case 43:
				return this.jjStopAtPos(0,
										98);
			case 44:
				return this.jjStopAtPos(0,
										108);
			case 45:
				return this.jjStopAtPos(0,
										99);
			case 46:
				this.jjmatchedKind = 87;
				return this.jjMoveStringLiteralDfa1_4(0x4001000000L);
			case 47:
				return this.jjStartNfaWithStates_4(	0,
													103,
													38);
			case 58:
				return this.jjStopAtPos(0,
										110);
			case 59:
				return this.jjStopAtPos(0,
										109);
			case 61:
				this.jjmatchedKind = 91;
				return this.jjMoveStringLiteralDfa1_4(0x10000000L);
			case 62:
				return this.jjStopAtPos(0,
										123);
			case 63:
				this.jjmatchedKind = 89;
				return this.jjMoveStringLiteralDfa1_4(0x4000000L);
			case 91:
				return this.jjStartNfaWithStates_4(	0,
													111,
													2);
			case 93:
				return this.jjStopAtPos(0,
										112);
			case 97:
				return this.jjMoveStringLiteralDfa1_4(0x40000000000000L);
			case 102:
				return this.jjMoveStringLiteralDfa1_4(0x80000L);
			case 105:
				return this.jjMoveStringLiteralDfa1_4(0x20000000000000L);
			case 116:
				return this.jjMoveStringLiteralDfa1_4(0x100000L);
			case 117:
				return this.jjMoveStringLiteralDfa1_4(0x80000000000000L);
			case 123:
				return this.jjStopAtPos(0,
										115);
			case 125:
				return this.jjStopAtPos(0,
										116);
			default:
				return this.jjMoveNfa_4(1,
										0);
		}
	}

	private final int jjMoveStringLiteralDfa1_4(long active1) {
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_4(	0,
											0L,
											active1);
			return 1;
		}
		switch (this.curChar) {
			case 42:
				if ((active1 & 0x2000000000L) != 0L) {
					return this.jjStopAtPos(1,
											101);
				}
				break;
			case 46:
				if ((active1 & 0x1000000L) != 0L) {
					this.jjmatchedKind = 88;
					this.jjmatchedPos = 1;
				}
				return this.jjMoveStringLiteralDfa2_4(	active1,
														0x4000000000L);
			case 61:
				if ((active1 & 0x10000000L) != 0L) {
					return this.jjStopAtPos(1,
											92);
				}
				else if ((active1 & 0x20000000L) != 0L) {
					return this.jjStopAtPos(1,
											93);
				}
				break;
			case 63:
				if ((active1 & 0x4000000L) != 0L) {
					return this.jjStopAtPos(1,
											90);
				}
				break;
			case 97:
				return this.jjMoveStringLiteralDfa2_4(	active1,
														0x80000L);
			case 110:
				if ((active1 & 0x20000000000000L) != 0L) {
					return this.jjStartNfaWithStates_4(	1,
														117,
														34);
				}
				break;
			case 114:
				return this.jjMoveStringLiteralDfa2_4(	active1,
														0x100000L);
			case 115:
				if ((active1 & 0x40000000000000L) != 0L) {
					return this.jjStartNfaWithStates_4(	1,
														118,
														34);
				}
				return this.jjMoveStringLiteralDfa2_4(	active1,
														0x80000000000000L);
			default:
				break;
		}
		return this.jjStartNfa_4(	0,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa2_4(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_4(	0,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_4(	1,
											0L,
											active1);
			return 2;
		}
		switch (this.curChar) {
			case 46:
				if ((active1 & 0x4000000000L) != 0L) {
					return this.jjStopAtPos(2,
											102);
				}
				break;
			case 105:
				return this.jjMoveStringLiteralDfa3_4(	active1,
														0x80000000000000L);
			case 108:
				return this.jjMoveStringLiteralDfa3_4(	active1,
														0x80000L);
			case 117:
				return this.jjMoveStringLiteralDfa3_4(	active1,
														0x100000L);
			default:
				break;
		}
		return this.jjStartNfa_4(	1,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa3_4(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_4(	1,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_4(	2,
											0L,
											active1);
			return 3;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x100000L) != 0L) {
					return this.jjStartNfaWithStates_4(	3,
														84,
														34);
				}
				break;
			case 110:
				return this.jjMoveStringLiteralDfa4_4(	active1,
														0x80000000000000L);
			case 115:
				return this.jjMoveStringLiteralDfa4_4(	active1,
														0x80000L);
			default:
				break;
		}
		return this.jjStartNfa_4(	2,
									0L,
									active1);
	}

	private final int jjMoveStringLiteralDfa4_4(long old1,
												long active1) {
		if (((active1 &= old1)) == 0L) {
			return this.jjStartNfa_4(	2,
										0L,
										old1);
		}
		try {
			this.curChar = this.input_stream.readChar();
		}
		catch (java.io.IOException e) {
			this.jjStopStringLiteralDfa_4(	3,
											0L,
											active1);
			return 4;
		}
		switch (this.curChar) {
			case 101:
				if ((active1 & 0x80000L) != 0L) {
					return this.jjStartNfaWithStates_4(	4,
														83,
														34);
				}
				break;
			case 103:
				if ((active1 & 0x80000000000000L) != 0L) {
					return this.jjStartNfaWithStates_4(	4,
														119,
														34);
				}
				break;
			default:
				break;
		}
		return this.jjStartNfa_4(	3,
									0L,
									active1);
	}

	private final int jjMoveNfa_4(	int startState,
									int curPos) {
		// int[] nextStates;
		int startsAt = 0;
		this.jjnewStateCnt = 75;
		int i = 1;
		this.jjstateSet[0] = startState;
		// int j, kind = 0x7fffffff;
		int kind = 0x7fffffff;
		for (;;) {
			if (++this.jjround == 0x7fffffff) {
				this.ReInitRounds();
			}
			if (this.curChar < 64) {
				long l = 1L << this.curChar;
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
							if ((0x3ff000000000000L & l) != 0L) {
								if (kind > 85) {
									kind = 85;
								}
								this.jjCheckNAddStates(	350,
														352);
							}
							else if ((0x100002600L & l) != 0L) {
								if (kind > 73) {
									kind = 73;
								}
								this.jjCheckNAdd(0);
							}
							else if (this.curChar == 38) {
								this.jjAddStates(	353,
													357);
							}
							else if (this.curChar == 47) {
								this.jjAddStates(	358,
													359);
							}
							else if (this.curChar == 33) {
								this.jjCheckNAdd(36);
							}
							else if (this.curChar == 36) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(34);
							}
							else if (this.curChar == 60) {
								this.jjCheckNAdd(27);
							}
							else if (this.curChar == 39) {
								this.jjCheckNAddStates(	290,
														292);
							}
							else if (this.curChar == 34) {
								this.jjCheckNAddStates(	293,
														295);
							}
							if (this.curChar == 38) {
								if (kind > 105) {
									kind = 105;
								}
							}
							else if (this.curChar == 60) {
								if (kind > 94) {
									kind = 94;
								}
							}
							if (this.curChar == 60) {
								this.jjstateSet[this.jjnewStateCnt++] = 2;
							}
							break;
						case 38:
							if (this.curChar == 62 && kind > 124) {
								kind = 124;
							}
							break;
						case 0:
							if ((0x100002600L & l) == 0L) {
								break;
							}
							if (kind > 73) {
								kind = 73;
							}
							this.jjCheckNAdd(0);
							break;
						case 2:
							if ((0xa00000000L & l) != 0L) {
								this.jjstateSet[this.jjnewStateCnt++] = 4;
							}
							break;
						case 3:
							if (this.curChar == 45 && kind > 74) {
								kind = 74;
							}
							break;
						case 4:
							if (this.curChar == 45) {
								this.jjstateSet[this.jjnewStateCnt++] = 3;
							}
							break;
						case 5:
							if (this.curChar == 34) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 6:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 8:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 9:
							if (this.curChar == 34 && kind > 81) {
								kind = 81;
							}
							break;
						case 11:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 12:
							if (this.curChar == 39) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 13:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 15:
							if ((0x9400000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 16:
							if (this.curChar == 39 && kind > 81) {
								kind = 81;
							}
							break;
						case 18:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 20:
							if (this.curChar == 34) {
								this.jjCheckNAddTwoStates(	21,
															22);
							}
							break;
						case 21:
							if ((0xfffffffbffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	21,
															22);
							}
							break;
						case 22:
							if (this.curChar == 34 && kind > 82) {
								kind = 82;
							}
							break;
						case 23:
							if (this.curChar == 39) {
								this.jjCheckNAddTwoStates(	24,
															25);
							}
							break;
						case 24:
							if ((0xffffff7fffffffffL & l) != 0L) {
								this.jjCheckNAddTwoStates(	24,
															25);
							}
							break;
						case 25:
							if (this.curChar == 39 && kind > 82) {
								kind = 82;
							}
							break;
						case 26:
							if (this.curChar == 60 && kind > 94) {
								kind = 94;
							}
							break;
						case 27:
							if (this.curChar == 61 && kind > 95) {
								kind = 95;
							}
							break;
						case 28:
							if (this.curChar == 60) {
								this.jjCheckNAdd(27);
							}
							break;
						case 29:
						case 72:
							if (this.curChar == 38 && kind > 105) {
								kind = 105;
							}
							break;
						case 33:
							if (this.curChar != 36) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 34:
							if ((0x3ff001000000000L & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 35:
							if (this.curChar == 33) {
								this.jjCheckNAdd(36);
							}
							break;
						case 36:
							if ((0x100002600L & l) == 0L) {
								break;
							}
							if (kind > 128) {
								kind = 128;
							}
							this.jjCheckNAdd(36);
							break;
						case 37:
							if (this.curChar == 47) {
								this.jjAddStates(	358,
													359);
							}
							break;
						case 40:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAddStates(	350,
													352);
							break;
						case 41:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 85) {
								kind = 85;
							}
							this.jjCheckNAdd(41);
							break;
						case 42:
							if ((0x3ff000000000000L & l) != 0L) {
								this.jjCheckNAddTwoStates(	42,
															43);
							}
							break;
						case 43:
							if (this.curChar == 46) {
								this.jjCheckNAdd(44);
							}
							break;
						case 44:
							if ((0x3ff000000000000L & l) == 0L) {
								break;
							}
							if (kind > 86) {
								kind = 86;
							}
							this.jjCheckNAdd(44);
							break;
						case 58:
							if (this.curChar == 38) {
								this.jjAddStates(	353,
													357);
							}
							break;
						case 59:
							if (this.curChar == 59 && kind > 94) {
								kind = 94;
							}
							break;
						case 62:
							if (this.curChar == 59) {
								this.jjCheckNAdd(27);
							}
							break;
						case 65:
							if (this.curChar == 59 && kind > 96) {
								kind = 96;
							}
							break;
						case 68:
							if (this.curChar == 61 && kind > 97) {
								kind = 97;
							}
							break;
						case 69:
							if (this.curChar == 59) {
								this.jjstateSet[this.jjnewStateCnt++] = 68;
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else if (this.curChar < 128) {
				long l = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
							if ((0x7fffffe87ffffffL & l) != 0L) {
								if (kind > 120) {
									kind = 120;
								}
								this.jjCheckNAdd(34);
							}
							else if (this.curChar == 92) {
								this.jjAddStates(	360,
													363);
							}
							else if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 31;
							}
							else if (this.curChar == 91) {
								this.jjstateSet[this.jjnewStateCnt++] = 2;
							}
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	53,
															74);
							}
							else if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	46,
															48);
							}
							else if (this.curChar == 124) {
								if (kind > 106) {
									kind = 106;
								}
							}
							else if (this.curChar == 114) {
								this.jjAddStates(	300,
													301);
							}
							break;
						case 38:
							if (this.curChar == 93 && kind > 124) {
								kind = 124;
							}
							break;
						case 6:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 7:
							if (this.curChar == 92) {
								this.jjAddStates(	302,
													303);
							}
							break;
						case 8:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 10:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 11;
							}
							break;
						case 11:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	293,
														295);
							}
							break;
						case 13:
							if ((0xffffffffefffffffL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 14:
							if (this.curChar == 92) {
								this.jjAddStates(	304,
													305);
							}
							break;
						case 15:
							if ((0x81450c610000000L & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 17:
							if (this.curChar == 120) {
								this.jjstateSet[this.jjnewStateCnt++] = 18;
							}
							break;
						case 18:
							if ((0x7e0000007eL & l) != 0L) {
								this.jjCheckNAddStates(	290,
														292);
							}
							break;
						case 19:
							if (this.curChar == 114) {
								this.jjAddStates(	300,
													301);
							}
							break;
						case 21:
							this.jjAddStates(	306,
												307);
							break;
						case 24:
							this.jjAddStates(	308,
												309);
							break;
						case 30:
						case 31:
							if (this.curChar == 124 && kind > 106) {
								kind = 106;
							}
							break;
						case 32:
							if (this.curChar == 124) {
								this.jjstateSet[this.jjnewStateCnt++] = 31;
							}
							break;
						case 33:
						case 34:
							if ((0x7fffffe87ffffffL & l) == 0L) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 45:
							if (this.curChar == 108) {
								this.jjCheckNAddTwoStates(	46,
															48);
							}
							break;
						case 46:
							if (this.curChar == 116 && kind > 94) {
								kind = 94;
							}
							break;
						case 47:
							if (this.curChar == 101 && kind > 95) {
								kind = 95;
							}
							break;
						case 48:
						case 51:
							if (this.curChar == 116) {
								this.jjCheckNAdd(47);
							}
							break;
						case 49:
							if (this.curChar == 92) {
								this.jjAddStates(	360,
													363);
							}
							break;
						case 50:
							if (this.curChar == 108) {
								this.jjCheckNAdd(46);
							}
							break;
						case 52:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 51;
							}
							break;
						case 53:
							if (this.curChar == 116 && kind > 96) {
								kind = 96;
							}
							break;
						case 54:
							if (this.curChar == 103) {
								this.jjCheckNAdd(53);
							}
							break;
						case 55:
							if (this.curChar == 101 && kind > 97) {
								kind = 97;
							}
							break;
						case 56:
						case 74:
							if (this.curChar == 116) {
								this.jjCheckNAdd(55);
							}
							break;
						case 57:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 56;
							}
							break;
						case 60:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 59;
							}
							break;
						case 61:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 60;
							}
							break;
						case 63:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 62;
							}
							break;
						case 64:
							if (this.curChar == 108) {
								this.jjstateSet[this.jjnewStateCnt++] = 63;
							}
							break;
						case 66:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 65;
							}
							break;
						case 67:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 66;
							}
							break;
						case 70:
							if (this.curChar == 116) {
								this.jjstateSet[this.jjnewStateCnt++] = 69;
							}
							break;
						case 71:
							if (this.curChar == 103) {
								this.jjstateSet[this.jjnewStateCnt++] = 70;
							}
							break;
						case 73:
							if (this.curChar == 103) {
								this.jjCheckNAddTwoStates(	53,
															74);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			else {
				int hiByte = (this.curChar >> 8);
				int i1 = hiByte >> 6;
				long l1 = 1L << (hiByte & 077);
				int i2 = (this.curChar & 0xff) >> 6;
				long l2 = 1L << (this.curChar & 077);
				// MatchLoop:
				do {
					switch (this.jjstateSet[--i]) {
						case 1:
						case 34:
							if (!jjCanMove_1(	hiByte,
												i1,
												i2,
												l1,
												l2)) {
								break;
							}
							if (kind > 120) {
								kind = 120;
							}
							this.jjCheckNAdd(34);
							break;
						case 6:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	293,
													295);
							}
							break;
						case 13:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	290,
													292);
							}
							break;
						case 21:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	306,
													307);
							}
							break;
						case 24:
							if (jjCanMove_0(hiByte,
											i1,
											i2,
											l1,
											l2)) {
								this.jjAddStates(	308,
													309);
							}
							break;
						default:
							break;
					}
				} while (i != startsAt);
			}
			if (kind != 0x7fffffff) {
				this.jjmatchedKind = kind;
				this.jjmatchedPos = curPos;
				kind = 0x7fffffff;
			}
			++curPos;
			if ((i = this.jjnewStateCnt) == (startsAt = 75 - (this.jjnewStateCnt = startsAt))) {
				return curPos;
			}
			try {
				this.curChar = this.input_stream.readChar();
			}
			catch (java.io.IOException e) {
				return curPos;
			}
		}
	}

	static final int[] jjnextStates = {
		10,
		12,
		4,
		5,
		3,
		4,
		5,
		557,
		566,
		306,
		307,
		308,
		309,
		310,
		311,
		312,
		313,
		314,
		315,
		316,
		317,
		318,
		319,
		320,
		321,
		322,
		323,
		324,
		325,
		326,
		327,
		328,
		329,
		330,
		336,
		337,
		345,
		346,
		357,
		358,
		369,
		370,
		381,
		382,
		391,
		392,
		402,
		403,
		413,
		414,
		426,
		427,
		436,
		437,
		449,
		450,
		463,
		464,
		474,
		475,
		476,
		477,
		478,
		479,
		480,
		481,
		482,
		483,
		484,
		485,
		486,
		487,
		488,
		489,
		490,
		491,
		501,
		502,
		503,
		515,
		516,
		521,
		527,
		528,
		530,
		12,
		21,
		24,
		31,
		36,
		44,
		51,
		56,
		63,
		70,
		76,
		84,
		91,
		100,
		106,
		116,
		122,
		127,
		134,
		139,
		147,
		157,
		166,
		175,
		182,
		190,
		199,
		206,
		214,
		215,
		223,
		228,
		233,
		242,
		251,
		258,
		268,
		276,
		287,
		294,
		304,
		5,
		6,
		14,
		15,
		149,
		150,
		159,
		160,
		168,
		169,
		177,
		178,
		179,
		184,
		185,
		186,
		192,
		193,
		194,
		201,
		202,
		203,
		208,
		209,
		210,
		216,
		217,
		218,
		220,
		221,
		222,
		225,
		226,
		227,
		230,
		231,
		232,
		235,
		236,
		244,
		245,
		246,
		260,
		261,
		262,
		278,
		279,
		280,
		296,
		297,
		332,
		333,
		339,
		340,
		348,
		349,
		360,
		361,
		372,
		373,
		384,
		385,
		394,
		395,
		405,
		406,
		416,
		417,
		429,
		430,
		439,
		440,
		452,
		453,
		466,
		467,
		493,
		494,
		505,
		506,
		560,
		561,
		564,
		565,
		561,
		563,
		564,
		565,
		306,
		307,
		308,
		309,
		310,
		311,
		312,
		313,
		314,
		315,
		316,
		317,
		318,
		319,
		320,
		321,
		322,
		323,
		324,
		325,
		326,
		327,
		328,
		329,
		532,
		533,
		534,
		535,
		536,
		537,
		538,
		539,
		540,
		541,
		542,
		543,
		544,
		475,
		476,
		477,
		478,
		479,
		480,
		481,
		482,
		483,
		484,
		485,
		486,
		487,
		488,
		489,
		490,
		545,
		502,
		546,
		516,
		549,
		552,
		528,
		553,
		523,
		524,
		559,
		564,
		565,
		39,
		40,
		41,
		59,
		62,
		65,
		69,
		70,
		36,
		37,
		13,
		14,
		16,
		6,
		7,
		9,
		48,
		50,
		52,
		55,
		20,
		23,
		8,
		10,
		15,
		17,
		21,
		22,
		24,
		25,
		36,
		37,
		38,
		56,
		59,
		62,
		66,
		67,
		45,
		47,
		49,
		52,
		3,
		5,
		35,
		36,
		37,
		55,
		58,
		61,
		65,
		66,
		32,
		33,
		8,
		9,
		11,
		1,
		2,
		4,
		44,
		46,
		48,
		51,
		15,
		18,
		16,
		17,
		19,
		20,
		41,
		42,
		43,
		61,
		64,
		67,
		71,
		72,
		38,
		39,
		50,
		52,
		54,
		57,
	};

	private static final boolean jjCanMove_0(	int hiByte,
												int i1,
												int i2,
												long l1,
												long l2) {
		switch (hiByte) {
			case 0:
				return ((jjbitVec2[i2] & l2) != 0L);
			default:
				if ((jjbitVec0[i1] & l1) != 0L) {
					return true;
				}
				return false;
		}
	}

	private static final boolean jjCanMove_1(	int hiByte,
												int i1,
												int i2,
												long l1,
												long l2) {
		switch (hiByte) {
			case 0:
				return ((jjbitVec4[i2] & l2) != 0L);
			case 48:
				return ((jjbitVec5[i2] & l2) != 0L);
			case 49:
				return ((jjbitVec6[i2] & l2) != 0L);
			case 51:
				return ((jjbitVec7[i2] & l2) != 0L);
			case 61:
				return ((jjbitVec8[i2] & l2) != 0L);
			default:
				if ((jjbitVec3[i1] & l1) != 0L) {
					return true;
				}
				return false;
		}
	}

	public static final String[] jjstrLiteralImages = {
		"",
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		"\44\173",
		"\43\173",
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		"\146\141\154\163\145",
		"\164\162\165\145",
		null,
		null,
		"\56",
		"\56\56",
		"\77",
		"\77\77",
		"\75",
		"\75\75",
		"\41\75",
		null,
		null,
		null,
		null,
		"\53",
		"\55",
		"\52",
		"\52\52",
		"\56\56\56",
		"\57",
		"\45",
		null,
		null,
		"\41",
		"\54",
		"\73",
		"\72",
		"\133",
		"\135",
		"\50",
		"\51",
		"\173",
		"\175",
		"\151\156",
		"\141\163",
		"\165\163\151\156\147",
		null,
		null,
		null,
		"\76",
		null,
		"\76",
		"\76\75",
		null,
		null,
		null,
		null,
		null,
		null,
	};

	public static final String[] lexStateNames = {
		"DEFAULT",
		"NODIRECTIVE",
		"FM_EXPRESSION",
		"IN_PAREN",
		"NAMED_PARAMETER_EXPRESSION",
		"EXPRESSION_COMMENT",
		"NO_SPACE_EXPRESSION",
		"NO_PARSE",
	};

	public static final int[] jjnewLexState = {
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		2,
		2,
		-1,
		5,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		-1,
		2,
		2,
		-1,
		-1,
		-1,
		-1,
	};

	static final long[] jjtoToken = {
		0xffffffffffffffc1L,
		0xf9fffffffffe01ffL,
		0x1fL,
	};

	static final long[] jjtoSkip = {
		0x0L,
		0xfe00L,
		0x0L,
	};

	protected SimpleCharStream input_stream;

	private final int[] jjrounds = new int[567];

	private final int[] jjstateSet = new int[1134];

	StringBuffer image;

	int jjimageLen;

	int lengthOfMatch;

	protected char curChar;

	public FMParserTokenManager(SimpleCharStream stream) {
		if (SimpleCharStream.staticFlag) {
			throw new Error("ERROR: Cannot use a static CharStream class with a non-static lexical analyzer.");
		}
		this.input_stream = stream;
	}

	public FMParserTokenManager(SimpleCharStream stream,
								int lexState) {
		this(stream);
		this.SwitchTo(lexState);
	}

	public void ReInit(SimpleCharStream stream) {
		this.jjmatchedPos = this.jjnewStateCnt = 0;
		this.curLexState = this.defaultLexState;
		this.input_stream = stream;
		this.ReInitRounds();
	}

	private final void ReInitRounds() {
		int i;
		this.jjround = 0x80000001;
		for (i = 567; i-- > 0;) {
			this.jjrounds[i] = 0x80000000;
		}
	}

	public void ReInit(	SimpleCharStream stream,
						int lexState) {
		this.ReInit(stream);
		this.SwitchTo(lexState);
	}

	public void SwitchTo(int lexState) {
		if (lexState >= 8 || lexState < 0) {
			throw new TokenMgrError("Error: Ignoring invalid lexical state : "
										+ lexState
										+ ". State unchanged.",
									TokenMgrError.INVALID_LEXICAL_STATE);
		}
		else {
			this.curLexState = lexState;
		}
	}

	protected Token jjFillToken() {
		Token t = Token.newToken(this.jjmatchedKind);
		t.kind = this.jjmatchedKind;
		String im = jjstrLiteralImages[this.jjmatchedKind];
		t.image = (im == null)	? this.input_stream.GetImage()
								: im;
		t.beginLine = this.input_stream.getBeginLine();
		t.beginColumn = this.input_stream.getBeginColumn();
		t.endLine = this.input_stream.getEndLine();
		t.endColumn = this.input_stream.getEndColumn();
		return t;
	}

	int curLexState = 0;

	int defaultLexState = 0;

	int jjnewStateCnt;

	int jjround;

	int jjmatchedPos;

	int jjmatchedKind;

	public Token getNextToken() {
		// int kind;
		// Token specialToken = null;
		Token matchedToken;
		int curPos = 0;

		EOFLoop: for (;;) {
			try {
				this.curChar = this.input_stream.BeginToken();
			}
			catch (java.io.IOException e) {
				this.jjmatchedKind = 0;
				matchedToken = this.jjFillToken();
				return matchedToken;
			}
			this.image = null;
			this.jjimageLen = 0;

			switch (this.curLexState) {
				case 0:
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_0();
					break;
				case 1:
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_1();
					break;
				case 2:
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_2();
					break;
				case 3:
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_3();
					break;
				case 4:
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_4();
					break;
				case 5:
					try {
						this.input_stream.backup(0);
						while ((this.curChar < 64 && (0x4000000000000000L & (1L << this.curChar)) != 0L)
								|| (this.curChar >> 6) == 1
								&& (0x20000000L & (1L << (this.curChar & 077))) != 0L) {
							this.curChar = this.input_stream.BeginToken();
						}
					}
					catch (java.io.IOException e1) {
						continue EOFLoop;
					}
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_5();
					break;
				case 6:
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_6();
					break;
				case 7:
					this.jjmatchedKind = 0x7fffffff;
					this.jjmatchedPos = 0;
					curPos = this.jjMoveStringLiteralDfa0_7();
					break;
			}
			if (this.jjmatchedKind != 0x7fffffff) {
				if (this.jjmatchedPos + 1 < curPos) {
					this.input_stream.backup(curPos - this.jjmatchedPos - 1);
				}
				if ((jjtoToken[this.jjmatchedKind >> 6] & (1L << (this.jjmatchedKind & 077))) != 0L) {
					matchedToken = this.jjFillToken();
					this.TokenLexicalActions(matchedToken);
					if (jjnewLexState[this.jjmatchedKind] != -1) {
						this.curLexState = jjnewLexState[this.jjmatchedKind];
					}
					return matchedToken;
				}
				else {
					this.SkipLexicalActions(null);
					if (jjnewLexState[this.jjmatchedKind] != -1) {
						this.curLexState = jjnewLexState[this.jjmatchedKind];
					}
					continue EOFLoop;
				}
			}
			int error_line = this.input_stream.getEndLine();
			int error_column = this.input_stream.getEndColumn();
			String error_after = null;
			boolean EOFSeen = false;
			try {
				this.input_stream.readChar();
				this.input_stream.backup(1);
			}
			catch (java.io.IOException e1) {
				EOFSeen = true;
				error_after = curPos <= 1	? ""
											: this.input_stream.GetImage();
				if (this.curChar == '\n' || this.curChar == '\r') {
					error_line++;
					error_column = 0;
				}
				else {
					error_column++;
				}
			}
			if (!EOFSeen) {
				this.input_stream.backup(1);
				error_after = curPos <= 1	? ""
											: this.input_stream.GetImage();
			}
			throw new TokenMgrError(EOFSeen,
									this.curLexState,
									error_line,
									error_column,
									error_after,
									this.curChar,
									TokenMgrError.LEXICAL_ERROR);
		}
	}

	void SkipLexicalActions(Token matchedToken) {
		switch (this.jjmatchedKind) {
			case 79:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				if (this.parenthesisNesting > 0) {
					this.SwitchTo(IN_PAREN);
				}
				else if (this.inInvocation) {
					this.SwitchTo(NAMED_PARAMETER_EXPRESSION);
				}
				else {
					this.SwitchTo(FM_EXPRESSION);
				}
				break;
			default:
				break;
		}
	}

	void TokenLexicalActions(Token matchedToken) {
		switch (this.jjmatchedKind) {
			case 6:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 7:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 8:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 9:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 10:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 11:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 12:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 13:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 14:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 15:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 16:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 17:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 18:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 19:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 20:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 21:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 22:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 23:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 24:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 25:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 26:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 27:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 28:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										NO_PARSE);
				this.noparseTag = "comment";
				break;
			case 29:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.noparseTag = "-->";
				this.strictSyntaxCheck(	matchedToken,
										NO_PARSE);
				break;
			case 30:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										NO_PARSE);
				this.noparseTag = "noparse";
				break;
			case 31:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 32:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 33:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 34:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 35:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 36:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 37:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 38:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 39:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 40:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 41:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 42:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 43:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 44:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 45:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 46:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 47:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 48:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 49:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 50:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 51:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 52:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 53:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 54:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 55:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 56:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 57:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 58:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 59:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										FM_EXPRESSION);
				break;
			case 60:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 61:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 62:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.strictSyntaxCheck(	matchedToken,
										DEFAULT);
				break;
			case 63:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.unifiedCall(matchedToken);
				break;
			case 64:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.unifiedCallEnd(matchedToken);
				break;
			case 65:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.ftlHeader(matchedToken);
				break;
			case 66:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				this.ftlHeader(matchedToken);
				break;
			case 67:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				if (!this.directiveSyntaxEstablished) {
					matchedToken.kind = PRINTABLE_CHARS;
				}
				else {
					char firstChar = matchedToken.image.charAt(0);
					if (firstChar == '<' && this.altDirectiveSyntax) {
						matchedToken.kind = PRINTABLE_CHARS;
					}
					else if (firstChar == '[' && !this.altDirectiveSyntax) {
						matchedToken.kind = PRINTABLE_CHARS;
					}
					else if (this.strictEscapeSyntax) {
						String s = matchedToken.image;
						int index = s.indexOf('#');
						s = s.substring(index);
						String msg = "Unknown directive: "
										+ s
										+ " on line: "
										+ matchedToken.beginLine
										+ ", column: "
										+ matchedToken.beginColumn
										+ 1
										+ ", in template: "
										+ this.templateName;
						throw new TokenMgrError(msg,
												TokenMgrError.LEXICAL_ERROR);
					}
				}
				break;
			case 111:
				if (this.image == null) {
					this.image = new StringBuffer(jjstrLiteralImages[111]);
				}
				else {
					this.image.append(jjstrLiteralImages[111]);
				}
				++this.bracketNesting;
				break;
			case 112:
				if (this.image == null) {
					this.image = new StringBuffer(jjstrLiteralImages[112]);
				}
				else {
					this.image.append(jjstrLiteralImages[112]);
				}
				this.closeBracket(matchedToken);
				break;
			case 113:
				if (this.image == null) {
					this.image = new StringBuffer(jjstrLiteralImages[113]);
				}
				else {
					this.image.append(jjstrLiteralImages[113]);
				}
				++this.parenthesisNesting;
				if (this.parenthesisNesting == 1) {
					this.SwitchTo(IN_PAREN);
				}
				break;
			case 114:
				if (this.image == null) {
					this.image = new StringBuffer(jjstrLiteralImages[114]);
				}
				else {
					this.image.append(jjstrLiteralImages[114]);
				}
				--this.parenthesisNesting;
				if (this.parenthesisNesting == 0) {
					if (this.inInvocation) {
						this.SwitchTo(NAMED_PARAMETER_EXPRESSION);
					}
					else {
						this.SwitchTo(FM_EXPRESSION);
					}
				}
				break;
			case 115:
				if (this.image == null) {
					this.image = new StringBuffer(jjstrLiteralImages[115]);
				}
				else {
					this.image.append(jjstrLiteralImages[115]);
				}
				++this.hashLiteralNesting;
				break;
			case 116:
				if (this.image == null) {
					this.image = new StringBuffer(jjstrLiteralImages[116]);
				}
				else {
					this.image.append(jjstrLiteralImages[116]);
				}
				if (this.hashLiteralNesting == 0) {
					this.SwitchTo(DEFAULT);
				}
				else {
					--this.hashLiteralNesting;
				}
				break;
			case 123:
				if (this.image == null) {
					this.image = new StringBuffer(jjstrLiteralImages[123]);
				}
				else {
					this.image.append(jjstrLiteralImages[123]);
				}
				if (this.inFTLHeader) {
					this.eatNewline();
				}
				this.inFTLHeader = false;
				if (this.altDirectiveSyntax) {
					matchedToken.kind = NATURAL_GT;
				}
				else {
					this.SwitchTo(DEFAULT);
				}
				break;
			case 124:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				if (this.inFTLHeader) {
					this.eatNewline();
				}
				this.inFTLHeader = false;
				this.SwitchTo(DEFAULT);
				break;
			case 129:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				if (this.noparseTag.equals("-->")) {
					boolean squareBracket = matchedToken.image.endsWith("]");
					if ((this.altDirectiveSyntax && squareBracket)
						|| (!this.altDirectiveSyntax && !squareBracket)) {
						this.SwitchTo(DEFAULT);
					}
				}
				break;
			case 130:
				if (this.image == null) {
					this.image = new StringBuffer(new String(this.input_stream.GetSuffix(this.jjimageLen
																							+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				else {
					this.image.append(new String(this.input_stream.GetSuffix(this.jjimageLen
																				+ (this.lengthOfMatch = this.jjmatchedPos + 1))));
				}
				StringTokenizer st = new StringTokenizer(	this.image.toString(),
															" \t\n\r<>[]/#",
															false);
				if (st.nextToken().equals(this.noparseTag)) {
					this.SwitchTo(DEFAULT);
				}
				break;
			default:
				break;
		}
	}
}
