/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.util.HashSet;
import java.util.Set;

import freemarker.template.SimpleNumber;
import freemarker.template.SimpleScalar;
import freemarker.template.SimpleSequence;
import freemarker.template.TemplateCollectionModel;
import freemarker.template.TemplateException;
import freemarker.template.TemplateHashModel;
import freemarker.template.TemplateHashModelEx;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import freemarker.template.TemplateModelIterator;
import freemarker.template.TemplateNumberModel;
import freemarker.template.TemplateScalarModel;
import freemarker.template.TemplateSequenceModel;

/**
 * An operator for the + operator. Note that this is treated separately from the other 4 arithmetic operators, since + is overloaded to mean string
 * concatenation.
 * 
 * <AUTHOR> href="mailto:<EMAIL>">Jonathan Revusky</a>
 */
final class AddConcatExpression
								extends
								Expression {

	private final Expression left;

	private final Expression right;

	AddConcatExpression(Expression left,
						Expression right) {
		this.left = left;
		this.right = right;
	}

	@Override
	TemplateModel _getAsTemplateModel(Environment env) throws TemplateException {
		TemplateModel leftModel = this.left.getAsTemplateModel(env);
		TemplateModel rightModel = this.right.getAsTemplateModel(env);
		if (leftModel instanceof TemplateNumberModel && rightModel instanceof TemplateNumberModel) {
			Number first = EvaluationUtil.getNumber((TemplateNumberModel) leftModel,
													this.left,
													env);
			Number second = EvaluationUtil.getNumber(	(TemplateNumberModel) rightModel,
														this.right,
														env);
			ArithmeticEngine ae = env != null	? env.getArithmeticEngine()
												: this.getTemplate().getArithmeticEngine();
			return new SimpleNumber(ae.add(	first,
											second));
		}
		else if (leftModel instanceof TemplateSequenceModel
					&& rightModel instanceof TemplateSequenceModel) {
			return new ConcatenatedSequence((TemplateSequenceModel) leftModel,
											(TemplateSequenceModel) rightModel);
		}
		else {
			try {
				String s1 = getStringValue(	leftModel,
											this.left,
											env);
				if (s1 == null) {
					s1 = "null";
				}
				String s2 = getStringValue(	rightModel,
											this.right,
											env);
				if (s2 == null) {
					s2 = "null";
				}
				return new SimpleScalar(s1.concat(s2));
			}
			catch (NonStringException e) {
				if (leftModel instanceof TemplateHashModel
					&& rightModel instanceof TemplateHashModel) {
					if (leftModel instanceof TemplateHashModelEx
						&& rightModel instanceof TemplateHashModelEx) {
						TemplateHashModelEx leftModelEx = (TemplateHashModelEx) leftModel;
						TemplateHashModelEx rightModelEx = (TemplateHashModelEx) rightModel;
						if (leftModelEx.size() == 0) {
							return rightModelEx;
						}
						else if (rightModelEx.size() == 0) {
							return leftModelEx;
						}
						else {
							return new ConcatenatedHashEx(	leftModelEx,
															rightModelEx);
						}
					}
					else {
						return new ConcatenatedHash((TemplateHashModel) leftModel,
													(TemplateHashModel) rightModel);
					}
				}
				else {
					throw e;
				}
			}
		}
	}

	@Override
	boolean isLiteral() {
		return this.constantValue != null || (this.left.isLiteral() && this.right.isLiteral());
	}

	@Override
	Expression _deepClone(	String name,
							Expression subst) {
		return new AddConcatExpression(	this.left.deepClone(name,
															subst),
										this.right.deepClone(	name,
																subst));
	}

	@Override
	public String getCanonicalForm() {
		return this.left.getCanonicalForm() + " + " + this.right.getCanonicalForm();
	}

	private static final class ConcatenatedSequence
													implements
													TemplateSequenceModel {
		private final TemplateSequenceModel left;

		private final TemplateSequenceModel right;

		ConcatenatedSequence(	TemplateSequenceModel left,
								TemplateSequenceModel right) {
			this.left = left;
			this.right = right;
		}

		public int size() throws TemplateModelException {
			return this.left.size() + this.right.size();
		}

		public TemplateModel get(int i) throws TemplateModelException {
			int ls = this.left.size();
			return i < ls	? this.left.get(i)
							: this.right.get(i - ls);
		}
	}

	private static class ConcatenatedHash
											implements
											TemplateHashModel {
		protected final TemplateHashModel left;

		protected final TemplateHashModel right;

		ConcatenatedHash(	TemplateHashModel left,
							TemplateHashModel right) {
			this.left = left;
			this.right = right;
		}

		public TemplateModel get(String key) throws TemplateModelException {
			TemplateModel model = this.right.get(key);
			return (model != null)	? model
									: this.left.get(key);
		}

		public boolean isEmpty() throws TemplateModelException {
			return this.left.isEmpty() && this.right.isEmpty();
		}
	}

	private static final class ConcatenatedHashEx
													extends
													ConcatenatedHash
																	implements
																	TemplateHashModelEx {
		private CollectionAndSequence keys;

		private CollectionAndSequence values;

		private int size;

		ConcatenatedHashEx(	TemplateHashModelEx left,
							TemplateHashModelEx right) {
			super(	left,
					right);
		}

		public int size() throws TemplateModelException {
			this.initKeys();
			return this.size;
		}

		public TemplateCollectionModel keys() throws TemplateModelException {
			this.initKeys();
			return this.keys;
		}

		public TemplateCollectionModel values() throws TemplateModelException {
			this.initValues();
			return this.values;
		}

		private void initKeys() throws TemplateModelException {
			if (this.keys == null) {
				HashSet keySet = new HashSet();
				SimpleSequence keySeq = new SimpleSequence(32);
				addKeys(keySet,
						keySeq,
						(TemplateHashModelEx) this.left);
				addKeys(keySet,
						keySeq,
						(TemplateHashModelEx) this.right);
				this.size = keySet.size();
				this.keys = new CollectionAndSequence(keySeq);
			}
		}

		private static void addKeys(Set set,
									SimpleSequence keySeq,
									TemplateHashModelEx hash) throws TemplateModelException {
			TemplateModelIterator it = hash.keys().iterator();
			while (it.hasNext()) {
				TemplateScalarModel tsm = (TemplateScalarModel) it.next();
				if (set.add(tsm.getAsString())) {
					// The first occurence of the key decides the index;
					// this is consisten with stuff like java.util.LinkedHashSet.
					keySeq.add(tsm);
				}
			}
		}

		private void initValues() throws TemplateModelException {
			if (this.values == null) {
				SimpleSequence seq = new SimpleSequence(this.size());
				// Note: size() invokes initKeys() if needed.

				int ln = this.keys.size();
				for (int i = 0; i < ln; i++) {
					seq.add(this.get(((TemplateScalarModel) this.keys.get(i)).getAsString()));
				}
				this.values = new CollectionAndSequence(seq);
			}
		}
	}
}
