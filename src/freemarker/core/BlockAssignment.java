/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Map;

import freemarker.template.SimpleScalar;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateTransformModel;

/**
 * @version $Id: BlockAssignment.java,v 1.1 2010/09/04 12:00:50 dragon Exp $
 */
final class BlockAssignment extends TemplateElement {

    private final String varName;
    private final Expression namespaceExp;
    private final int scope;

    BlockAssignment(TemplateElement nestedBlock, String varName, int scope, Expression namespaceExp) {
        this.nestedBlock = nestedBlock;
        this.varName = varName;
        this.namespaceExp = namespaceExp;
        this.scope = scope;
    }

    void accept(Environment env) throws TemplateException, IOException {
        if (nestedBlock != null) {
            env.visit(nestedBlock, new CaptureOutput(env), null);
        } else {
			TemplateModel value = new SimpleScalar("");
			if (namespaceExp != null) {
				Environment.Namespace ns = (Environment.Namespace) namespaceExp.getAsTemplateModel(env);
				ns.put(varName, value);
 			} else if (scope == Assignment.NAMESPACE) {
				env.setVariable(varName, value);
			} else if (scope == Assignment.GLOBAL) {
				env.setGlobalVariable(varName, value);
			} else if (scope == Assignment.LOCAL) {
				env.setLocalVariable(varName, value);
			}
		}
    }

    private class CaptureOutput implements TemplateTransformModel {
        private final Environment env;
        private final Environment.Namespace fnsModel;
        
        CaptureOutput(Environment env) throws TemplateException {
            this.env = env;
            TemplateModel nsModel = null;
            if(namespaceExp != null) {
                nsModel = namespaceExp.getAsTemplateModel(env);
                if (!(nsModel instanceof Environment.Namespace)) {
                    throw new TemplateException(
                        "namespace parameter does not specify "
                        + "a namespace. It is a " 
                        + nsModel.getClass().getName(), env);
                }
            }
            fnsModel = (Environment.Namespace )nsModel; 
        }
        
        public Writer getWriter(Writer out, Map args) {
            return new StringWriter() {
                public void close() {
                    SimpleScalar result = new SimpleScalar(toString());
                    switch(scope) {
                        case Assignment.NAMESPACE: {
                            if(fnsModel != null) {
                                fnsModel.put(varName, result);
                            }
                            else {
                                env.setVariable(varName, result);
                            }
                            break;
                        }
                        case Assignment.LOCAL: {
                            env.setLocalVariable(varName, result);
                            break;
                        }
                        case Assignment.GLOBAL: {
                            env.setGlobalVariable(varName, result);
                            break;
                        }
                    }
                }
            };
        }
    }
    
    public String getCanonicalForm() {
        String key;
        switch(scope) {
            case Assignment.LOCAL: {
                key = "local";
                break;
            }
            case Assignment.GLOBAL: {
                key = "global";
                break;
            }
            default: {
                key = "assign";
                break;
            }
        }
	String block = nestedBlock == null ? "" : nestedBlock.getCanonicalForm();
        return "<#" + key + " " + varName + 
            (namespaceExp != null ? " in " + namespaceExp.getCanonicalForm() : "") 
            + ">" +block + "</#" + key + ">";
    }

    public String getDescription() {
        return "block assignment to variable: " + varName;
    }

    boolean isIgnorable() {
        return false;
    }
}

