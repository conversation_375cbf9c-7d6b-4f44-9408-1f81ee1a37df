/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.text.Collator;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.TimeZone;

import com.kskyb.broker.lang.Destroyable;
import com.kskyb.broker.lang.ObjectFinalizer;
import com.kskyb.broker.template.freemarker.model.TemplateDelegateModel;
import com.kskyb.broker.util.ArrayMap;

import freemarker.ext.beans.BeansWrapper;
import freemarker.log.Logger;
import freemarker.template.Configuration;
import freemarker.template.ObjectWrapper;
import freemarker.template.SimpleHash;
import freemarker.template.SimpleSequence;
import freemarker.template.Template;
import freemarker.template.TemplateCacheModel;
import freemarker.template.TemplateCollectionModel;
import freemarker.template.TemplateDateModel;
import freemarker.template.TemplateDirectiveBody;
import freemarker.template.TemplateDirectiveModel;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import freemarker.template.TemplateHashModel;
import freemarker.template.TemplateHashModelEx;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import freemarker.template.TemplateModelIterator;
import freemarker.template.TemplateNodeModel;
import freemarker.template.TemplateScalarModel;
import freemarker.template.TemplateSequenceModel;
import freemarker.template.TemplateTransformModel;
import freemarker.template.TransformControl;
import freemarker.template.utility.UndeclaredThrowableException;

/**
 * Object that represents the runtime environment during template processing. For every invocation of a <tt>Template.process()</tt> method, a new
 * instance of this object is created, and then discarded when <tt>process()</tt> returns. This object stores the set of temporary variables created
 * by the template, the value of settings set by the template, the reference to the data model root, etc. Everything that is needed to fulfill the
 * template processing job.
 * 
 * <p>
 * Data models that need to access the <tt>Environment</tt> object that represents the template processing on the current thread can use the
 * {@link #getCurrentEnvironment()} method.
 * 
 * <p>
 * If you need to modify or read this object before or after the <tt>process</tt> call, use
 * {@link Template#createProcessingEnvironment(Object rootMap, Writer out, ObjectWrapper wrapper)}
 * 
 * <AUTHOR> href="mailto:<EMAIL>">Jonathan Revusky</a>
 * <AUTHOR> Szegedi
 */
public final class Environment
								extends
								Configurable
											implements
											Destroyable {

	private static final ThreadLocal threadEnv = new ThreadLocal();

	private static final Logger logger = Logger.getLogger("freemarker.runtime");

	private static final Logger attemptLogger = Logger.getLogger("freemarker.runtime.attempt");

	private static final Map localizedNumberFormats = new HashMap();

	private static final Map localizedDateFormats = new HashMap();

	// Do not use this object directly; clone it first! DecimalFormat isn't
	// thread-safe.
	private static final DecimalFormat C_NUMBER_FORMAT = new DecimalFormat(	"0.################",
																			new DecimalFormatSymbols(Locale.US));
	static {
		C_NUMBER_FORMAT.setGroupingUsed(false);
		C_NUMBER_FORMAT.setDecimalSeparatorAlwaysShown(false);
	}

	private final Map<String, Object> rootMapping;

	private final TemplateHashModel rootDataModel;

	private final ArrayList elementStack = new ArrayList();

	private final ArrayList recoveredErrorStack = new ArrayList();

	private NumberFormat numberFormat;

	private Map numberFormats;

	private DateFormat timeFormat;

	private DateFormat dateFormat;

	private DateFormat dateTimeFormat;

	private Map[] dateFormats;

	private NumberFormat cNumberFormat;

	private Collator collator;

	private Writer out;

	private Macro.Context currentMacroContext;

	/**
	 * 
	 */
	private ArrayList<LocalContext> localContextStack;

	/**
	 * 
	 */
	private ArrayMap<String, TemplateModel> modelCache = null;

	/**
	 * @param key
	 * @param model
	 */
	public final void setCacheVariable(	final String key,
										final TemplateModel model) {
		if (this.modelCache == null) {
			this.modelCache = new ArrayMap<String, TemplateModel>();
		}
		this.modelCache.put(key,
							model);
	}

	/**
	 * @see com.kskyb.broker.lang.Destroyable#destroy()
	 */
	public void destroy() {
		ObjectFinalizer.finalizeMap(this.modelCache);
	}

	/**
	 * 
	 */
	private Namespace mainNamespace;

	/**
	 * 
	 */
	private Namespace currentNamespace;

	/**
	 * 
	 */
	private Namespace globalNamespace;

	/**
	 * 
	 */
	private HashMap loadedLibs;

	/**
	 * 
	 */
	private Throwable lastThrowable;

	/**
	 * 
	 */
	private TemplateModel lastReturnValue;

	/**
	 * 
	 */
	private HashMap macroToNamespaceLookup = new HashMap();

	/**
	 * 
	 */
	private TemplateNodeModel currentVisitorNode;

	/**
	 * 
	 */
	private TemplateSequenceModel nodeNamespaces;

	/**
	 * Things we keep track of for the fallback mechanism.
	 */
	private int nodeNamespaceIndex;

	/**
	 * 
	 */
	private String currentNodeName;

	/**
	 * 
	 */
	private String currentNodeNS;

	/**
	 * 
	 */
	private String cachedURLEscapingCharset;

	/**
	 * 
	 */
	private boolean urlEscapingCharsetCached;

	/**
	 * Retrieves the environment object associated with the current thread. Data model implementations that need access to the environment can call
	 * this method to obtain the environment object that represents the template processing that is currently running on the current thread.
	 */
	public static Environment getCurrentEnvironment() {
		return (Environment) threadEnv.get();
	}

	/**
	 * @param template
	 * @param rootDataModel
	 * @param out
	 */
	public Environment(	final Template template,
						final Map<String, Object> map,
						final TemplateHashModel rootDataModel,
						final Writer out) {
		super(template);
		this.globalNamespace = new Namespace(null);
		this.currentNamespace = this.mainNamespace = new Namespace(template);
		this.out = out;
		this.rootDataModel = rootDataModel;
		this.rootMapping = map;
		this.importMacros(template);
	}

	/**
	 * @return the rootMapping
	 */
	public final Map<String, Object> getRootMapping() {
		return this.rootMapping;
	}

	/**
	 * Retrieves the currently processed template.
	 */
	public Template getTemplate() {
		return (Template) this.getParent();
	}

	/**
	 * Deletes cached values that meant to be valid only during a single template execution.
	 */
	private void clearCachedValues() {
		this.numberFormats = null;
		this.numberFormat = null;
		this.dateFormats = null;
		this.collator = null;
		this.cachedURLEscapingCharset = null;
		this.urlEscapingCharsetCached = false;
	}

	/**
	 * Processes the template to which this environment belongs.
	 */
	public void process()	throws TemplateException,
							IOException {
		Object savedEnv = threadEnv.get();
		threadEnv.set(this);
		try {
			// Cached values from a previous execution are possibly outdated.
			this.clearCachedValues();
			try {
				this.doAutoImportsAndIncludes(this);
				this.visit(this.getTemplate().getRootTreeNode());
				// Do not flush if there was an exception.
				this.out.flush();
			}
			finally {
				// It's just to allow the GC to free memory...
				this.clearCachedValues();
			}
		}
		finally {
			threadEnv.set(savedEnv);
		}
	}

	private static final TemplateModel[] NO_OUT_ARGS = new TemplateModel[0];

	/**
	 * "Visit" the template element.
	 */
	void visit(TemplateElement element)	throws TemplateException,
										IOException {
		this.pushElement(element);
		try {
			element.accept(this);
		}
		catch (TemplateException te) {
			this.handleTemplateException(te);
		}
		finally {
			this.popElement();
		}
	}

	/**
	 * @param element
	 * @param directiveModel
	 * @param args
	 * @param bodyParameterNames
	 * @throws TemplateException
	 * @throws IOException
	 */
	public final void visit(final TemplateElement element,
							final TemplateDelegateModel directiveModel,
							final Map<String, String> args,
							final List bodyParameterNames)	throws TemplateException,
															IOException {
		if (element == null) {
			directiveModel.delegate(this,
									args,
									null,
									null);
			return;
		}

		final TemplateDirectiveBody nested = new TemplateDirectiveBody() {
			public void render(Writer newOut)	throws TemplateException,
												IOException {
				Writer prevOut = Environment.this.out;
				Environment.this.out = newOut;
				try {
					Environment.this.visit(element);
				}
				finally {
					Environment.this.out = prevOut;
				}
			}
		};
		final TemplateModel[] outArgs;
		if (bodyParameterNames == null || bodyParameterNames.isEmpty()) {
			outArgs = NO_OUT_ARGS;
		}
		else {
			outArgs = new TemplateModel[bodyParameterNames.size()];
		}
		if (outArgs.length > 0) {
			this.pushLocalContext(new LocalContext() {
				public TemplateModel getLocalVariable(String name) {
					if (bodyParameterNames == null) {
						return null;
					}
					int index = bodyParameterNames.indexOf(name);
					return index != -1	? outArgs[index]
										: null;
				}

				public Collection getLocalVariableNames() {
					return bodyParameterNames;
				}
			});
		}
		try {
			directiveModel.delegate(this,
									args,
									outArgs,
									nested);
		}
		finally {
			if (outArgs.length > 0) {
				this.popLocalContext();
			}
		}
	}

	/**
	 * @param element
	 * @param directiveModel
	 * @param args
	 * @param bodyParameterNames
	 * @throws TemplateException
	 * @throws IOException
	 */
	public final void visit(final TemplateElement element,
							final TemplateDirectiveModel directiveModel,
							final Map<String, TemplateModel> args,
							final List bodyParameterNames)	throws TemplateException,
															IOException {
		TemplateDirectiveBody nested;
		if (element == null) {
			nested = null;
		}
		else {
			nested = new TemplateDirectiveBody() {
				public void render(Writer newOut)	throws TemplateException,
													IOException {
					Writer prevOut = Environment.this.out;
					Environment.this.out = newOut;
					try {
						Environment.this.visit(element);
					}
					finally {
						Environment.this.out = prevOut;
					}
				}
			};
		}
		final TemplateModel[] outArgs;
		if (bodyParameterNames == null || bodyParameterNames.isEmpty()) {
			outArgs = NO_OUT_ARGS;
		}
		else {
			outArgs = new TemplateModel[bodyParameterNames.size()];
		}
		if (outArgs.length > 0) {
			this.pushLocalContext(new LocalContext() {
				public TemplateModel getLocalVariable(String name) {
					if (bodyParameterNames == null) {
						return null;
					}
					int index = bodyParameterNames.indexOf(name);
					return index != -1	? outArgs[index]
										: null;
				}

				public Collection getLocalVariableNames() {
					return bodyParameterNames;
				}
			});
		}
		try {
			directiveModel.execute(	this,
									args,
									outArgs,
									nested);
		}
		finally {
			if (outArgs.length > 0) {
				this.popLocalContext();
			}
		}
	}

	/**
	 * "Visit" the template element, passing the output through a TemplateTransformModel
	 * 
	 * @param element
	 *            the element to visit through a transform
	 * @param transform
	 *            the transform to pass the element output through
	 * @param args
	 *            optional arguments fed to the transform
	 */
	final void visit(	final TemplateElement element,
						final TemplateTransformModel transform,
						final Map<String, TemplateModel> args)	throws TemplateException,
																IOException {
		try {
			Writer tw = transform.getWriter(this.out,
											args);
			if (tw == null) {
				tw = EMPTY_BODY_WRITER;
			}
			TransformControl tc = tw instanceof TransformControl ? (TransformControl) tw
																: null;

			Writer prevOut = this.out;
			this.out = tw;
			try {
				if (tc == null || tc.onStart() != TransformControl.SKIP_BODY) {
					do {
						if (element != null) {
							this.visit(element);
						}
					} while (tc != null && tc.afterBody() == TransformControl.REPEAT_EVALUATION);
				}
			}
			catch (Throwable t) {
				try {
					if (tc != null) {
						tc.onError(t);
					}
					else {
						throw t;
					}
				}
				catch (TemplateException e) {
					throw e;
				}
				catch (IOException e) {
					throw e;
				}
				catch (RuntimeException e) {
					throw e;
				}
				catch (Error e) {
					throw e;
				}
				catch (Throwable e) {
					throw new UndeclaredThrowableException(e);
				}
			}
			finally {
				this.out = prevOut;
				tw.close();
			}
		}
		catch (TemplateException te) {
			this.handleTemplateException(te);
		}
	}

	/**
	 * Visit a block using buffering/recovery
	 */

	void visit(	TemplateElement attemptBlock,
				TemplateElement recoveryBlock)	throws TemplateException,
												IOException {
		Writer prevOut = this.out;
		StringWriter sw = new StringWriter();
		this.out = sw;
		TemplateException thrownException = null;
		try {
			this.visit(attemptBlock);
		}
		catch (TemplateException te) {
			thrownException = te;
		}
		finally {
			this.out = prevOut;
		}
		if (thrownException != null) {
			if (attemptLogger.isDebugEnabled()) {
				attemptLogger.debug("Error in attempt block " + attemptBlock.getStartLocation(),
									thrownException);
			}
			try {
				this.recoveredErrorStack.add(thrownException.getMessage());
				this.visit(recoveryBlock);
			}
			finally {
				this.recoveredErrorStack.remove(this.recoveredErrorStack.size() - 1);
			}
		}
		else {
			this.out.write(sw.toString());
		}
	}

	void visit(BodyInstruction.Context bctxt)	throws TemplateException,
												IOException {
		Macro.Context invokingMacroContext = this.getCurrentMacroContext();
		ArrayList prevLocalContextStack = this.localContextStack;
		TemplateElement body = invokingMacroContext.body;
		if (body != null) {
			this.currentMacroContext = invokingMacroContext.prevMacroContext;
			this.currentNamespace = invokingMacroContext.bodyNamespace;
			Configurable prevParent = this.getParent();
			this.setParent(this.currentNamespace.getTemplate());
			this.localContextStack = invokingMacroContext.prevLocalContextStack;
			if (invokingMacroContext.bodyParameterNames != null) {
				this.pushLocalContext(bctxt);
			}
			try {
				this.visit(body);
			}
			finally {
				if (invokingMacroContext.bodyParameterNames != null) {
					this.popLocalContext();
				}
				this.currentMacroContext = invokingMacroContext;
				this.currentNamespace = this.getMacroNamespace(invokingMacroContext.getMacro());
				this.setParent(prevParent);
				this.localContextStack = prevLocalContextStack;
			}
		}
	}

	/**
	 * "visit" an IteratorBlock
	 */
	void visit(IteratorBlock.Context ictxt)	throws TemplateException,
											IOException {
		this.pushLocalContext(ictxt);
		try {
			ictxt.runLoop(this);
		}
		catch (BreakInstruction.Break br) {
		}
		catch (TemplateException te) {
			this.handleTemplateException(te);
		}
		finally {
			this.popLocalContext();
		}
	}

	/**
	 * "Visit" A TemplateNodeModel
	 */

	void visit(	TemplateNodeModel node,
				TemplateSequenceModel namespaces)	throws TemplateException,
													IOException {
		if (this.nodeNamespaces == null) {
			SimpleSequence ss = new SimpleSequence(1);
			ss.add(this.currentNamespace);
			this.nodeNamespaces = ss;
		}
		int prevNodeNamespaceIndex = this.nodeNamespaceIndex;
		String prevNodeName = this.currentNodeName;
		String prevNodeNS = this.currentNodeNS;
		TemplateSequenceModel prevNodeNamespaces = this.nodeNamespaces;
		TemplateNodeModel prevVisitorNode = this.currentVisitorNode;
		this.currentVisitorNode = node;
		if (namespaces != null) {
			this.nodeNamespaces = namespaces;
		}
		try {
			TemplateModel macroOrTransform = this.getNodeProcessor(node);
			if (macroOrTransform instanceof Macro) {
				this.visit(	(Macro) macroOrTransform,
							null,
							null,
							null,
							null);
			}
			else if (macroOrTransform instanceof TemplateTransformModel) {
				this.visit(	null,
							(TemplateTransformModel) macroOrTransform,
							null);
			}
			else {
				String nodeType = node.getNodeType();
				if (nodeType != null) {
					// If the node's type is 'text', we just output it.
					if ((nodeType.equals("text") && node instanceof TemplateScalarModel)) {
						this.out.write(((TemplateScalarModel) node).getAsString());
					}
					else if (nodeType.equals("document")) {
						this.recurse(	node,
										namespaces);
					}
					// We complain here, unless the node's type is 'pi', or "comment" or "document_type", in which case
					// we just ignore it.
					else if (!nodeType.equals("pi") && !nodeType.equals("comment") && !nodeType.equals("document_type")) {
						String nsBit = "";
						String ns = node.getNodeNamespace();
						if (ns != null) {
							if (ns.length() > 0) {
								nsBit = " and namespace " + ns;
							}
							else {
								nsBit = " and no namespace";
							}
						}
						throw new TemplateException("No macro or transform defined for node named "
														+ node.getNodeName()
														+ nsBit
														+ ", and there is no fallback handler called @"
														+ nodeType
														+ " either.",
													this);
					}
				}
				else {
					String nsBit = "";
					String ns = node.getNodeNamespace();
					if (ns != null) {
						if (ns.length() > 0) {
							nsBit = " and namespace " + ns;
						}
						else {
							nsBit = " and no namespace";
						}
					}
					throw new TemplateException("No macro or transform defined for node with name "
													+ node.getNodeName()
													+ nsBit
													+ ", and there is no macro or transform called @default either.",
												this);
				}
			}
		}
		finally {
			this.currentVisitorNode = prevVisitorNode;
			this.nodeNamespaceIndex = prevNodeNamespaceIndex;
			this.currentNodeName = prevNodeName;
			this.currentNodeNS = prevNodeNS;
			this.nodeNamespaces = prevNodeNamespaces;
		}
	}

	/**
	 * "visit" a macro.
	 */

	void visit(	Macro macro,
				Map namedArgs,
				List positionalArgs,
				List bodyParameterNames,
				TemplateElement nestedBlock) throws TemplateException,
											IOException {
		if (macro == Macro.DO_NOTHING_MACRO) {
			return;
		}
		this.pushElement(macro);
		try {
			Macro.Context previousMacroContext = this.currentMacroContext;
			Macro.Context mc = macro.new Context(	this,
													nestedBlock,
													bodyParameterNames);

			String catchAll = macro.getCatchAll();
			TemplateModel unknownVars = null;

			if (namedArgs != null) {
				if (catchAll != null) {
					unknownVars = new SimpleHash();
				}
				for (Iterator it = namedArgs.entrySet().iterator(); it.hasNext();) {
					Map.Entry entry = (Map.Entry) it.next();
					String varName = (String) entry.getKey();
					boolean hasVar = macro.hasArgNamed(varName);
					if (hasVar || catchAll != null) {
						Expression arg = (Expression) entry.getValue();
						TemplateModel value = arg.getAsTemplateModel(this);
						if (hasVar) {
							mc.setLocalVar(	varName,
											value);
						}
						else if (unknownVars != null && unknownVars instanceof SimpleHash) {
							// TODO : check 추가 조건이 항상 true일까?
							((SimpleHash) unknownVars).put(	varName,
															value);
						}
					}
					else {
						String msg = "Macro " + macro.getName() + " has no such argument: " + varName;
						throw new TemplateException(msg,
													this);
					}
				}
			}
			else if (positionalArgs != null) {
				if (catchAll != null) {
					unknownVars = new SimpleSequence();
				}
				String[] argumentNames = macro.getArgumentNamesInternal();
				int size = positionalArgs.size();
				if (argumentNames.length < size && catchAll == null) {
					throw new TemplateException("Macro " + macro.getName() + " only accepts " + argumentNames.length + " parameters.",
												this);
				}
				for (int i = 0; i < size; i++) {
					Expression argExp = (Expression) positionalArgs.get(i);
					TemplateModel argModel = argExp.getAsTemplateModel(this);
					try {
						if (i < argumentNames.length) {
							String argName = argumentNames[i];
							mc.setLocalVar(	argName,
											argModel);
						}
						else if (unknownVars != null && unknownVars instanceof SimpleHash) {
							// TODO : check 추가 조건이 항상 true일까?
							((SimpleSequence) unknownVars).add(argModel);
						}
					}
					catch (RuntimeException re) {
						throw new TemplateException(re,
													this);
					}
				}
			}
			if (catchAll != null) {
				mc.setLocalVar(	catchAll,
								unknownVars);
			}
			ArrayList prevLocalContextStack = this.localContextStack;
			this.localContextStack = null;
			Namespace prevNamespace = this.currentNamespace;
			Configurable prevParent = this.getParent();
			this.currentNamespace = (Namespace) this.macroToNamespaceLookup.get(macro);
			this.currentMacroContext = mc;
			try {
				mc.runMacro(this);
			}
			catch (ReturnInstruction.Return re) {
			}
			catch (TemplateException te) {
				this.handleTemplateException(te);
			}
			finally {
				this.currentMacroContext = previousMacroContext;
				this.localContextStack = prevLocalContextStack;
				this.currentNamespace = prevNamespace;
				this.setParent(prevParent);
			}
		}
		finally {
			this.popElement();
		}
	}

	String getCurrentRecoveredErrorMesssage() throws TemplateException {
		if (this.recoveredErrorStack.isEmpty()) {
			throw new TemplateException(".error is not available outside of a <#recover> block",
										this);
		}
		return (String) this.recoveredErrorStack.get(this.recoveredErrorStack.size() - 1);
	}

	void fallback()	throws TemplateException,
					IOException {
		TemplateModel macroOrTransform = this.getNodeProcessor(	this.currentNodeName,
																this.currentNodeNS,
																this.nodeNamespaceIndex);
		if (macroOrTransform instanceof Macro) {
			this.visit(	(Macro) macroOrTransform,
						null,
						null,
						null,
						null);
		}
		else if (macroOrTransform instanceof TemplateTransformModel) {
			this.visit(	null,
						(TemplateTransformModel) macroOrTransform,
						null);
		}
	}

	void visitMacroDef(Macro macro) {
		this.macroToNamespaceLookup.put(macro,
										this.currentNamespace);
		this.currentNamespace.put(	macro.getName(),
									macro);
	}

	Namespace getMacroNamespace(Macro macro) {
		return (Namespace) this.macroToNamespaceLookup.get(macro);
	}

	void recurse(	TemplateNodeModel node,
					TemplateSequenceModel namespaces)	throws TemplateException,
														IOException {
		if (node == null) {
			node = this.getCurrentVisitorNode();
			if (node == null) {
				throw new TemplateModelException("The target node of recursion is missing or null.");
			}
		}
		TemplateSequenceModel children = node.getChildNodes();
		if (children == null) {
			return;
		}
		for (int i = 0; i < children.size(); i++) {
			TemplateNodeModel child = (TemplateNodeModel) children.get(i);
			if (child != null) {
				this.visit(	child,
							namespaces);
			}
		}
	}

	Macro.Context getCurrentMacroContext() {
		return this.currentMacroContext;
	}

	private void handleTemplateException(TemplateException te) throws TemplateException {
		// Logic to prevent double-handling of the exception in
		// nested visit() calls.
		if (this.lastThrowable == te) {
			throw te;
		}
		this.lastThrowable = te;

		// Log the exception
		if (logger.isErrorEnabled()) {
			logger.error(	te.getMessage(),
							te);
		}

		// Stop exception is not passed to the handler, but
		// explicitly rethrown.
		if (te instanceof StopException) {
			throw te;
		}

		// Finally, pass the exception to the handler
		this.getTemplateExceptionHandler().handleTemplateException(	te,
																	this,
																	this.out);
	}

	@Override
	public void setTemplateExceptionHandler(TemplateExceptionHandler templateExceptionHandler) {
		super.setTemplateExceptionHandler(templateExceptionHandler);
		this.lastThrowable = null;
	}

	@Override
	public void setLocale(Locale locale) {
		super.setLocale(locale);
		// Clear local format cache
		this.numberFormats = null;
		this.numberFormat = null;

		this.dateFormats = null;
		this.timeFormat = this.dateFormat = this.dateTimeFormat = null;

		this.collator = null;
	}

	@Override
	public void setTimeZone(TimeZone timeZone) {
		super.setTimeZone(timeZone);
		// Clear local date format cache
		this.dateFormats = null;
		this.timeFormat = this.dateFormat = this.dateTimeFormat = null;
	}

	@Override
	public void setURLEscapingCharset(String urlEscapingCharset) {
		this.urlEscapingCharsetCached = false;
		super.setURLEscapingCharset(urlEscapingCharset);
	}

	/*
	 * Note that altough it is not allowed to set this setting with the <tt>setting</tt> directive, it still must be allowed to set it from Java code
	 * while the template executes, since some frameworks allow templates to actually change the output encoding on-the-fly.
	 */
	@Override
	public void setOutputEncoding(String outputEncoding) {
		this.urlEscapingCharsetCached = false;
		super.setOutputEncoding(outputEncoding);
	}

	/**
	 * Returns the name of the charset that should be used for URL encoding. This will be <code>null</code> if the information is not available. The
	 * function caches the return value, so it is quick to call it repeately.
	 */
	String getEffectiveURLEscapingCharset() {
		if (!this.urlEscapingCharsetCached) {
			this.cachedURLEscapingCharset = this.getURLEscapingCharset();
			if (this.cachedURLEscapingCharset == null) {
				this.cachedURLEscapingCharset = this.getOutputEncoding();
			}
			this.urlEscapingCharsetCached = true;
		}
		return this.cachedURLEscapingCharset;
	}

	Collator getCollator() {
		if (this.collator == null) {
			this.collator = Collator.getInstance(this.getLocale());
		}
		return this.collator;
	}

	public void setOut(Writer out) {
		this.out = out;
	}

	public Writer getOut() {
		return this.out;
	}

	String formatNumber(Number number) {
		if (this.numberFormat == null) {
			this.numberFormat = this.getNumberFormatObject(this.getNumberFormat());
		}
		return this.numberFormat.format(number);
	}

	@Override
	public void setNumberFormat(String formatName) {
		super.setNumberFormat(formatName);
		this.numberFormat = null;
	}

	String formatDate(	Date date,
						int type) throws TemplateModelException {
		DateFormat df = this.getDateFormatObject(type);
		if (df == null) {
			throw new TemplateModelException("Can't convert the date to string, because it is not known which parts of the date variable are in use. Use ?date, ?time or ?datetime built-in, or ?string.<format> or ?string(format) built-in with this date.");
		}
		return df.format(date);
	}

	@Override
	public void setTimeFormat(String formatName) {
		super.setTimeFormat(formatName);
		this.timeFormat = null;
	}

	@Override
	public void setDateFormat(String formatName) {
		super.setDateFormat(formatName);
		this.dateFormat = null;
	}

	@Override
	public void setDateTimeFormat(String formatName) {
		super.setDateTimeFormat(formatName);
		this.dateTimeFormat = null;
	}

	public Configuration getConfiguration() {
		return this.getTemplate().getConfiguration();
	}

	TemplateModel getLastReturnValue() {
		return this.lastReturnValue;
	}

	void setLastReturnValue(TemplateModel lastReturnValue) {
		this.lastReturnValue = lastReturnValue;
	}

	void clearLastReturnValue() {
		this.lastReturnValue = null;
	}

	NumberFormat getNumberFormatObject(String pattern) {
		if (this.numberFormats == null) {
			this.numberFormats = new HashMap();
		}

		NumberFormat format = (NumberFormat) this.numberFormats.get(pattern);
		if (format != null) {
			return format;
		}

		// Get format from global format cache
		synchronized (localizedNumberFormats) {
			Locale locale = this.getLocale();
			NumberFormatKey fk = new NumberFormatKey(	pattern,
														locale);
			format = (NumberFormat) localizedNumberFormats.get(fk);
			if (format == null) {
				// Add format to global format cache. Note this is
				// globally done once per locale per pattern.
				if ("number".equals(pattern)) {
					format = NumberFormat.getNumberInstance(locale);
				}
				else if ("currency".equals(pattern)) {
					format = NumberFormat.getCurrencyInstance(locale);
				}
				else if ("percent".equals(pattern)) {
					format = NumberFormat.getPercentInstance(locale);
				}
				else if ("computer".equals(pattern)) {
					format = this.getCNumberFormat();
				}
				else {
					format = new DecimalFormat(	pattern,
												new DecimalFormatSymbols(this.getLocale()));
				}
				localizedNumberFormats.put(	fk,
											format);
			}
		}

		// Clone it and store the clone in the local cache
		format = (NumberFormat) format.clone();
		this.numberFormats.put(	pattern,
								format);
		return format;
	}

	DateFormat getDateFormatObject(int dateType) throws TemplateModelException {
		switch (dateType) {
			case TemplateDateModel.UNKNOWN: {
				return null;
			}
			case TemplateDateModel.TIME: {
				if (this.timeFormat == null) {
					this.timeFormat = this.getDateFormatObject(	dateType,
																this.getTimeFormat());
				}
				return this.timeFormat;
			}
			case TemplateDateModel.DATE: {
				if (this.dateFormat == null) {
					this.dateFormat = this.getDateFormatObject(	dateType,
																this.getDateFormat());
				}
				return this.dateFormat;
			}
			case TemplateDateModel.DATETIME: {
				if (this.dateTimeFormat == null) {
					this.dateTimeFormat = this.getDateFormatObject(	dateType,
																	this.getDateTimeFormat());
				}
				return this.dateTimeFormat;
			}
			default: {
				throw new TemplateModelException("Unrecognized date type " + dateType);
			}
		}
	}

	DateFormat getDateFormatObject(	int dateType,
									String pattern) throws TemplateModelException {
		if (this.dateFormats == null) {
			this.dateFormats = new Map[4];
			this.dateFormats[TemplateDateModel.UNKNOWN] = new HashMap();
			this.dateFormats[TemplateDateModel.TIME] = new HashMap();
			this.dateFormats[TemplateDateModel.DATE] = new HashMap();
			this.dateFormats[TemplateDateModel.DATETIME] = new HashMap();
		}
		Map typedDateFormat = this.dateFormats[dateType];

		DateFormat format = (DateFormat) typedDateFormat.get(pattern);
		if (format != null) {
			return format;
		}

		// Get format from global format cache
		synchronized (localizedDateFormats) {
			Locale locale = this.getLocale();
			TimeZone timeZone = this.getTimeZone();
			DateFormatKey fk = new DateFormatKey(	dateType,
													pattern,
													locale,
													timeZone);
			format = (DateFormat) localizedDateFormats.get(fk);
			if (format == null) {
				// Add format to global format cache. Note this is
				// globally done once per locale per pattern.
				StringTokenizer tok = new StringTokenizer(	pattern,
															"_");
				int style = tok.hasMoreTokens()	? this.parseDateStyleToken(tok.nextToken())
												: DateFormat.DEFAULT;
				if (style != -1) {
					switch (dateType) {
						case TemplateDateModel.UNKNOWN: {
							throw new TemplateModelException("Can't convert the date to string using a "
																+ "built-in format, because it is not known which "
																+ "parts of the date variable are in use. Use "
																+ "?date, ?time or ?datetime built-in, or "
																+ "?string.<format> or ?string(<format>) built-in "
																+ "with explicit formatting pattern with this date.");
						}
						case TemplateDateModel.TIME: {
							format = DateFormat.getTimeInstance(style,
																locale);
							break;
						}
						case TemplateDateModel.DATE: {
							format = DateFormat.getDateInstance(style,
																locale);
							break;
						}
						case TemplateDateModel.DATETIME: {
							int timestyle = tok.hasMoreTokens()	? this.parseDateStyleToken(tok.nextToken())
																: style;
							if (timestyle != -1) {
								format = DateFormat.getDateTimeInstance(style,
																		timestyle,
																		locale);
							}
							break;
						}
					}
				}
				if (format == null) {
					try {
						format = new SimpleDateFormat(	pattern,
														locale);
					}
					catch (IllegalArgumentException e) {
						throw new TemplateModelException(	"Can't parse " + pattern + " to a date format.",
															e);
					}
				}
				format.setTimeZone(timeZone);
				localizedDateFormats.put(	fk,
											format);
			}
		}

		// Clone it and store the clone in the local cache
		format = (DateFormat) format.clone();
		typedDateFormat.put(pattern,
							format);
		return format;
	}

	int parseDateStyleToken(String token) {
		if ("short".equals(token)) {
			return DateFormat.SHORT;
		}
		if ("medium".equals(token)) {
			return DateFormat.MEDIUM;
		}
		if ("long".equals(token)) {
			return DateFormat.LONG;
		}
		if ("full".equals(token)) {
			return DateFormat.FULL;
		}
		return -1;
	}

	/**
	 * Returns the {@link NumberFormat} used for the <tt>c</tt> built-in. This is always US English <code>"0.################"</code>, without
	 * grouping and without superfluous decimal separator.
	 */
	public NumberFormat getCNumberFormat() {
		// It can't be cached in a static field, because DecimalFormat-s aren't
		// thread-safe.
		if (this.cNumberFormat == null) {
			this.cNumberFormat = (DecimalFormat) C_NUMBER_FORMAT.clone();
		}
		return this.cNumberFormat;
	}

	TemplateTransformModel getTransform(Expression exp) throws TemplateException {
		TemplateTransformModel ttm = null;
		TemplateModel tm = exp.getAsTemplateModel(this);
		if (tm instanceof TemplateTransformModel) {
			ttm = (TemplateTransformModel) tm;
		}
		else if (exp instanceof Identifier) {
			tm = this.getConfiguration().getSharedVariable(exp.toString());
			if (tm instanceof TemplateTransformModel) {
				ttm = (TemplateTransformModel) tm;
			}
		}
		return ttm;
	}

	/**
	 * Returns the loop or macro local variable corresponding to this variable name. Possibly null. (Note that the misnomer is kept for backward
	 * compatibility: loop variables are not local variables according to our terminology.)
	 */
	public TemplateModel getLocalVariable(String name) throws TemplateModelException {
		if (this.localContextStack != null) {
			for (int i = this.localContextStack.size() - 1; i >= 0; i--) {
				LocalContext lc = this.localContextStack.get(i);
				TemplateModel tm = lc.getLocalVariable(name);
				if (tm != null) {
					return tm;
				}
			}
		}
		return this.currentMacroContext == null	? null
												: this.currentMacroContext.getLocalVariable(name);
	}

	/**
	 * Returns the variable that is visible in this context. This is the correspondent to an FTL top-level variable reading expression. That is, it
	 * tries to find the the variable in this order:
	 * <ol>
	 * <li>An loop variable (if we're in a loop or user defined directive body) such as foo_has_next
	 * <li>A local variable (if we're in a macro)
	 * <li>A variable defined in the current namespace (say, via &lt;#assign ...&gt;)
	 * <li>A variable defined globally (say, via &lt;#global ....&gt;)
	 * <li>Variable in the data model:
	 * <ol>
	 * <li>A variable in the root hash that was exposed to this rendering environment in the Template.process(...) call
	 * <li>A shared variable set in the configuration via a call to Configuration.setSharedVariable(...)
	 * </ol>
	 * </li>
	 * </ol>
	 */
	public TemplateModel getVariable(String name) throws TemplateModelException {
		TemplateModel result = this.modelCache == null	? null
														: this.modelCache.get(name);

		if (result != null) {
			return result;
		}

		result = this.getLocalVariable(name);
		if (result == null) {
			result = this.currentNamespace.get(name);
		}
		if (result == null) {
			result = this.getGlobalVariable(name);
		}

		if (result != null && result instanceof TemplateCacheModel) {
			if (this.modelCache == null) {
				this.modelCache = new ArrayMap<String, TemplateModel>();
			}
			this.modelCache.put(name,
								result);
		}

		return result;
	}

	/**
	 * Returns the globally visible variable of the given name (or null). This is correspondent to FTL <code>.globals.<i>name</i></code>. This will
	 * first look at variables that were assigned globally via: &lt;#global ...&gt; and then at the data model exposed to the template.
	 */
	public TemplateModel getGlobalVariable(String name) throws TemplateModelException {
		TemplateModel result = this.globalNamespace.get(name);
		if (result == null) {
			result = this.rootDataModel.get(name);
		}
		if (result == null) {
			result = this.getConfiguration().getSharedVariable(name);
		}
		return result;
	}

	/**
	 * Sets a variable that is visible globally. This is correspondent to FTL <code><#global <i>name</i>=<i>model</i>></code>. This can be considered
	 * a convenient shorthand for: getGlobalNamespace().put(name, model)
	 */
	public void setGlobalVariable(	String name,
									TemplateModel model) {
		this.globalNamespace.put(	name,
									model);
	}

	/**
	 * Sets a variable in the current namespace. This is correspondent to FTL <code><#assign <i>name</i>=<i>model</i>></code>. This can be considered
	 * a convenient shorthand for: getCurrentNamespace().put(name, model)
	 */
	public void setVariable(String name,
							TemplateModel model) {
		this.currentNamespace.put(	name,
									model);
	}

	/**
	 * Sets a local variable (one effective only during a macro invocation). This is correspondent to FTL
	 * <code><#local <i>name</i>=<i>model</i>></code>.
	 * 
	 * @param name
	 *            the identifier of the variable
	 * @param model
	 *            the value of the variable.
	 * @throws IllegalStateException
	 *             if the environment is not executing a macro body.
	 */
	public void setLocalVariable(	String name,
									TemplateModel model) {
		if (this.currentMacroContext == null) {
			throw new IllegalStateException("Not executing macro body");
		}
		this.currentMacroContext.setLocalVar(	name,
												model);
	}

	/**
	 * Returns a set of variable names that are known at the time of call. This includes names of all shared variables in the {@link Configuration},
	 * names of all global variables that were assigned during the template processing, names of all variables in the current name-space, names of all
	 * local variables and loop variables. If the passed root data model implements the {@link TemplateHashModelEx} interface, then all names it
	 * retrieves through a call to {@link TemplateHashModelEx#keys()} method are returned as well. The method returns a new Set object on each call
	 * that is completely disconnected from the Environment. That is, modifying the set will have no effect on the Environment object.
	 */
	public Set getKnownVariableNames() throws TemplateModelException {
		// shared vars.
		Set set = this.getConfiguration().getSharedVariableNames();

		// root hash
		if (this.rootDataModel instanceof TemplateHashModelEx) {
			TemplateModelIterator rootNames = ((TemplateHashModelEx) this.rootDataModel).keys().iterator();
			while (rootNames.hasNext()) {
				set.add(((TemplateScalarModel) rootNames.next()).getAsString());
			}
		}

		// globals
		for (TemplateModelIterator tmi = this.globalNamespace.keys().iterator(); tmi.hasNext();) {
			set.add(((TemplateScalarModel) tmi.next()).getAsString());
		}

		// current name-space
		for (TemplateModelIterator tmi = this.currentNamespace.keys().iterator(); tmi.hasNext();) {
			set.add(((TemplateScalarModel) tmi.next()).getAsString());
		}

		// locals and loop vars
		if (this.currentMacroContext != null) {
			set.addAll(this.currentMacroContext.getLocalVariableNames());
		}
		if (this.localContextStack != null) {
			for (int i = this.localContextStack.size() - 1; i >= 0; i--) {
				LocalContext lc = this.localContextStack.get(i);
				set.addAll(lc.getLocalVariableNames());
			}
		}
		return set;
	}

	/**
	 * Outputs the instruction stack. Useful for debugging. {@link TemplateException}s incorporate this information in their stack traces.
	 */
	public void outputInstructionStack(PrintWriter pw) {
		pw.println("----------");
		ListIterator iter = this.elementStack.listIterator(this.elementStack.size());
		if (iter.hasPrevious()) {
			pw.print("==> ");
			TemplateElement prev = (TemplateElement) iter.previous();
			pw.print(prev.getDescription());
			pw.print(" [");
			pw.print(prev.getStartLocation());
			pw.println("]");
		}
		while (iter.hasPrevious()) {
			TemplateElement prev = (TemplateElement) iter.previous();
			if (prev instanceof UnifiedCall || prev instanceof Include) {
				String location = prev.getDescription() + " [" + prev.getStartLocation() + "]";
				if (location != null && location.length() > 0) {
					pw.print(" in ");
					pw.println(location);
				}
			}
		}
		pw.println("----------");
		pw.flush();
	}

	private void pushLocalContext(LocalContext localContext) {
		if (this.localContextStack == null) {
			this.localContextStack = new ArrayList();
		}
		this.localContextStack.add(localContext);
	}

	private void popLocalContext() {
		this.localContextStack.remove(this.localContextStack.size() - 1);
	}

	ArrayList getLocalContextStack() {
		return this.localContextStack;
	}

	/**
	 * Returns the name-space for the name if exists, or null.
	 * 
	 * @param name
	 *            the template path that you have used with the <code>import</code> directive or {@link #importLib(String, String)} call, in
	 *            normalized form. That is, the path must be an absolute path, and it must not contain "/../" or "/./". The leading "/" is optional.
	 */
	public Namespace getNamespace(String name) {
		if (name.startsWith("/")) {
			name = name.substring(1);
		}
		if (this.loadedLibs != null) {
			return (Namespace) this.loadedLibs.get(name);
		}
		else {
			return null;
		}
	}

	/**
	 * Returns the main name-space. This is correspondent of FTL <code>.main</code> hash.
	 */
	public Namespace getMainNamespace() {
		return this.mainNamespace;
	}

	/**
	 * Returns the main name-space. This is correspondent of FTL <code>.namespace</code> hash.
	 */
	public Namespace getCurrentNamespace() {
		return this.currentNamespace;
	}

	/**
	 * Returns a fictitious name-space that contains the globally visible variables that were created in the template, but not the variables of the
	 * data-model. There is no such thing in FTL; this strange method was added because of the JSP taglib support, since this imaginary name-space
	 * contains the page-scope attributes.
	 */
	public Namespace getGlobalNamespace() {
		return this.globalNamespace;
	}

	public TemplateHashModel getDataModel() {
		final TemplateHashModel result = new TemplateHashModel() {
			public boolean isEmpty() {
				return false;
			}

			public TemplateModel get(String key) throws TemplateModelException {
				TemplateModel value = Environment.this.rootDataModel.get(key);
				if (value == null) {
					value = Environment.this.getConfiguration().getSharedVariable(key);
				}
				return value;
			}
		};

		if (this.rootDataModel instanceof TemplateHashModelEx) {
			return new TemplateHashModelEx() {
				public boolean isEmpty() throws TemplateModelException {
					return result.isEmpty();
				}

				public TemplateModel get(String key) throws TemplateModelException {
					return result.get(key);
				}

				// NB: The methods below do not take into account
				// configuration shared variables even though
				// the hash will return them, if only for BWC reasons
				public TemplateCollectionModel values() throws TemplateModelException {
					return ((TemplateHashModelEx) Environment.this.rootDataModel).values();
				}

				public TemplateCollectionModel keys() throws TemplateModelException {
					return ((TemplateHashModelEx) Environment.this.rootDataModel).keys();
				}

				public int size() throws TemplateModelException {
					return ((TemplateHashModelEx) Environment.this.rootDataModel).size();
				}
			};
		}
		return result;
	}

	/**
	 * Returns the read-only hash of globally visible variables. This is the correspondent of FTL <code>.globals</code> hash. That is, you see the
	 * variables created with <code>&lt;#global ...></code>, and the variables of the data-model. To create new global variables, use
	 * {@link #setGlobalVariable setGlobalVariable}.
	 */
	public TemplateHashModel getGlobalVariables() {
		return new TemplateHashModel() {
			public boolean isEmpty() {
				return false;
			}

			public TemplateModel get(String key) throws TemplateModelException {
				TemplateModel result = Environment.this.globalNamespace.get(key);
				if (result == null) {
					result = Environment.this.rootDataModel.get(key);
				}
				if (result == null) {
					result = Environment.this.getConfiguration().getSharedVariable(key);
				}
				return result;
			}
		};
	}

	private void pushElement(TemplateElement element) {
		this.elementStack.add(element);
	}

	private void popElement() {
		this.elementStack.remove(this.elementStack.size() - 1);
	}

	public TemplateNodeModel getCurrentVisitorNode() {
		return this.currentVisitorNode;
	}

	/**
	 * sets TemplateNodeModel as the current visitor node. <tt>.current_node</tt>
	 */
	public void setCurrentVisitorNode(TemplateNodeModel node) {
		this.currentVisitorNode = node;
	}

	TemplateModel getNodeProcessor(TemplateNodeModel node) throws TemplateException {
		String nodeName = node.getNodeName();
		if (nodeName == null) {
			throw new TemplateException("Node name is null.",
										this);
		}
		TemplateModel result = this.getNodeProcessor(	nodeName,
														node.getNodeNamespace(),
														0);

		if (result == null) {
			String type = node.getNodeType();

			/* DD: Original version: */
			if (type == null) {
				type = "default";
			}
			result = this.getNodeProcessor(	"@" + type,
											null,
											0);

			/*
			 * DD: Jonathan's non-BC version and IMHO otherwise wrong version: if (type != null) { result = getNodeProcessor("@" + type, null, 0); }
			 * if (result == null) { result = getNodeProcessor("@default", null, 0); }
			 */
		}
		return result;
	}

	private TemplateModel getNodeProcessor(	final String nodeName,
											final String nsURI,
											int startIndex) throws TemplateException {
		TemplateModel result = null;
		int i;
		for (i = startIndex; i < this.nodeNamespaces.size(); i++) {
			Namespace ns = null;
			try {
				ns = (Namespace) this.nodeNamespaces.get(i);
			}
			catch (ClassCastException cce) {
				throw new InvalidReferenceException("A using clause should contain a sequence of namespaces or strings that indicate the location of importable macro libraries.",
													this);
			}
			result = this.getNodeProcessor(	ns,
											nodeName,
											nsURI);
			if (result != null) {
				break;
			}
		}
		if (result != null) {
			this.nodeNamespaceIndex = i + 1;
			this.currentNodeName = nodeName;
			this.currentNodeNS = nsURI;
		}
		return result;
	}

	private TemplateModel getNodeProcessor(	Namespace ns,
											String localName,
											String nsURI) throws TemplateException {
		TemplateModel result = null;
		if (nsURI == null) {
			result = ns.get(localName);
			if (!(result instanceof Macro) && !(result instanceof TemplateTransformModel)) {
				result = null;
			}
		}
		else {
			Template template = ns.getTemplate();
			String prefix = template.getPrefixForNamespace(nsURI);
			if (prefix == null) {
				// The other template cannot handle this node
				// since it has no prefix registered for the namespace
				return null;
			}
			if (prefix.length() > 0) {
				result = ns.get(prefix + ":" + localName);
				if (!(result instanceof Macro) && !(result instanceof TemplateTransformModel)) {
					result = null;
				}
			}
			else {
				if (nsURI.length() == 0) {
					result = ns.get(Template.NO_NS_PREFIX + ":" + localName);
					if (!(result instanceof Macro) && !(result instanceof TemplateTransformModel)) {
						result = null;
					}
				}
				if (nsURI.equals(template.getDefaultNS())) {
					result = ns.get(Template.DEFAULT_NAMESPACE_PREFIX + ":" + localName);
					if (!(result instanceof Macro) && !(result instanceof TemplateTransformModel)) {
						result = null;
					}
				}
				if (result == null) {
					result = ns.get(localName);
					if (!(result instanceof Macro) && !(result instanceof TemplateTransformModel)) {
						result = null;
					}
				}
			}
		}
		return result;
	}

	/**
	 * Emulates <code>include</code> directive, except that <code>name</code> must be tempate root relative.
	 * 
	 * <p>
	 * It's the same as <code>include(getTemplateForInclusion(name, encoding, parse))</code>. But, you may want to separately call these two methods,
	 * so you can determine the source of exceptions more precisely, and thus achieve more intelligent error handling.
	 * 
	 * @see #getTemplateForInclusion(String name, String encoding, boolean parse)
	 * @see #include(Template includedTemplate)
	 */
	public void include(String name,
						String encoding,
						boolean parse)	throws IOException,
										TemplateException {
		this.include(this.getTemplateForInclusion(	name,
													encoding,
													parse));
	}

	/**
	 * Gets a template for inclusion; used with {@link #include(Template includedTemplate)}. The advantage over simply using
	 * <code>config.getTemplate(...)</code> is that it chooses the default encoding as the <code>include</code> directive does.
	 * 
	 * @param name
	 *            the name of the template, relatively to the template root directory (not the to the directory of the currently executing template
	 *            file!). (Note that you can use {@link freemarker.cache.TemplateCache#getFullTemplatePath} to convert paths to template root relative
	 *            paths.)
	 * @param encoding
	 *            the encoding of the obtained template. If null, the encoding of the Template that is currently being processed in this Environment
	 *            is used.
	 * @param parse
	 *            whether to process a parsed template or just include the unparsed template source.
	 */
	public Template getTemplateForInclusion(String name,
											String encoding,
											boolean parse) throws IOException {
		if (encoding == null) {
			encoding = this.getTemplate().getEncoding();
		}
		if (encoding == null) {
			encoding = this.getConfiguration().getEncoding(this.getLocale());
		}
		return this.getConfiguration().getTemplate(	name,
													this.getLocale(),
													encoding,
													parse);
	}

	/**
	 * Processes a Template in the context of this <code>Environment</code>, including its output in the <code>Environment</code>'s Writer.
	 * 
	 * @param includedTemplate
	 *            the template to process. Note that it does <em>not</em> need to be a template returned by
	 *            {@link #getTemplateForInclusion(String name, String encoding, boolean parse)}.
	 */
	public void include(Template includedTemplate)	throws TemplateException,
													IOException {
		Template prevTemplate = this.getTemplate();
		this.setParent(includedTemplate);
		this.importMacros(includedTemplate);
		try {
			this.visit(includedTemplate.getRootTreeNode());
		}
		finally {
			this.setParent(prevTemplate);
		}
	}

	/**
	 * Emulates <code>import</code> directive, except that <code>name</code> must be tempate root relative.
	 * 
	 * <p>
	 * It's the same as <code>importLib(getTemplateForImporting(name), namespace)</code>. But, you may want to separately call these two methods, so
	 * you can determine the source of exceptions more precisely, and thus achieve more intelligent error handling.
	 * 
	 * @see #getTemplateForImporting(String name)
	 * @see #importLib(Template includedTemplate, String namespace)
	 */
	public Namespace importLib(	String name,
								String namespace)	throws IOException,
													TemplateException {
		return this.importLib(	this.getTemplateForImporting(name),
								namespace);
	}

	/**
	 * Gets a template for importing; used with {@link #importLib(Template importedTemplate, String namespace)}. The advantage over simply using
	 * <code>config.getTemplate(...)</code> is that it chooses the encoding as the <code>import</code> directive does.
	 * 
	 * @param name
	 *            the name of the template, relatively to the template root directory (not the to the directory of the currently executing template
	 *            file!). (Note that you can use {@link freemarker.cache.TemplateCache#getFullTemplatePath} to convert paths to template root relative
	 *            paths.)
	 */
	public Template getTemplateForImporting(String name) throws IOException {
		return this.getTemplateForInclusion(name,
											null,
											true);
	}

	/**
	 * Emulates <code>import</code> directive.
	 * 
	 * @param loadedTemplate
	 *            the template to import. Note that it does <em>not</em> need to be a template returned by
	 *            {@link #getTemplateForImporting(String name)}.
	 */
	public Namespace importLib(	Template loadedTemplate,
								String namespace)	throws IOException,
													TemplateException {
		if (this.loadedLibs == null) {
			this.loadedLibs = new HashMap();
		}
		String templateName = loadedTemplate.getName();
		Namespace existingNamespace = (Namespace) this.loadedLibs.get(templateName);
		if (existingNamespace != null) {
			if (namespace != null) {
				this.setVariable(	namespace,
									existingNamespace);
			}
		}
		else {
			Namespace newNamespace = new Namespace(loadedTemplate);
			if (namespace != null) {
				this.currentNamespace.put(	namespace,
											newNamespace);
				if (this.currentNamespace == this.mainNamespace) {
					this.globalNamespace.put(	namespace,
												newNamespace);
				}
			}
			Namespace prevNamespace = this.currentNamespace;
			this.currentNamespace = newNamespace;
			this.loadedLibs.put(templateName,
								this.currentNamespace);
			Writer prevOut = this.out;
			this.out = NULL_WRITER;
			try {
				this.include(loadedTemplate);
			}
			finally {
				this.out = prevOut;
				this.currentNamespace = prevNamespace;
			}
		}
		return (Namespace) this.loadedLibs.get(templateName);
	}

	String renderElementToString(TemplateElement te) throws IOException,
													TemplateException {
		Writer prevOut = this.out;
		try {
			StringWriter sw = new StringWriter();
			this.out = sw;
			this.visit(te);
			return sw.toString();
		}
		finally {
			this.out = prevOut;
		}
	}

	void importMacros(Template template) {
		for (Iterator it = template.getMacros().values().iterator(); it.hasNext();) {
			this.visitMacroDef((Macro) it.next());
		}
	}

	/**
	 * @return the namespace URI registered for this prefix, or null. This is based on the mappings registered in the current namespace.
	 */
	public String getNamespaceForPrefix(String prefix) {
		return this.currentNamespace.getTemplate().getNamespaceForPrefix(prefix);
	}

	public String getPrefixForNamespace(String nsURI) {
		return this.currentNamespace.getTemplate().getPrefixForNamespace(nsURI);
	}

	/**
	 * @return the default node namespace for the current FTL namespace
	 */
	public String getDefaultNS() {
		return this.currentNamespace.getTemplate().getDefaultNS();
	}

	/**
	 * A hook that Jython uses.
	 */
	public Object __getitem__(String key) throws TemplateModelException {
		return BeansWrapper.getDefaultInstance().unwrap(this.getVariable(key));
	}

	/**
	 * A hook that Jython uses.
	 */
	public void __setitem__(String key,
							Object o) throws TemplateException {
		this.setGlobalVariable(	key,
								this.getObjectWrapper().wrap(o));
	}

	private static final class NumberFormatKey {
		private final String pattern;

		private final Locale locale;

		NumberFormatKey(String pattern,
						Locale locale) {
			this.pattern = pattern;
			this.locale = locale;
		}

		@Override
		public boolean equals(Object o) {
			if (o instanceof NumberFormatKey) {
				NumberFormatKey fk = (NumberFormatKey) o;
				return fk.pattern.equals(this.pattern) && fk.locale.equals(this.locale);
			}
			return false;
		}

		@Override
		public int hashCode() {
			return this.pattern.hashCode() ^ this.locale.hashCode();
		}
	}

	private static final class DateFormatKey {
		private final int dateType;

		private final String pattern;

		private final Locale locale;

		private final TimeZone timeZone;

		DateFormatKey(	int dateType,
						String pattern,
						Locale locale,
						TimeZone timeZone) {
			this.dateType = dateType;
			this.pattern = pattern;
			this.locale = locale;
			this.timeZone = timeZone;
		}

		@Override
		public boolean equals(Object o) {
			if (o instanceof DateFormatKey) {
				DateFormatKey fk = (DateFormatKey) o;
				return this.dateType == fk.dateType
						&& fk.pattern.equals(this.pattern)
						&& fk.locale.equals(this.locale)
						&& fk.timeZone.equals(this.timeZone);
			}
			return false;
		}

		@Override
		public int hashCode() {
			return this.dateType ^ this.pattern.hashCode() ^ this.locale.hashCode() ^ this.timeZone.hashCode();
		}
	}

	/**
	 * <AUTHOR>
	 * 
	 */
	public class Namespace
							extends
							SimpleHash {

		/**
		 * 
		 */
		private static final long serialVersionUID = -3123004030473919525L;

		private Template template;

		Namespace() {
			this.template = Environment.this.getTemplate();
		}

		Namespace(Template template) {
			this.template = template;
		}

		/**
		 * @return the Template object with which this Namespace is associated.
		 */
		public Template getTemplate() {
			return this.template == null ? Environment.this.getTemplate()
										: this.template;
		}
	}

	static final Writer NULL_WRITER = new Writer() {
		@Override
		public void write(	char cbuf[],
							int off,
							int len) {
		}

		@Override
		public void flush() {
		}

		@Override
		public void close() {
		}
	};

	private static final Writer EMPTY_BODY_WRITER = new Writer() {

		@Override
		public void write(	char[] cbuf,
							int off,
							int len) throws IOException {
			if (len > 0) {
				throw new IOException("This transform does not allow nested content.");
			}
		}

		@Override
		public void flush() {
		}

		@Override
		public void close() {
		}
	};

}
