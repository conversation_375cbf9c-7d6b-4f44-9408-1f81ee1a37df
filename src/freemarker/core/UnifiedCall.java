/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the 
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.kskyb.broker.template.freemarker.model.TemplateDelegateModel;
import com.kskyb.broker.util.ArrayMap;

import freemarker.template.EmptyMap;
import freemarker.template.TemplateDirectiveModel;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateTransformModel;

/**
 * An element for the unified macro/transform syntax.
 */
final class UnifiedCall
						extends
						TemplateElement {

	/**
	 * 
	 */
	private Expression nameExp;

	/**
	 * 
	 */
	private Map<String, Expression> namedArgs;

	/**
	 * 
	 */
	private List<Expression> positionalArgs;

	/**
	 * 
	 */
	private List bodyParameterNames;

	/**
	 * 
	 */
	boolean legacySyntax;

	/**
	 * @param nameExp
	 * @param namedArgs
	 * @param nestedBlock
	 * @param bodyParameterNames
	 */
	UnifiedCall(final Expression nameExp,
				final Map namedArgs,
				final TemplateElement nestedBlock,
				final List bodyParameterNames) {
		this.nameExp = nameExp;
		this.namedArgs = namedArgs;
		this.nestedBlock = nestedBlock;
		this.bodyParameterNames = bodyParameterNames;
	}

	/**
	 * @param nameExp
	 * @param positionalArgs
	 * @param nestedBlock
	 * @param bodyParameterNames
	 */
	UnifiedCall(final Expression nameExp,
				final List<Expression> positionalArgs,
				final TemplateElement nestedBlock,
				final List bodyParameterNames) {
		this.nameExp = nameExp;
		this.positionalArgs = positionalArgs;
		this.nestedBlock = nestedBlock == TextBlock.EMPTY_BLOCK	? null
																: nestedBlock;
		this.bodyParameterNames = bodyParameterNames;
	}

	/**
	 * @param env
	 * @return
	 * @throws TemplateException
	 */
	private final Map<String, TemplateModel> extract(final Environment env) throws TemplateException {
		if (this.namedArgs != null && !this.namedArgs.isEmpty()) {
			Map<String, TemplateModel> args = new ArrayMap<String, TemplateModel>();
			for (final Map.Entry<String, Expression> entry : this.namedArgs.entrySet()) {
				String key = entry.getKey();
				Expression valueExp = entry.getValue();
				args.put(	key,
							valueExp.getAsTemplateModel(env));
			}
			return args;
		}
		return EmptyMap.instance;
	}

	/**
	 * Add by DRAGON
	 * 
	 * @param env
	 * @return
	 * @throws TemplateException
	 */
	private final Map<String, String> extractAttribute(final Environment env) throws TemplateException {
		Map<String, String> args = new HashMap<String, String>();
		if (this.namedArgs != null && !this.namedArgs.isEmpty()) {
			for (final Map.Entry<String, Expression> entry : this.namedArgs.entrySet()) {
				String key = entry.getKey();
				Expression valueExp = entry.getValue();
				args.put(	key,
							valueExp.getStringValue(env));
			}
		}
		return args;
	}

	/**
	 * @see freemarker.core.TemplateElement#accept(freemarker.core.Environment)
	 */
	@Override
	final void accept(final Environment env) throws TemplateException,
											IOException {
		TemplateModel tm = this.nameExp.getAsTemplateModel(env);

		if (tm == Macro.DO_NOTHING_MACRO) {
			return; // shortcut here.
		}
		if (tm instanceof Macro) {
			Macro macro = (Macro) tm;
			if (macro.isFunction && !this.legacySyntax) {
				throw new TemplateException("Routine "
												+ macro.getName()
												+ " is a function. A function can only be called "
												+ "within the evaluation of an expression.",
											env);
			}
			env.visit(	macro,
						this.namedArgs,
						this.positionalArgs,
						this.bodyParameterNames,
						this.nestedBlock);
			return;
		}

		if (tm instanceof TemplateDelegateModel) {
			env.visit(	this.nestedBlock,
						(TemplateDelegateModel) tm,
						this.extractAttribute(env),
						this.bodyParameterNames);
			return;
		}

		if (tm instanceof TemplateDirectiveModel) {
			env.visit(	this.nestedBlock,
						(TemplateDirectiveModel) tm,
						this.extract(env),
						this.bodyParameterNames);
			return;
		}
		if (tm instanceof TemplateTransformModel) {
			env.visit(	this.nestedBlock,
						(TemplateTransformModel) tm,
						this.extract(env));
			return;
		}

		if (tm == null) {
			throw new InvalidReferenceException(this.getStartLocation() + " " + this.nameExp + " not found.",
												env);
		}
		throw new TemplateException(this.getStartLocation()
										+ ": "
										+ this.nameExp
										+ " is not a user-defined directive. It is a "
										+ tm.getClass().getName(),
									env);
	}

	/**
	 * @see freemarker.core.TemplateObject#getCanonicalForm()
	 */
	@Override
	public final String getCanonicalForm() {
		StringBuffer buf = new StringBuffer("<@");
		buf.append(this.nameExp.getCanonicalForm());
		if (this.positionalArgs != null) {
			for (int i = 0; i < this.positionalArgs.size(); i++) {
				Expression arg = this.positionalArgs.get(i);
				if (i != 0) {
					buf.append(',');
				}
				buf.append(' ');
				buf.append(arg.getCanonicalForm());
			}
		}
		else {
			ArrayList keys = new ArrayList(this.namedArgs.keySet());
			Collections.sort(keys);
			for (int i = 0; i < keys.size(); i++) {
				Expression arg = this.namedArgs.get(keys.get(i));
				buf.append(' ');
				buf.append(keys.get(i));
				buf.append('=');
				buf.append(arg.getCanonicalForm());
			}
		}
		if (this.nestedBlock == null) {
			buf.append("/>");
		}
		else {
			buf.append('>');
			buf.append(this.nestedBlock.getCanonicalForm());
			buf.append("</@");
			if (this.nameExp instanceof Identifier || (this.nameExp instanceof Dot && ((Dot) this.nameExp).onlyHasIdentifiers())) {
				buf.append(this.nameExp);
			}
			buf.append('>');
		}
		return buf.toString();
	}

	/**
	 * @see freemarker.core.TemplateElement#getDescription()
	 */
	@Override
	public final String getDescription() {
		return "user-directive " + this.nameExp;
	}
}
