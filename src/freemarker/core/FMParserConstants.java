/* Generated By:JavaCC: Do not edit this line. FMParserConstants.java */
package freemarker.core;

interface FMParserConstants {

  int EOF = 0;
  int BLANK = 1;
  int START_TAG = 2;
  int END_TAG = 3;
  int CLOSE_TAG1 = 4;
  int CLOSE_TAG2 = 5;
  int ATTEMPT = 6;
  int RECOVER = 7;
  int IF = 8;
  int ELSE_IF = 9;
  int LIST = 10;
  int FOREACH = 11;
  int SWITCH = 12;
  int CASE = 13;
  int ASSIGN = 14;
  int GLOBALASSIGN = 15;
  int LOCALASSIGN = 16;
  int _INCLUDE = 17;
  int IMPORT = 18;
  int FUNCTION = 19;
  int MACRO = 20;
  int TRANSFORM = 21;
  int VISIT = 22;
  int STOP = 23;
  int RETURN = 24;
  int CALL = 25;
  int SETTING = 26;
  int COMPRESS = 27;
  int COMMENT = 28;
  int TERSE_COMMENT = 29;
  int NOPARSE = 30;
  int END_IF = 31;
  int END_LIST = 32;
  int END_RECOVER = 33;
  int END_ATTEMPT = 34;
  int END_FOREACH = 35;
  int END_LOCAL = 36;
  int END_GLOBAL = 37;
  int END_ASSIGN = 38;
  int END_FUNCTION = 39;
  int END_MACRO = 40;
  int END_COMPRESS = 41;
  int END_TRANSFORM = 42;
  int END_SWITCH = 43;
  int ELSE = 44;
  int BREAK = 45;
  int SIMPLE_RETURN = 46;
  int HALT = 47;
  int FLUSH = 48;
  int TRIM = 49;
  int LTRIM = 50;
  int RTRIM = 51;
  int NOTRIM = 52;
  int DEFAUL = 53;
  int SIMPLE_NESTED = 54;
  int NESTED = 55;
  int SIMPLE_RECURSE = 56;
  int RECURSE = 57;
  int FALLBACK = 58;
  int ESCAPE = 59;
  int END_ESCAPE = 60;
  int NOESCAPE = 61;
  int END_NOESCAPE = 62;
  int UNIFIED_CALL = 63;
  int UNIFIED_CALL_END = 64;
  int FTL_HEADER = 65;
  int TRIVIAL_FTL_HEADER = 66;
  int UNKNOWN_DIRECTIVE = 67;
  int WHITESPACE = 68;
  int PRINTABLE_CHARS = 69;
  int FALSE_ALERT = 70;
  int OUTPUT_ESCAPE = 71;
  int NUMERICAL_ESCAPE = 72;
  int ESCAPED_CHAR = 80;
  int STRING_LITERAL = 81;
  int RAW_STRING = 82;
  int FALSE = 83;
  int TRUE = 84;
  int INTEGER = 85;
  int DECIMAL = 86;
  int DOT = 87;
  int DOT_DOT = 88;
  int BUILT_IN = 89;
  int EXISTS = 90;
  int EQUALS = 91;
  int DOUBLE_EQUALS = 92;
  int NOT_EQUALS = 93;
  int LESS_THAN = 94;
  int LESS_THAN_EQUALS = 95;
  int ESCAPED_GT = 96;
  int ESCAPED_GTE = 97;
  int PLUS = 98;
  int MINUS = 99;
  int TIMES = 100;
  int DOUBLE_STAR = 101;
  int ELLIPSIS = 102;
  int DIVIDE = 103;
  int PERCENT = 104;
  int AND = 105;
  int OR = 106;
  int EXCLAM = 107;
  int COMMA = 108;
  int SEMICOLON = 109;
  int COLON = 110;
  int OPEN_BRACKET = 111;
  int CLOSE_BRACKET = 112;
  int OPEN_PAREN = 113;
  int CLOSE_PAREN = 114;
  int OPEN_BRACE = 115;
  int CLOSE_BRACE = 116;
  int IN = 117;
  int AS = 118;
  int USING = 119;
  int ID = 120;
  int LETTER = 121;
  int DIGIT = 122;
  int DIRECTIVE_END = 123;
  int EMPTY_DIRECTIVE_END = 124;
  int NATURAL_GT = 125;
  int NATURAL_GTE = 126;
  int TERMINATING_WHITESPACE = 127;
  int TERMINATING_EXCLAM = 128;
  int TERSE_COMMENT_END = 129;
  int MAYBE_END = 130;
  int KEEP_GOING = 131;
  int LONE_LESS_THAN_OR_DASH = 132;

  int DEFAULT = 0;
  int NODIRECTIVE = 1;
  int FM_EXPRESSION = 2;
  int IN_PAREN = 3;
  int NAMED_PARAMETER_EXPRESSION = 4;
  int EXPRESSION_COMMENT = 5;
  int NO_SPACE_EXPRESSION = 6;
  int NO_PARSE = 7;

  String[] tokenImage = {
    "<EOF>",
    "<BLANK>",
    "<START_TAG>",
    "<END_TAG>",
    "<CLOSE_TAG1>",
    "<CLOSE_TAG2>",
    "<ATTEMPT>",
    "<RECOVER>",
    "<IF>",
    "<ELSE_IF>",
    "<LIST>",
    "<FOREACH>",
    "<SWITCH>",
    "<CASE>",
    "<ASSIGN>",
    "<GLOBALASSIGN>",
    "<LOCALASSIGN>",
    "<_INCLUDE>",
    "<IMPORT>",
    "<FUNCTION>",
    "<MACRO>",
    "<TRANSFORM>",
    "<VISIT>",
    "<STOP>",
    "<RETURN>",
    "<CALL>",
    "<SETTING>",
    "<COMPRESS>",
    "<COMMENT>",
    "<TERSE_COMMENT>",
    "<NOPARSE>",
    "<END_IF>",
    "<END_LIST>",
    "<END_RECOVER>",
    "<END_ATTEMPT>",
    "<END_FOREACH>",
    "<END_LOCAL>",
    "<END_GLOBAL>",
    "<END_ASSIGN>",
    "<END_FUNCTION>",
    "<END_MACRO>",
    "<END_COMPRESS>",
    "<END_TRANSFORM>",
    "<END_SWITCH>",
    "<ELSE>",
    "<BREAK>",
    "<SIMPLE_RETURN>",
    "<HALT>",
    "<FLUSH>",
    "<TRIM>",
    "<LTRIM>",
    "<RTRIM>",
    "<NOTRIM>",
    "<DEFAUL>",
    "<SIMPLE_NESTED>",
    "<NESTED>",
    "<SIMPLE_RECURSE>",
    "<RECURSE>",
    "<FALLBACK>",
    "<ESCAPE>",
    "<END_ESCAPE>",
    "<NOESCAPE>",
    "<END_NOESCAPE>",
    "<UNIFIED_CALL>",
    "<UNIFIED_CALL_END>",
    "<FTL_HEADER>",
    "<TRIVIAL_FTL_HEADER>",
    "<UNKNOWN_DIRECTIVE>",
    "<WHITESPACE>",
    "<PRINTABLE_CHARS>",
    "<FALSE_ALERT>",
    "\"${\"",
    "\"#{\"",
    "<token of kind 73>",
    "<token of kind 74>",
    "<token of kind 75>",
    "\">\"",
    "\"]\"",
    "\"-\"",
    "<token of kind 79>",
    "<ESCAPED_CHAR>",
    "<STRING_LITERAL>",
    "<RAW_STRING>",
    "\"false\"",
    "\"true\"",
    "<INTEGER>",
    "<DECIMAL>",
    "\".\"",
    "\"..\"",
    "\"?\"",
    "\"??\"",
    "\"=\"",
    "\"==\"",
    "\"!=\"",
    "<LESS_THAN>",
    "<LESS_THAN_EQUALS>",
    "<ESCAPED_GT>",
    "<ESCAPED_GTE>",
    "\"+\"",
    "\"-\"",
    "\"*\"",
    "\"**\"",
    "\"...\"",
    "\"/\"",
    "\"%\"",
    "<AND>",
    "<OR>",
    "\"!\"",
    "\",\"",
    "\";\"",
    "\":\"",
    "\"[\"",
    "\"]\"",
    "\"(\"",
    "\")\"",
    "\"{\"",
    "\"}\"",
    "\"in\"",
    "\"as\"",
    "\"using\"",
    "<ID>",
    "<LETTER>",
    "<DIGIT>",
    "\">\"",
    "<EMPTY_DIRECTIVE_END>",
    "\">\"",
    "\">=\"",
    "<TERMINATING_WHITESPACE>",
    "<TERMINATING_EXCLAM>",
    "<TERSE_COMMENT_END>",
    "<MAYBE_END>",
    "<KEEP_GOING>",
    "<LONE_LESS_THAN_OR_DASH>",
  };

}
