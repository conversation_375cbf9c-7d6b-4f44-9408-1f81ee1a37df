/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the 
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.ListIterator;

import freemarker.template.SimpleSequence;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateSequenceModel;
import freemarker.template.utility.Collections12;

/**
 * <AUTHOR>
 * 
 */
final class ListLiteral
						extends
						Expression {

	/**
	 * 
	 */
	final List<Expression> values;

	/**
	 * @param values
	 */
	ListLiteral(List<Expression> values) {
		this.values = values;
	}

	@Override
	TemplateModel _getAsTemplateModel(final Environment env) throws TemplateException {
		SimpleSequence list = new SimpleSequence(this.values.size());
		for (Expression exp : this.values) {
			TemplateModel tm = exp.getAsTemplateModel(env);
			assertNonNull(	tm,
							exp,
							env);
			list.add(tm);
		}
		return list;
	}

	/**
	 * For the benefit of method calls, return the list of arguments as a list of values.
	 */
	List<String> getValueList(Environment env) throws TemplateException {
		int size = this.values.size();
		switch (size) {
			case 0: {
				return Collections.EMPTY_LIST;
			}
			case 1: {
				return Collections12.singletonList(this.values.get(0).getStringValue(env));
			}
			default: {
				List<String> result = new ArrayList(this.values.size());
				for (ListIterator iterator = this.values.listIterator(); iterator.hasNext();) {
					Expression exp = (Expression) iterator.next();
					result.add(exp.getStringValue(env));
				}
				return result;
			}
		}
	}

	/**
	 * For the benefit of extended method calls, return the list of arguments as a list of template models.
	 */
	final List<TemplateModel> getModelList(Environment env) throws TemplateException {
		int size = this.values.size();
		switch (size) {
			case 0: {
				return Collections.EMPTY_LIST;
			}
			case 1: {
				return Collections12.singletonList(this.values.get(0).getAsTemplateModel(env));
			}
			default: {
				List<TemplateModel> result = new ArrayList(this.values.size());
				for (ListIterator iterator = this.values.listIterator(); iterator.hasNext();) {
					Expression exp = (Expression) iterator.next();
					result.add(exp.getAsTemplateModel(env));
				}
				return result;
			}
		}
	}

	@Override
	public String getCanonicalForm() {
		StringBuffer buf = new StringBuffer("[");
		int size = this.values.size();
		for (int i = 0; i < size; i++) {
			Expression value = this.values.get(i);
			buf.append(value.getCanonicalForm());
			if (i != size - 1) {
				buf.append(",");
			}
		}
		buf.append("]");
		return buf.toString();
	}

	@Override
	boolean isLiteral() {
		if (this.constantValue != null) {
			return true;
		}
		for (int i = 0; i < this.values.size(); i++) {
			Expression exp = this.values.get(i);
			if (!exp.isLiteral()) {
				return false;
			}
		}
		return true;
	}

	// A hacky routine used by VisitNode and RecurseNode

	TemplateSequenceModel evaluateStringsToNamespaces(Environment env) throws TemplateException {
		TemplateSequenceModel val = (TemplateSequenceModel) this.getAsTemplateModel(env);
		SimpleSequence result = new SimpleSequence(val.size());
		for (int i = 0; i < this.values.size(); i++) {
			if (this.values.get(i) instanceof StringLiteral) {
				String s = ((StringLiteral) this.values.get(i)).getAsString();
				try {
					Environment.Namespace ns = env.importLib(	s,
																null);
					result.add(ns);
				}
				catch (IOException ioe) {
					throw new TemplateException("Could not import library '" + s + "', " + ioe.getMessage(),
												env);
				}
			}
			else {
				result.add(val.get(i));
			}
		}
		return result;
	}

	@Override
	Expression _deepClone(	String name,
							Expression subst) {
		final Expression[] exps = this.values.toArray(new Expression[this.values.size()]);
		final List<Expression> clonedValues = new ArrayList();
		for (Expression exp : exps) {
			clonedValues.add(exp.deepClone(	name,
											subst));
		}
		return new ListLiteral(clonedValues);
	}

}
