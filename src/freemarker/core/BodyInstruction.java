/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the 
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;

/**
 * An instruction that processes the nested block within a macro instruction.
 * 
 * <AUTHOR> href="mailto:<EMAIL>">Jonathan Revusky</a>
 */
final class BodyInstruction
							extends
							TemplateElement {

	private ArrayList<Expression> bodyParameters;

	BodyInstruction(ArrayList<Expression> bodyParameters) {
		this.bodyParameters = bodyParameters;
	}

	List getBodyParameters() {
		return this.bodyParameters;
	}

	@Override
	void accept(Environment env) throws IOException,
								TemplateException {
		Context bodyContext = new Context(env);
		env.visit(bodyContext);
	}

	@Override
	public String getCanonicalForm() {
		StringBuffer buf = new StringBuffer("<#nested");
		if (this.bodyParameters != null) {
			for (int i = 0; i < this.bodyParameters.size(); i++) {
				buf.append(' ');
				buf.append(this.bodyParameters.get(i));
			}
		}
		buf.append('>');
		return buf.toString();
	}

	@Override
	public String getDescription() {
		return "nested macro content";
	}

	class Context
					implements
					LocalContext {
		Macro.Context invokingMacroContext;

		Environment.Namespace bodyVars;

		Context(Environment env) throws TemplateException {
			this.invokingMacroContext = env.getCurrentMacroContext();
			List bodyParameterNames = this.invokingMacroContext.bodyParameterNames;
			if (BodyInstruction.this.bodyParameters != null) {
				for (int i = 0; i < BodyInstruction.this.bodyParameters.size(); i++) {
					Expression exp = BodyInstruction.this.bodyParameters.get(i);
					TemplateModel tm = exp.getAsTemplateModel(env);
					if (bodyParameterNames != null && i < bodyParameterNames.size()) {
						String bodyParameterName = (String) bodyParameterNames.get(i);
						if (this.bodyVars == null) {
							this.bodyVars = env.new Namespace();
						}
						this.bodyVars.put(	bodyParameterName,
											tm);
					}
				}
			}
		}

		public TemplateModel getLocalVariable(String name) throws TemplateModelException {
			return this.bodyVars == null ? null
										: this.bodyVars.get(name);
		}

		public Collection getLocalVariableNames() {
			List bodyParameterNames = this.invokingMacroContext.bodyParameterNames;
			return bodyParameterNames == null	? Collections.EMPTY_LIST
												: bodyParameterNames;
		}
	}
}
