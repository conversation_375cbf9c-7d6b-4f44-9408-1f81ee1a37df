/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the 
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.io.IOException;
import java.io.StringReader;

import freemarker.template.SimpleScalar;
import freemarker.template.TemplateException;
import freemarker.template.TemplateExceptionHandler;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateScalarModel;

final class StringLiteral
							extends
							Expression
										implements
										TemplateScalarModel {

	private final String value;

	private TemplateElement interpolatedOutput;

	StringLiteral(String value) {
		this.value = value;
	}

	void checkInterpolation() throws ParseException {
		if (this.value.length() > 3
			&& (this.value.indexOf("${") >= 0 || this.value.indexOf("#{") >= 0)) {
			SimpleCharStream scs = new SimpleCharStream(new StringReader(this.value),
														this.beginLine,
														this.beginColumn + 1,
														this.value.length());
			FMParserTokenManager token_source = new FMParserTokenManager(scs);
			token_source.onlyTextOutput = true;
			FMParser parser = new FMParser(token_source);
			parser.template = this.getTemplate();
			try {
				this.interpolatedOutput = parser.FreeMarkerText();
			}
			catch (ParseException e) {
				e.setTemplateName(this.getTemplate().getName());
				throw e;
			}
			this.constantValue = null;
		}
	}

	@Override
	TemplateModel _getAsTemplateModel(Environment env) throws TemplateException {
		return new SimpleScalar(this.getStringValue(env));
	}

	public String getAsString() {
		return this.value;
	}

	@Override
	String getStringValue(Environment env) throws TemplateException {
		if (this.interpolatedOutput == null) {
			return this.value;
		}
		else {
			TemplateExceptionHandler teh = env.getTemplateExceptionHandler();
			env.setTemplateExceptionHandler(TemplateExceptionHandler.RETHROW_HANDLER);
			try {
				return env.renderElementToString(this.interpolatedOutput);
			}
			catch (IOException ioe) {
				throw new TemplateException(ioe,
											env);
			}
			finally {
				env.setTemplateExceptionHandler(teh);
			}
		}
	}

	@Override
	public String getCanonicalForm() {
		// return "\"" + StringUtil.FTLStringLiteralEnc(value) + "\"";
		return "\"" + escapeString(this.value) + "\"";
	}

	@Override
	boolean isLiteral() {
		return this.interpolatedOutput == null;
	}

	@Override
	Expression _deepClone(	String name,
							Expression subst) {
		StringLiteral cloned = new StringLiteral(this.value);
		cloned.interpolatedOutput = this.interpolatedOutput;
		return cloned;
	}

	static private String escapeString(String s) {
		if (s.indexOf('"') == -1) {
			return s;
		}
		java.util.StringTokenizer st = new java.util.StringTokenizer(	s,
																		"\"",
																		true);
		StringBuffer buf = new StringBuffer();
		while (st.hasMoreTokens()) {
			String tok = st.nextToken();
			if (tok.equals("\"")) {
				buf.append('\\');
			}
			buf.append(tok);
		}
		return buf.toString();
	}
}
