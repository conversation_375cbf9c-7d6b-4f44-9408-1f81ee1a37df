/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the 
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;

import freemarker.template.SimpleSequence;
import freemarker.template.TemplateException;
import freemarker.template.TemplateNodeModel;
import freemarker.template.TemplateSequenceModel;
import freemarker.template.utility.Collections12;

/**
 * Objects that represent elements in the compiled tree representation of the template necessarily descend from this abstract class.
 */
// implements
// TreeNode
abstract public class TemplateElement
										extends
										TemplateObject {

	/**
	 * 
	 */
	TemplateElement parent;

	// Only one of nestedBlock and nestedElements can be non-null.

	/**
	 * 
	 */
	TemplateElement nestedBlock;

	/**
	 * 
	 */
	List<TemplateElement> nestedElements;

	/**
	 * Processes the contents of this <tt>TemplateElement</tt> and outputs the resulting text
	 * 
	 * @param env
	 *            The runtime environment
	 */
	abstract void accept(Environment env) throws TemplateException, IOException;

	/**
	 * @return
	 */
	abstract public String getDescription();

	// Methods to implement TemplateNodeModel

	/**
	 * @return
	 */
	public TemplateNodeModel getParentNode() {
		// return parent;
		return null;
	}

	/**
	 * @return
	 */
	public String getNodeNamespace() {
		return null;
	}

	/**
	 * @return
	 */
	public String getNodeType() {
		return "element";
	}

	/**
	 * @return
	 */
	public TemplateSequenceModel getChildNodes() {
		if (this.nestedElements != null) {
			return new SimpleSequence(this.nestedElements);
		}
		SimpleSequence result = new SimpleSequence();
		if (this.nestedBlock != null) {
			result.add(this.nestedBlock);
		}
		return result;
	}

	/**
	 * @return
	 */
	public String getNodeName() {
		String classname = this.getClass().getName();
		int shortNameOffset = classname.lastIndexOf('.') + 1;
		return classname.substring(shortNameOffset);
	}

	// Methods so that we can implement the Swing TreeNode API.

	/**
	 * @return
	 */
	public boolean isLeaf() {
		return this.nestedBlock == null
				&& (this.nestedElements == null || this.nestedElements.isEmpty());
	}

	/**
	 * @return
	 */
	public boolean getAllowsChildren() {
		return !this.isLeaf();
	}

	/**
	 * @param node
	 * @return
	 */
	public int getIndex(TemplateElement node) {
		if (this.nestedBlock instanceof MixedContent) {
			return this.nestedBlock.getIndex(node);
		}
		if (this.nestedBlock != null) {
			if (node == this.nestedBlock) {
				return 0;
			}
		}
		else if (this.nestedElements != null) {
			return this.nestedElements.indexOf(node);
		}
		return -1;
	}

	/**
	 * @return
	 */
	public int getChildCount() {
		if (this.nestedBlock instanceof MixedContent) {
			return this.nestedBlock.getChildCount();
		}
		if (this.nestedBlock != null) {
			return 1;
		}
		else if (this.nestedElements != null) {
			return this.nestedElements.size();
		}
		return 0;
	}

	/**
	 * @return
	 */
	public Enumeration children() {
		if (this.nestedBlock instanceof MixedContent) {
			return this.nestedBlock.children();
		}
		if (this.nestedBlock != null) {
			return Collections.enumeration(Collections12.singletonList(this.nestedBlock));
		}
		else if (this.nestedElements != null) {
			return Collections.enumeration(this.nestedElements);
		}
		return Collections.enumeration(Collections.EMPTY_LIST);
	}

	/**
	 * @param index
	 * @return
	 */
	public TemplateElement getChildAt(int index) {
		if (this.nestedBlock instanceof MixedContent) {
			return this.nestedBlock.getChildAt(index);
		}
		if (this.nestedBlock != null) {
			if (index == 0) {
				return this.nestedBlock;
			}
			throw new ArrayIndexOutOfBoundsException("invalid index");
		}
		else if (this.nestedElements != null) {
			return this.nestedElements.get(index);
		}
		throw new ArrayIndexOutOfBoundsException("element has no children");
	}

	/**
	 * @param index
	 * @param element
	 */
	public void setChildAt(	int index,
							TemplateElement element) {
		if (this.nestedBlock instanceof MixedContent) {
			this.nestedBlock.setChildAt(index,
										element);
		}
		else if (this.nestedBlock != null) {
			if (index == 0) {
				this.nestedBlock = element;
				element.parent = this;
			}
			else {
				throw new IndexOutOfBoundsException("invalid index");
			}
		}
		else if (this.nestedElements != null) {
			this.nestedElements.set(index,
									element);
			element.parent = this;
		}
		else {
			throw new IndexOutOfBoundsException("element has no children");
		}
	}

	/**
	 * @return
	 */
	public TemplateElement getParent() {
		return this.parent;
	}

	// Walk the tree and set the parent field in all the nested elements recursively.

	/**
	 * @param parent
	 */
	void setParentRecursively(TemplateElement parent) {
		this.parent = parent;
		int nestedSize = this.nestedElements == null ? 0
													: this.nestedElements.size();
		for (int i = 0; i < nestedSize; i++) {
			(this.nestedElements.get(i)).setParentRecursively(this);
		}
		if (this.nestedBlock != null) {
			this.nestedBlock.setParentRecursively(this);
		}
	}

	/**
	 * We walk the tree and do some cleanup
	 * 
	 * @param stripWhitespace
	 *            whether to clean up superfluous whitespace
	 */
	TemplateElement postParseCleanup(boolean stripWhitespace) throws ParseException {
		if (this.nestedElements != null) {
			for (int i = 0; i < this.nestedElements.size(); i++) {
				TemplateElement te = this.nestedElements.get(i);
				te = te.postParseCleanup(stripWhitespace);
				this.nestedElements.set(i,
										te);
				te.parent = this;
			}
			if (stripWhitespace) {
				for (Iterator it = this.nestedElements.iterator(); it.hasNext();) {
					TemplateElement te = (TemplateElement) it.next();
					if (te.isIgnorable()) {
						it.remove();
					}
				}
			}
			if (this.nestedElements instanceof ArrayList) {
				((ArrayList) this.nestedElements).trimToSize();
			}
		}
		if (this.nestedBlock != null) {
			this.nestedBlock = this.nestedBlock.postParseCleanup(stripWhitespace);
			if (this.nestedBlock.isIgnorable()) {
				this.nestedBlock = null;
			}
			else {
				this.nestedBlock.parent = this;
			}
		}
		return this;
	}

	/**
	 * @return
	 */
	boolean isIgnorable() {
		return false;
	}

	// The following methods exist to support some fancier tree-walking
	// and were introduced to support the whitespace cleanup feature in 2.2

	/**
	 * @return
	 */
	TemplateElement prevTerminalNode() {
		TemplateElement prev = this.previousSibling();
		if (prev != null) {
			return prev.getLastLeaf();
		}
		else if (this.parent != null) {
			return this.parent.prevTerminalNode();
		}
		return null;
	}

	/**
	 * @return
	 */
	TemplateElement nextTerminalNode() {
		TemplateElement next = this.nextSibling();
		if (next != null) {
			return next.getFirstLeaf();
		}
		else if (this.parent != null) {
			return this.parent.nextTerminalNode();
		}
		return null;
	}

	/**
	 * @return
	 */
	TemplateElement previousSibling() {
		if (this.parent == null) {
			return null;
		}
		List siblings = this.parent.nestedElements;
		if (siblings == null) {
			return null;
		}
		for (int i = siblings.size() - 1; i >= 0; i--) {
			if (siblings.get(i) == this) {
				return (i > 0)	? (TemplateElement) siblings.get(i - 1)
								: null;
			}
		}
		return null;
	}

	/**
	 * @return
	 */
	TemplateElement nextSibling() {
		if (this.parent == null) {
			return null;
		}
		List siblings = this.parent.nestedElements;
		if (siblings == null) {
			return null;
		}
		for (int i = 0; i < siblings.size(); i++) {
			if (siblings.get(i) == this) {
				return (i + 1) < siblings.size() ? (TemplateElement) siblings.get(i + 1)
												: null;
			}
		}
		return null;
	}

	/**
	 * @return
	 */
	private TemplateElement getFirstChild() {
		if (this.nestedBlock != null) {
			return this.nestedBlock;
		}
		if (this.nestedElements != null && this.nestedElements.size() > 0) {
			return this.nestedElements.get(0);
		}
		return null;
	}

	/**
	 * @return
	 */
	private TemplateElement getLastChild() {
		if (this.nestedBlock != null) {
			return this.nestedBlock;
		}
		if (this.nestedElements != null && this.nestedElements.size() > 0) {
			return this.nestedElements.get(this.nestedElements.size() - 1);
		}
		return null;
	}

	/**
	 * @return
	 */
	private TemplateElement getFirstLeaf() {
		TemplateElement te = this;
		while (!te.isLeaf() && !(te instanceof Macro) && !(te instanceof BlockAssignment)) {
			// A macro or macro invocation is treated as a leaf here for special reasons
			te = te.getFirstChild();
		}
		return te;
	}

	/**
	 * @return
	 */
	private TemplateElement getLastLeaf() {
		TemplateElement te = this;
		while (!te.isLeaf() && !(te instanceof Macro) && !(te instanceof BlockAssignment)) {
			// A macro or macro invocation is treated as a leaf here for special reasons
			te = te.getLastChild();
		}
		return te;
	}

	/**
	 * determines whether this element's presence on a line indicates that we should not strip opening whitespace in the post-parse whitespace
	 * gobbling step.
	 */
	boolean heedsOpeningWhitespace() {
		return false;
	}

	/**
	 * determines whether this element's presence on a line indicates that we should not strip trailing whitespace in the post-parse whitespace
	 * gobbling step.
	 */
	boolean heedsTrailingWhitespace() {
		return false;
	}
}
