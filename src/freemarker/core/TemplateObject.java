/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the 
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.core;

import freemarker.template.Template;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;

/**
 * Objects that represent instructions or expressions in the compiled tree representation of the template all descend from this abstract base class.
 */
public abstract class TemplateObject {

	private Template template;

	int beginColumn, beginLine, endColumn, endLine;

	final void setLocation(	Template template,
							Token begin,
							Token end) throws ParseException {
		this.setLocation(	template,
							begin.beginColumn,
							begin.beginLine,
							end.endColumn,
							end.endLine);
	}

	final void setLocation(	Template template,
							Token begin,
							TemplateObject end) throws ParseException {
		this.setLocation(	template,
							begin.beginColumn,
							begin.beginLine,
							end.endColumn,
							end.endLine);
	}

	final void setLocation(	Template template,
							TemplateObject begin,
							Token end) throws ParseException {
		this.setLocation(	template,
							begin.beginColumn,
							begin.beginLine,
							end.endColumn,
							end.endLine);
	}

	final void setLocation(	Template template,
							TemplateObject begin,
							TemplateObject end) throws ParseException {
		this.setLocation(	template,
							begin.beginColumn,
							begin.beginLine,
							end.endColumn,
							end.endLine);
	}

	public final int getBeginColumn() {
		return this.beginColumn;
	}

	public final int getBeginLine() {
		return this.beginLine;
	}

	public final int getEndColumn() {
		return this.endColumn;
	}

	public final int getEndLine() {
		return this.endLine;
	}

	void setLocation(	Template template,
						int beginColumn,
						int beginLine,
						int endColumn,
						int endLine) throws ParseException {
		this.template = template;
		this.beginColumn = beginColumn;
		this.beginLine = beginLine;
		this.endColumn = endColumn;
		this.endLine = endLine;
	}

	static void assertNonNull(	TemplateModel model,
								Expression exp,
								Environment env) throws InvalidReferenceException {
		if (model == null) {
			throw new InvalidReferenceException("Expression "
													+ exp
													+ " is undefined "
													+ exp.getStartLocation()
													+ ".",
												env);
		}
	}

	static TemplateException invalidTypeException(	TemplateModel model,
													Expression exp,
													Environment env,
													String expected) throws TemplateException {
		assertNonNull(	model,
						exp,
						env);
		return new TemplateException(	"Expected "
											+ expected
											+ ". "
											+ exp
											+ " evaluated instead to "
											+ model.getClass().getName()
											+ " "
											+ exp.getStartLocation()
											+ ".",
										env);
	}

	/**
	 * Returns a string that indicates where in the template source, this object is.
	 */
	public String getStartLocation() {
		String templateName = this.template != null	? this.template.getName()
													: "input";
		return "on line " + this.beginLine + ", column " + this.beginColumn + " in " + templateName;
	}

	public String getEndLocation() {
		String templateName = this.template != null	? this.template.getName()
													: "input";
		return "on line " + this.endLine + ", column " + this.endColumn + " in " + templateName;
	}

	public final String getSource() {
		if (this.template != null) {
			return this.template.getSource(	this.beginColumn,
											this.beginLine,
											this.endColumn,
											this.endLine);
		}
		else {
			return this.getCanonicalForm();
		}
	}

	@Override
	public String toString() {
		try {
			return this.getSource();
		}
		catch (Exception e) { // REVISIT: A bit of a hack? (JR)
			return this.getCanonicalForm();
		}
	}

	/**
	 * @return whether the point in the template file specified by the column and line numbers is contained within this template object.
	 */
	public boolean contains(int column,
							int line) {
		if (line < this.beginLine || line > this.endLine) {
			return false;
		}
		if (line == this.beginLine) {
			if (column < this.beginColumn) {
				return false;
			}
		}
		if (line == this.endLine) {
			if (column > this.endColumn) {
				return false;
			}
		}
		return true;
	}

	public Template getTemplate() {
		return this.template;
	}

	TemplateObject copyLocationFrom(TemplateObject from) {
		this.template = from.template;
		this.beginColumn = from.beginColumn;
		this.beginLine = from.beginLine;
		this.endColumn = from.endColumn;
		this.endLine = from.endLine;
		return this;
	}

	abstract public String getCanonicalForm();
}
