package freemarker.template;

import java.util.Locale;

import freemarker.core.Environment;

abstract public class LocalizedString
										implements
										TemplateScalarModel {

	public String getAsString() throws TemplateModelException {
		Environment env = Environment.getCurrentEnvironment();
		Locale locale = env.getLocale();
		return this.getLocalizedString(locale);
	}

	abstract public String getLocalizedString(Locale locale) throws TemplateModelException;
}
