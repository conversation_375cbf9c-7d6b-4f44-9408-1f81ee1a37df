package freemarker.template.utility;

import java.io.PrintStream;
import java.io.PrintWriter;

/**
 * The equivalent of JDK 1.3 UndeclaredThrowableException.
 * 
 * <AUTHOR>
 * @version $Id: UndeclaredThrowableException.java,v 1.1 2010/09/04 12:00:50 dragon Exp $
 */
public class UndeclaredThrowableException
											extends
											RuntimeException {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6786680925871320389L;

	private final Throwable t;

	public UndeclaredThrowableException(Throwable t) {
		this.t = t;
	}

	@Override
	public void printStackTrace() {
		this.printStackTrace(System.err);
	}

	@Override
	public void printStackTrace(PrintStream ps) {
		synchronized (ps) {
			ps.print("Undeclared throwable:");
			this.t.printStackTrace(ps);
		}
	}

	@Override
	public void printStackTrace(PrintWriter pw) {
		synchronized (pw) {
			pw.print("Undeclared throwable:");
			this.t.printStackTrace(pw);
		}
	}

	public Throwable getUndeclaredThrowable() {
		return this.t;
	}
}
