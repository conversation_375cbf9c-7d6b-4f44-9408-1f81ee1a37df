/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.template.utility;

import java.io.IOException;
import java.io.Writer;
import java.util.Map;

import freemarker.template.TemplateModel;
import freemarker.template.TemplateTransformModel;

/**
 * Performs an XML escaping of a given template fragment. Specifically, &lt; &gt; &quot ' and &amp; are all turned into entity references.
 * 
 * <p>
 * An instance of this tarnsform is initially visible as shared variable called <tt>xml_escape</tt>.
 * </p>
 * 
 * @version $Id: XmlEscape.java,v 1.2 2011/11/25 01:55:39 dragon Exp $
 */
public class XmlEscape
						implements
						TemplateTransformModel {

	private static final char[] LT = "&lt;".toCharArray();

	private static final char[] GT = "&gt;".toCharArray();

	private static final char[] AMP = "&amp;".toCharArray();

	private static final char[] QUOT = "&quot;".toCharArray();

	private static final char[] APOS = "&apos;".toCharArray();

	public XmlEscape() {

	}

	/**
	 * @see freemarker.template.TemplateTransformModel#getWriter(java.io.Writer, java.util.Map)
	 */
	public Writer getWriter(final Writer out,
							final Map<String, TemplateModel> args) {
		return new Writer() {
			@Override
			public void write(int c) throws IOException {
				switch (c) {
					case '<':
						out.write(	LT,
									0,
									4);
						break;
					case '>':
						out.write(	GT,
									0,
									4);
						break;
					case '&':
						out.write(	AMP,
									0,
									5);
						break;
					case '"':
						out.write(	QUOT,
									0,
									6);
						break;
					case '\'':
						out.write(	APOS,
									0,
									6);
						break;
					default:
						out.write(c);
				}
			}

			@Override
			public void write(	char cbuf[],
								int off,
								int len) throws IOException {
				int lastoff = off;
				int lastpos = off + len;
				for (int i = off; i < lastpos; i++) {
					switch (cbuf[i]) {
						case '<':
							out.write(	cbuf,
										lastoff,
										i - lastoff);
							out.write(	LT,
										0,
										4);
							lastoff = i + 1;
							break;
						case '>':
							out.write(	cbuf,
										lastoff,
										i - lastoff);
							out.write(	GT,
										0,
										4);
							lastoff = i + 1;
							break;
						case '&':
							out.write(	cbuf,
										lastoff,
										i - lastoff);
							out.write(	AMP,
										0,
										5);
							lastoff = i + 1;
							break;
						case '"':
							out.write(	cbuf,
										lastoff,
										i - lastoff);
							out.write(	QUOT,
										0,
										6);
							lastoff = i + 1;
							break;
						case '\'':
							out.write(	cbuf,
										lastoff,
										i - lastoff);
							out.write(	APOS,
										0,
										6);
							lastoff = i + 1;
							break;
					}
				}
				int remaining = lastpos - lastoff;
				if (remaining > 0) {
					out.write(	cbuf,
								lastoff,
								remaining);
				}
			}

			@Override
			public void flush() throws IOException {
				out.flush();
			}

			@Override
			public void close() {
			}
		};
	}
}
