/*
 * Copyright (c) 2003 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.template.utility;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.Reader;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Map;

import freemarker.template.TemplateModel;
import freemarker.template.TemplateTransformModel;

/**
 * <p>
 * Transformer that supports FreeMarker legacy behavior: all newlines appearing within the transformed area will be transformed into the platform's
 * default newline. Unlike the old behavior, however, newlines generated by the data model are also converted. Legacy behavior was to leave newlines
 * in the data model unaltered.
 * </p>
 * 
 * <p>
 * Usage:<br />
 * From java:
 * </p>
 * 
 * <pre>
 * SimpleHash root = new SimpleHash();
 * 
 * root.put( "normalizeNewlines", new freemarker.template.utility.NormalizeNewlines() );
 * 
 * ...
 * </pre>
 * 
 * <p>
 * From your FreeMarker template:
 * </p>
 * 
 * <pre>
 * &lt;transform normalizeNewlines&gt;
 *   &lt;html&gt;
 *   &lt;head&gt;
 *   ...
 *   &lt;p&gt;This template has all newlines normalized to the current platform's
 *   default.&lt;/p&gt;
 *   ...
 *   &lt;/body&gt;
 *   &lt;/html&gt;
 * &lt;/transform&gt;
 * </pre>
 * 
 * 
 * @version $Id: NormalizeNewlines.java,v 1.2 2011/11/25 01:55:39 dragon Exp $
 */
public class NormalizeNewlines
								implements
								TemplateTransformModel {

	public Writer getWriter(final Writer out,
							final Map<String, TemplateModel> args) {
		final StringBuffer buf = new StringBuffer();
		return new Writer() {
			@Override
			public void write(	char cbuf[],
								int off,
								int len) {
				buf.append(	cbuf,
							off,
							len);
			}

			@Override
			public void flush() throws IOException {
				out.flush();
			}

			@Override
			public void close() throws IOException {
				StringReader sr = new StringReader(buf.toString());
				StringWriter sw = new StringWriter();
				NormalizeNewlines.this.transform(	sr,
													sw);
				out.write(sw.toString());
			}
		};
	}

	/**
	 * Performs newline normalization on FreeMarker output.
	 * 
	 * @param in
	 *            the input to be transformed
	 * @param out
	 *            the destination of the transformation
	 */
	public void transform(	Reader in,
							Writer out) throws IOException {
		BufferedReader br = (in instanceof BufferedReader)	? (BufferedReader) in
															: new BufferedReader(in);
		PrintWriter pw = (out instanceof PrintWriter)	? (PrintWriter) out
														: new PrintWriter(out);
		String line = br.readLine();
		if (line != null) {
			if (line.length() > 0) {
				pw.println(line);
			}
		}
		while ((line = br.readLine()) != null) {
			pw.println(line);
		}
	}
}
