package freemarker.template;

import java.util.Map;

import com.kskyb.broker.util.SelectorWrapper;


/**
 * <AUTHOR>
 * 
 */
public abstract class ObjectWrapperAdapter
											implements
											ObjectWrapper {

	/**
	 * @see freemarker.template.ObjectWrapper#root(java.lang.Object)
	 */
	public Map<String, Object> root(final Object obj) throws TemplateModelException {
		return SelectorWrapper.wrapSingleRecord(obj);
	}
}
