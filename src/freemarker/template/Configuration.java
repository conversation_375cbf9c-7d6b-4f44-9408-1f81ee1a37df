/*
 * Copyright (c) 2003-2006 The Visigoth Software Society. All rights
 * reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The end-user documentation included with the redistribution, if
 *    any, must include the following acknowledgement:
 *       "This product includes software developed by the
 *        Visigoth Software Society (http://www.visigoths.org/)."
 *    Alternately, this acknowledgement may appear in the software itself,
 *    if and wherever such third-party acknowledgements normally appear.
 *
 * 4. Neither the name "FreeMarker", "Visigoth", nor any of the names of the 
 *    project contributors may be used to endorse or promote products derived
 *    from this software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 5. Products derived from this software may not be called "FreeMarker" or "Visigoth"
 *    nor may "<PERSON>Marker" or "Visigoth" appear in their names
 *    without prior written permission of the Visigoth Software Society.
 *
 * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED.  IN NO EVENT SHALL THE VISIGOTH SOFTWARE SOCIETY OR
 * ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF
 * USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 * OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT
 * OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
 * SUCH DAMAGE.
 * ====================================================================
 *
 * This software consists of voluntary contributions made by many
 * individuals on behalf of the Visigoth Software Society. For more
 * information on the Visigoth Software Society, please see
 * http://www.visigoths.org/
 */

package freemarker.template;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

import com.kskyb.broker.util.ArrayMap;

import freemarker.cache.CacheStorage;
import freemarker.cache.ClassTemplateLoader;
import freemarker.cache.FileTemplateLoader;
import freemarker.cache.MruCacheStorage;
import freemarker.cache.TemplateCache;
import freemarker.cache.TemplateLoader;
import freemarker.core.Configurable;
import freemarker.core.Environment;
import freemarker.core.ParseException;
import freemarker.template.utility.ClassUtil;
import freemarker.template.utility.HtmlEscape;
import freemarker.template.utility.NormalizeNewlines;
import freemarker.template.utility.SecurityUtilities;
import freemarker.template.utility.StandardCompress;
import freemarker.template.utility.StringUtil;
import freemarker.template.utility.XmlEscape;

/**
 * Main entry point into the FreeMarker API, this class encapsulates the various configuration parameters with which FreeMarker is run, as well as
 * serves as a central template loading and caching point. Note that this class uses a default strategy for loading and caching templates. You can
 * plug in a replacement template loading mechanism by using the {@link #setTemplateLoader(TemplateLoader)} method.
 * 
 * This object is <em>not synchronized</em>. Thus, the settings must not be changed after you have started to access the object from multiple threads.
 * If you use multiple threads, set everything directly after you have instantiated the <code>Configuration</code> object, and don't change the
 * settings anymore.
 * 
 * <AUTHOR> href="mailto:<EMAIL>">Jonathan Revusky</a>
 * <AUTHOR> Szegedi
 * @version $Id: Configuration.java,v 1.2 2010/09/17 08:05:12 dragon Exp $
 */

public final class Configuration
								extends
								Configurable
											implements
											Cloneable {
	public static final String DEFAULT_ENCODING_KEY = "default_encoding";

	public static final String LOCALIZED_LOOKUP_KEY = "localized_lookup";

	public static final String STRICT_SYNTAX_KEY = "strict_syntax";

	public static final String WHITESPACE_STRIPPING_KEY = "whitespace_stripping";

	public static final String CACHE_STORAGE_KEY = "cache_storage";

	public static final String TEMPLATE_UPDATE_DELAY_KEY = "template_update_delay";

	public static final String AUTO_IMPORT_KEY = "auto_import";

	public static final String AUTO_INCLUDE_KEY = "auto_include";

	public static final String TAG_SYNTAX_KEY = "tag_syntax";

	public static final int AUTO_DETECT_TAG_SYNTAX = 0;

	public static final int ANGLE_BRACKET_TAG_SYNTAX = 1;

	public static final int SQUARE_BRACKET_TAG_SYNTAX = 2;

	private static String cachedVersion;

	private boolean strictSyntax = true;

	private boolean localizedLookup = true;

	private boolean whitespaceStripping = true;

	private int tagSyntax = ANGLE_BRACKET_TAG_SYNTAX;

	private TemplateCache cache;

	/**
	 * 
	 */
	private ArrayMap<String, TemplateModel> variables = new ArrayMap<String, TemplateModel>();

	/**
	 * 
	 */
	private HashMap encodingMap = new HashMap();

	/**
	 * 
	 */
	private Map autoImportMap = new HashMap();

	/**
	 * 
	 */
	private ArrayList autoImports = new ArrayList();

	/**
	 * 
	 */
	private ArrayList autoIncludes = new ArrayList();

	/**
	 * 
	 */
	private String defaultEncoding = SecurityUtilities.getSystemProperty("file.encoding");

	/**
	 * 
	 */
	public Configuration() {
		this.cache = new TemplateCache();
		this.cache.setConfiguration(this);
		this.cache.setDelay(5000);
		this.loadBuiltInSharedVariables();
	}

	private void loadBuiltInSharedVariables() {
		// variables.put("capture_output", new CaptureOutput());
		this.variables.put(	"compress",
							StandardCompress.INSTANCE);
		this.variables.put(	"html_escape",
							new HtmlEscape());
		this.variables.put(	"normalize_newlines",
							new NormalizeNewlines());
		this.variables.put(	"xml_escape",
							new XmlEscape());
	}

	/**
	 * Clears language-to-encoding map.
	 * 
	 * @see #loadBuiltInEncodingMap
	 * @see #setEncoding
	 */
	public void clearEncodingMap() {
		this.encodingMap.clear();
	}

	/**
	 * Sets a template loader that is used to look up and load templates. By providing your own template loader, you can customize the way templates
	 * are loaded. Several convenience methods in this class already allow you to install commonly used loaders:
	 * {@link #setClassForTemplateLoading(Class, String)}, {@link #setDirectoryForTemplateLoading(File)}, and
	 * {@link #setServletContextForTemplateLoading(Object, String)}. By default, a multi-loader is used that first tries to load a template from the
	 * file in the current directory, then from a resource on the classpath.
	 */
	public synchronized void setTemplateLoader(TemplateLoader loader) {
		this.createTemplateCache(	loader,
									this.cache.getCacheStorage());
	}

	/**
	 * @param loader
	 * @param storage
	 */
	private void createTemplateCache(	TemplateLoader loader,
										CacheStorage storage) {
		TemplateCache oldCache = this.cache;
		this.cache = new TemplateCache(	loader,
										storage);
		this.cache.setDelay(oldCache.getDelay());
		this.cache.setConfiguration(this);
		this.cache.setLocalizedLookup(this.localizedLookup);
	}

	/**
	 * @return the template loader that is used to look up and load templates.
	 * @see #setTemplateLoader
	 */
	public TemplateLoader getTemplateLoader() {
		return this.cache.getTemplateLoader();
	}

	/**
	 * @param storage
	 */
	public synchronized void setCacheStorage(CacheStorage storage) {
		this.createTemplateCache(	this.cache.getTemplateLoader(),
									storage);
	}

	/**
	 * Set the explicit directory from which to load templates.
	 */
	public void setDirectoryForTemplateLoading(File dir) throws IOException {
		TemplateLoader tl = this.getTemplateLoader();
		if (tl instanceof FileTemplateLoader) {
			String path = ((FileTemplateLoader) tl).baseDir.getCanonicalPath();
			if (path.equals(dir.getCanonicalPath())) {
				return;
			}
		}
		this.setTemplateLoader(new FileTemplateLoader(dir));
	}

	/**
	 * Sets a class relative to which we do the Class.getResource() call to load templates.
	 */
	public void setClassForTemplateLoading(	Class clazz,
											String pathPrefix) {
		this.setTemplateLoader(new ClassTemplateLoader(	clazz,
														pathPrefix));
	}

	/**
	 * Set the time in seconds that must elapse before checking whether there is a newer version of a template file. This method is thread-safe and
	 * can be called while the engine works.
	 */
	public void setTemplateUpdateDelay(int delay) {
		this.cache.setDelay(1000L * delay);
	}

	/**
	 * Sets whether directives such as if, else, etcetera must be written as #if, #else, etcetera. Any tag not starting with &lt;# or &lt;/# is
	 * considered as plain text and will go to the output as is. Tag starting with &lt# or &lt/# must be valid FTL tag, or else the template is
	 * invalid (i.e. &lt;#noSuchDirective> is an error).
	 */

	public void setStrictSyntaxMode(boolean b) {
		this.strictSyntax = b;
	}

	/**
	 * Tells whether directives such as if, else, etcetera must be written as #if, #else, etcetera.
	 * 
	 * @see #setStrictSyntaxMode
	 */
	public boolean getStrictSyntaxMode() {
		return this.strictSyntax;
	}

	/**
	 * Sets whether the FTL parser will try to remove superfluous white-space around certain FTL tags.
	 */
	public void setWhitespaceStripping(boolean b) {
		this.whitespaceStripping = b;
	}

	/**
	 * Gets whether the FTL parser will try to remove superfluous white-space around certain FTL tags.
	 * 
	 * @see #setWhitespaceStripping
	 */
	public boolean getWhitespaceStripping() {
		return this.whitespaceStripping;
	}

	/**
	 * Determines the syntax of the template files (angle bracket VS square bracket) that has no <markup>ftl</markup> directive in it. The
	 * <code>tagSyntax</code> parameter must be one of:
	 * <ul>
	 * <li>{@link Configuration#AUTO_DETECT_TAG_SYNTAX}: use the syntax of the first FreeMarker tag (can be anything, like <tt>list</tt>,
	 * <tt>include</tt>, user defined, ...etc)
	 * <li>{@link Configuration#ANGLE_BRACKET_TAG_SYNTAX}: use the angle bracket syntax (the normal syntax)
	 * <li>{@link Configuration#SQUARE_BRACKET_TAG_SYNTAX}: use the square bracket syntax
	 * </ul>
	 * 
	 * <p>
	 * In FreeMarker 2.3.x {@link Configuration#ANGLE_BRACKET_TAG_SYNTAX} is the default for better backward compatibility. Starting from 2.4.x
	 * {@link Configuration#AUTO_DETECT_TAG_SYNTAX} is the default, so it is recommended to use that even for 2.3.x.
	 * 
	 * <p>
	 * This setting is ignored for the templates that have <tt>ftl</tt> directive in it. For those templates the syntax used for the <tt>ftl</tt>
	 * directive determines the syntax.
	 */
	public void setTagSyntax(int tagSyntax) {
		if (tagSyntax != AUTO_DETECT_TAG_SYNTAX
			&& tagSyntax != SQUARE_BRACKET_TAG_SYNTAX
			&& tagSyntax != ANGLE_BRACKET_TAG_SYNTAX) {
			throw new IllegalArgumentException("This can only be set to one of three settings: Configuration.AUTO_DETECT_TAG_SYNTAX, Configuration.ANGLE_BRACKET_SYNTAX, or Configuration.SQAUARE_BRACKET_SYNTAX");
		}
		this.tagSyntax = tagSyntax;
	}

	/**
	 * @return whether the alternative square bracket syntax is set as the default
	 */
	public int getTagSyntax() {
		return this.tagSyntax;
	}

	/**
	 * Equivalent to <tt>getTemplate(name, thisCfg.getLocale(), thisCfg.getEncoding(thisCfg.getLocale()), true)</tt>.
	 */
	public Template getTemplate(String name) throws IOException {
		Locale loc = this.getLocale();
		return this.getTemplate(name,
								loc,
								this.getEncoding(loc),
								true);
	}

	/**
	 * Equivalent to <tt>getTemplate(name, locale, thisCfg.getEncoding(locale), true)</tt>.
	 */
	public Template getTemplate(String name,
								Locale locale) throws IOException {
		return this.getTemplate(name,
								locale,
								this.getEncoding(locale),
								true);
	}

	/**
	 * Equivalent to <tt>getTemplate(name, thisCfg.getLocale(), encoding, true)</tt>.
	 */
	public Template getTemplate(String name,
								String encoding) throws IOException {
		return this.getTemplate(name,
								this.getLocale(),
								encoding,
								true);
	}

	/**
	 * Equivalent to <tt>getTemplate(name, locale, encoding, true)</tt>.
	 */
	public Template getTemplate(String name,
								Locale locale,
								String encoding) throws IOException {
		return this.getTemplate(name,
								locale,
								encoding,
								true);
	}

	/**
	 * Retrieves a template specified by a name and locale, interpreted using the specified character encoding, either parsed or unparsed. For the
	 * exact semantics of parameters, see {@link TemplateCache#getTemplate(String, Locale, String, boolean)}.
	 * 
	 * @return the requested template.
	 * @throws FileNotFoundException
	 *             if the template could not be found.
	 * @throws IOException
	 *             if there was a problem loading the template.
	 * @throws ParseException
	 *             (extends <code>IOException</code>) if the template is syntactically bad.
	 */
	public Template getTemplate(String name,
								Locale locale,
								String encoding,
								boolean parse) throws IOException {
		Template result = this.cache.getTemplate(	name,
													locale,
													encoding,
													parse);
		if (result == null) {
			throw new FileNotFoundException("Template " + name + " not found.");
		}
		return result;
	}

	/**
	 * Sets the default encoding for converting bytes to characters when reading template files in a locale for which no explicit encoding was
	 * specified. Defaults to default system encoding.
	 */
	public void setDefaultEncoding(String encoding) {
		this.defaultEncoding = encoding;
	}

	/**
	 * Gets the default encoding for converting bytes to characters when reading template files in a locale for which no explicit encoding was
	 * specified. Defaults to default system encoding.
	 */
	public String getDefaultEncoding() {
		return this.defaultEncoding;
	}

	/**
	 * Gets the preferred character encoding for the given locale, or the default encoding if no encoding is set explicitly for the specified locale.
	 * You can associate encodings with locales using {@link #setEncoding(Locale, String)} or {@link #loadBuiltInEncodingMap()}.
	 * 
	 * @param loc
	 *            the locale
	 * @return the preferred character encoding for the locale.
	 */
	public String getEncoding(Locale loc) {
		// Try for a full name match (may include country and variant)
		String charset = (String) this.encodingMap.get(loc.toString());
		if (charset == null) {
			if (loc.getVariant().length() > 0) {
				Locale l = new Locale(	loc.getLanguage(),
										loc.getCountry());
				charset = (String) this.encodingMap.get(l.toString());
				if (charset != null) {
					this.encodingMap.put(	loc.toString(),
											charset);
				}
			}
			charset = (String) this.encodingMap.get(loc.getLanguage());
			if (charset != null) {
				this.encodingMap.put(	loc.toString(),
										charset);
			}
		}
		return charset != null	? charset
								: this.defaultEncoding;
	}

	/**
	 * Sets the character set encoding to use for templates of a given locale. If there is no explicit encoding set for some locale, then the default
	 * encoding will be used, what you can set with {@link #setDefaultEncoding}.
	 * 
	 * @see #clearEncodingMap
	 * @see #loadBuiltInEncodingMap
	 */
	public void setEncoding(Locale locale,
							String encoding) {
		this.encodingMap.put(	locale.toString(),
								encoding);
	}

	/**
	 * Adds a shared variable to the configuration. Shared variables are variables that are visible as top-level variables for all templates which use
	 * this configuration, if the data model does not contain a variable with the same name.
	 * 
	 * <p>
	 * Never use <tt>TemplateModel</tt> implementation that is not thread-safe for shared variables, if the configuration is used by multiple threads!
	 * It is the typical situation for Servlet based Web sites.
	 * 
	 * @param name
	 *            the name used to access the data object from your template. If a shared variable with this name already exists, it will replace
	 *            that.
	 * @see #setSharedVariable(String,Object)
	 * @see #setAllSharedVariables
	 */
	public void setSharedVariable(	String name,
									TemplateModel tm) {
		this.variables.put(	name,
							tm);
	}

	/**
	 * Adds shared variable to the configuration. It uses {@link Configurable#getObjectWrapper()} to wrap the <code>obj</code>.
	 * 
	 * @see #setSharedVariable(String,TemplateModel)
	 * @see #setAllSharedVariables
	 */
	public void setSharedVariable(	String name,
									Object obj) throws TemplateModelException {
		this.setSharedVariable(	name,
								this.getObjectWrapper().wrap(obj));
	}

	/**
	 * Gets a shared variable. Shared variables are variables that are available to all templates. When a template is processed, and an identifier is
	 * undefined in the data model, a shared variable object with the same identifier is then looked up in the configuration. There are several
	 * predefined variables that are always available through this method, see the FreeMarker manual for a comprehensive list of them.
	 * 
	 * @see #setSharedVariable(String,Object)
	 * @see #setSharedVariable(String,TemplateModel)
	 * @see #setAllSharedVariables
	 */
	public TemplateModel getSharedVariable(String name) {
		return this.variables.get(name);
	}

	/**
	 * @param name
	 */
	public final void removeSharedVariable(String name) {
		this.variables.remove(name);
	}

	/**
	 * Returns the set containing the names of all defined shared variables. The method returns a new Set object on each call that is completely
	 * disconnected from the Configuration. That is, modifying the set will have no effect on the Configuration object.
	 */
	public Set getSharedVariableNames() {
		return new HashSet(this.variables.getKeyList());
	}

	/**
	 * Adds all object in the hash as shared variable to the configuration.
	 * 
	 * <p>
	 * Never use <tt>TemplateModel</tt> implementation that is not thread-safe for shared variables, if the configuration is used by multiple threads!
	 * It is the typical situation for Servlet based Web sites.
	 * 
	 * @param hash
	 *            a hash model whose objects will be copied to the configuration with same names as they are given in the hash. If a shared variable
	 *            with these names already exist, it will be replaced with those from the map.
	 * 
	 * @see #setSharedVariable(String,Object)
	 * @see #setSharedVariable(String,TemplateModel)
	 */
	public void setAllSharedVariables(TemplateHashModelEx hash) throws TemplateModelException {
		TemplateModelIterator keys = hash.keys().iterator();
		TemplateModelIterator values = hash.values().iterator();
		while (keys.hasNext()) {
			this.setSharedVariable(	((TemplateScalarModel) keys.next()).getAsString(),
									values.next());
		}
	}

	/**
	 * Removes all shared variables, except the predefined ones (compress, html_escape, etc.).
	 */
	public void clearSharedVariables() {
		this.variables.clear();
		this.loadBuiltInSharedVariables();
	}

	/**
	 * Removes all entries from the template cache, thus forcing reloading of templates on subsequent <code>getTemplate</code> calls. This method is
	 * thread-safe and can be called while the engine works.
	 */
	public void clearTemplateCache() {
		this.cache.clear();
	}

	/**
	 * Returns if localized template lookup is enabled or not. This method is thread-safe and can be called while the engine works.
	 */
	public boolean getLocalizedLookup() {
		return this.cache.getLocalizedLookup();
	}

	/**
	 * Enables/disables localized template lookup. Enabled by default. This method is thread-safe and can be called while the engine works.
	 */
	public void setLocalizedLookup(boolean localizedLookup) {
		this.localizedLookup = localizedLookup;
		this.cache.setLocalizedLookup(localizedLookup);
	}

	/**
	 * Sets a setting by name and string value.
	 * 
	 * In additional to the settings understood by {@link Configurable#setSetting the super method}, it understands these:
	 * <ul>
	 * <li><code>"auto_import"</code>: Sets the list of auto-imports. Example of valid value: <br>
	 * <code>/lib/form.ftl as f, /lib/widget as w, "/lib/evil name.ftl" as odd</code> See: {@link #setAutoImports}
	 * <li><code>"auto_include"</code>: Sets the list of auto-includes. Example of valid value: <br>
	 * <code>/include/common.ftl, "/include/evil name.ftl"</code> See: {@link #setAutoIncludes}
	 * <li><code>"default_encoding"</code>: The name of the charset, such as <code>"UTF-8"</code>. See: {@link #setDefaultEncoding}
	 * <li><code>"localized_lookup"</code>: <code>"true"</code>, <code>"false"</code>, <code>"yes"</code>, <code>"no"</code>, <code>"t"</code>,
	 * <code>"f"</code>, <code>"y"</code>, <code>"n"</code>. Case insensitive. See: {@link #setLocalizedLookup}
	 * <li><code>"strict_syntax"</code>: <code>"true"</code>, <code>"false"</code>, etc. See: {@link #setStrictSyntaxMode}
	 * <li><code>"whitespace_stripping"</code>: <code>"true"</code>, <code>"false"</code>, etc. See: {@link #setWhitespaceStripping}
	 * <li><code>"cache_storage"</code>: If the value contains dot, then it is interpreted as class name, and the object will be created with its
	 * parameterless constructor. If the value does not contain dot, then a {@link freemarker.cache.MruCacheStorage} will be used with the maximum
	 * strong and soft sizes specified with the setting value. Examples of valid setting values:
	 * <table border=1 cellpadding=4>
	 * <tr>
	 * <th>Setting value
	 * <th>max. strong size
	 * <th>max. soft size
	 * <tr>
	 * <td><code>"strong:50, soft:500"</code>
	 * <td>50
	 * <td>500
	 * <tr>
	 * <td><code>"strong:100, soft"</code>
	 * <td>100
	 * <td><code>Integer.MAX_VALUE</code>
	 * <tr>
	 * <td><code>"strong:100"</code>
	 * <td>100
	 * <td>0
	 * <tr>
	 * <td><code>"soft:100"</code>
	 * <td>0
	 * <td>100
	 * <tr>
	 * <td><code>"strong"</code>
	 * <td><code>Integer.MAX_VALUE</code>
	 * <td>0
	 * <tr>
	 * <td><code>"soft"</code>
	 * <td>0
	 * <td><code>Integer.MAX_VALUE</code>
	 * </table>
	 * The value is not case sensitive. The order of <tt>soft</tt> and <tt>strong</tt> entries is not significant. See also: {@link #setCacheStorage}
	 * <li><code>"template_update_delay"</code>: Valid positive integer, the update delay measured in seconds. See: {@link #setTemplateUpdateDelay}
	 * <li><code>"tag_syntax"</code>: Must be one of: <code>"auto_detect"</code>, <code>"angle_bracket"</code>, <code>"square_bracket"</code>.
	 * </ul>
	 * 
	 * @param key
	 *            the name of the setting.
	 * @param value
	 *            the string that describes the new value of the setting.
	 * 
	 * @throws UnknownSettingException
	 *             if the key is wrong.
	 * @throws TemplateException
	 *             if the new value of the setting can't be set for any other reasons.
	 */
	@Override
	public void setSetting(	String key,
							String value) throws TemplateException {
		if ("TemplateUpdateInterval".equalsIgnoreCase(key)) {
			key = TEMPLATE_UPDATE_DELAY_KEY;
		}
		else if ("DefaultEncoding".equalsIgnoreCase(key)) {
			key = DEFAULT_ENCODING_KEY;
		}
		try {
			if (DEFAULT_ENCODING_KEY.equals(key)) {
				this.setDefaultEncoding(value);
			}
			else if (LOCALIZED_LOOKUP_KEY.equals(key)) {
				this.setLocalizedLookup(StringUtil.getYesNo(value));
			}
			else if (STRICT_SYNTAX_KEY.equals(key)) {
				this.setStrictSyntaxMode(StringUtil.getYesNo(value));
			}
			else if (WHITESPACE_STRIPPING_KEY.equals(key)) {
				this.setWhitespaceStripping(StringUtil.getYesNo(value));
			}
			else if (CACHE_STORAGE_KEY.equals(key)) {
				if (value.indexOf('.') == -1) {
					int strongSize = 0;
					int softSize = 0;
					Map map = StringUtil.parseNameValuePairList(value,
																String.valueOf(Integer.MAX_VALUE));
					Iterator it = map.entrySet().iterator();
					while (it.hasNext()) {
						Map.Entry ent = (Map.Entry) it.next();
						String pname = (String) ent.getKey();
						int pvalue;
						try {
							pvalue = Integer.parseInt((String) ent.getValue());
						}
						catch (NumberFormatException e) {
							throw this.invalidSettingValueException(key,
																	value);
						}
						if ("soft".equalsIgnoreCase(pname)) {
							softSize = pvalue;
						}
						else if ("strong".equalsIgnoreCase(pname)) {
							strongSize = pvalue;
						}
						else {
							throw this.invalidSettingValueException(key,
																	value);
						}
					}
					if (softSize == 0 && strongSize == 0) {
						throw this.invalidSettingValueException(key,
																value);
					}
					this.setCacheStorage(new MruCacheStorage(	strongSize,
																softSize));
				}
				else {
					this.setCacheStorage((CacheStorage) ClassUtil.forName(value).newInstance());
				}
			}
			else if (TEMPLATE_UPDATE_DELAY_KEY.equals(key)) {
				this.setTemplateUpdateDelay(Integer.parseInt(value));
			}
			else if (AUTO_INCLUDE_KEY.equals(key)) {
				this.setAutoIncludes(new SettingStringParser(value).parseAsList());
			}
			else if (AUTO_IMPORT_KEY.equals(key)) {
				this.setAutoImports(new SettingStringParser(value).parseAsImportList());
			}
			else if (TAG_SYNTAX_KEY.equals(key)) {
				if ("auto_detect".equals(value)) {
					this.setTagSyntax(AUTO_DETECT_TAG_SYNTAX);
				}
				else if ("angle_bracket".equals(value)) {
					this.setTagSyntax(ANGLE_BRACKET_TAG_SYNTAX);
				}
				else if ("square_bracket".equals(value)) {
					this.setTagSyntax(SQUARE_BRACKET_TAG_SYNTAX);
				}
				else {
					throw this.invalidSettingValueException(key,
															value);
				}
			}
			else {
				super.setSetting(	key,
									value);
			}
		}
		catch (Exception e) {
			throw new TemplateException("Failed to set setting " + key + " to value " + value,
										e,
										this.getEnvironment());
		}
	}

	/**
	 * Add an auto-imported template. The importing will happen at the top of any template that is vended by this Configuration object.
	 * 
	 * @param namespace
	 *            the name of the namespace into which the template is imported
	 * @param template
	 *            the name of the template
	 */
	public synchronized void addAutoImport(	String namespace,
											String template) {
		this.autoImports.remove(namespace);
		this.autoImports.add(namespace);
		this.autoImportMap.put(	namespace,
								template);
	}

	/**
	 * Remove an auto-imported template
	 * 
	 * @param namespace
	 *            the name of the namespace into which the template was imported
	 */

	public synchronized void removeAutoImport(String namespace) {
		this.autoImports.remove(namespace);
		this.autoImportMap.remove(namespace);
	}

	/**
	 * set a map of namespace names to templates for auto-importing a set of templates. Note that all previous auto-imports are removed.
	 */

	public synchronized void setAutoImports(Map map) {
		this.autoImports = new ArrayList(map.keySet());
		if (map instanceof HashMap) {
			this.autoImportMap = (Map) ((HashMap) map).clone();
		}
		else if (map instanceof SortedMap) {
			this.autoImportMap = new TreeMap(map);
		}
		else {
			this.autoImportMap = new HashMap(map);
		}
	}

	/**
	 * @see freemarker.core.Configurable#doAutoImportsAndIncludes(freemarker.core.Environment)
	 */
	@Override
	protected void doAutoImportsAndIncludes(Environment env) throws TemplateException,
															IOException {
		for (int i = 0; i < this.autoImports.size(); i++) {
			String namespace = (String) this.autoImports.get(i);
			String templateName = (String) this.autoImportMap.get(namespace);
			env.importLib(	templateName,
							namespace);
		}
		for (int i = 0; i < this.autoIncludes.size(); i++) {
			String templateName = (String) this.autoIncludes.get(i);
			Template template = this.getTemplate(	templateName,
													env.getLocale());
			env.include(template);
		}
	}

	/**
	 * add a template to be automatically included at the top of any template that is vended by this Configuration object.
	 * 
	 * @param templateName
	 *            the lookup name of the template.
	 */

	public synchronized void addAutoInclude(String templateName) {
		this.autoIncludes.remove(templateName);
		this.autoIncludes.add(templateName);
	}

	/**
	 * set the list of automatically included templates. Note that all previous auto-includes are removed.
	 */
	public synchronized void setAutoIncludes(List templateNames) {
		this.autoIncludes.clear();
		Iterator it = templateNames.iterator();
		while (it.hasNext()) {
			Object o = it.next();
			if (!(o instanceof String)) {
				throw new IllegalArgumentException("List items must be String-s.");
			}
			this.autoIncludes.add(o);
		}
	}

	/**
	 * remove a template from the auto-include list.
	 * 
	 * @param templateName
	 *            the lookup name of the template in question.
	 */

	public synchronized void removeAutoInclude(String templateName) {
		this.autoIncludes.remove(templateName);
	}

	/**
	 * Returns FreeMarker version number string. Examples of possible return values: <code>"2.2.5"</code>, <code>"2.3pre13"</code>,
	 * <code>"2.3pre13mod"</code>, <code>"2.3rc1"</code>, <code>"2.3"</code>, <code>"3.0"</code>.
	 * 
	 * <p>
	 * Notes on FreeMarker version numbering rules:
	 * <ul>
	 * <li>"pre" and "rc" (lowercase!) means "preview" and "release candidate" respectively. It is must be followed with a number (as "1" for the
	 * first release candidate).
	 * <li>The "mod" after the version number indicates that it's an unreleased modified version of the released version. After releases, the nighly
	 * builds are such releases. E.g. the nightly build after releasing "2.2.1" but before releasing "2.2.2" is "2.2.1mod".
	 * <li>The 2nd version number must be present, and maybe 0, as in "3.0".
	 * <li>The 3rd version number is never 0. E.g. the version number string for the first release of the 2.2 series is "2.2", and NOT "2.2.0".
	 * <li>When only the 3rd version number increases (2.2 -> 2.2.1, 2.2.1 -> 2.2.2 etc.), 100% backward compatiblity with the previous version MUST
	 * be kept. This means that <tt>freemarker.jar</tt> can be replaced in an application without risk (as far as the application doesn't depend on
	 * the presence of a FreeMarker bug). Note that backward compatibility restrictions do not apply for preview releases.
	 * </ul>
	 */
	public static String getVersionNumber() {
		if (cachedVersion != null) {
			return cachedVersion;
		}
		try {
			Properties vp = new Properties();
			InputStream ins = Configuration.class.getClassLoader().getResourceAsStream("freemarker/version.properties");
			if (ins == null) {
				throw new RuntimeException("Version file is missing.");
			}
			else {
				try {
					vp.load(ins);
				}
				finally {
					ins.close();
				}
				String v = vp.getProperty("version");
				if (v == null) {
					throw new RuntimeException("Version file is corrupt: version key is missing.");
				}
				cachedVersion = v;
			}
			return cachedVersion;
		}
		catch (IOException e) {
			throw new RuntimeException("Failed to load version file: " + e);
		}
	}

	/**
	 * Loads a preset language-to-encoding map. It assumes the usual character encodings for most languages. The previous content of the encoding map
	 * will be lost. This default map currently contains the following mappings:
	 * <table>
	 * <tr>
	 * <td>ar</td>
	 * <td>ISO-8859-6</td>
	 * </tr>
	 * <tr>
	 * <td>be</td>
	 * <td>ISO-8859-5</td>
	 * </tr>
	 * <tr>
	 * <td>bg</td>
	 * <td>ISO-8859-5</td>
	 * </tr>
	 * <tr>
	 * <td>ca</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>cs</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>da</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>de</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>el</td>
	 * <td>ISO-8859-7</td>
	 * </tr>
	 * <tr>
	 * <td>en</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>es</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>et</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>fi</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>fr</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>hr</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>hu</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>is</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>it</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>iw</td>
	 * <td>ISO-8859-8</td>
	 * </tr>
	 * <tr>
	 * <td>ja</td>
	 * <td>Shift_JIS</td>
	 * </tr>
	 * <tr>
	 * <td>ko</td>
	 * <td>EUC-KR</td>
	 * </tr>
	 * <tr>
	 * <td>lt</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>lv</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>mk</td>
	 * <td>ISO-8859-5</td>
	 * </tr>
	 * <tr>
	 * <td>nl</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>no</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>pl</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>pt</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>ro</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>ru</td>
	 * <td>ISO-8859-5</td>
	 * </tr>
	 * <tr>
	 * <td>sh</td>
	 * <td>ISO-8859-5</td>
	 * </tr>
	 * <tr>
	 * <td>sk</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>sl</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>sq</td>
	 * <td>ISO-8859-2</td>
	 * </tr>
	 * <tr>
	 * <td>sr</td>
	 * <td>ISO-8859-5</td>
	 * </tr>
	 * <tr>
	 * <td>sv</td>
	 * <td>ISO-8859-1</td>
	 * </tr>
	 * <tr>
	 * <td>tr</td>
	 * <td>ISO-8859-9</td>
	 * </tr>
	 * <tr>
	 * <td>uk</td>
	 * <td>ISO-8859-5</td>
	 * </tr>
	 * <tr>
	 * <td>zh</td>
	 * <td>GB2312</td>
	 * </tr>
	 * <tr>
	 * <td>zh_TW</td>
	 * <td>Big5</td>
	 * </tr>
	 * </table>
	 * 
	 * @see #clearEncodingMap
	 * @see #setEncoding
	 */
	public void loadBuiltInEncodingMap() {
		this.encodingMap.clear();
		this.encodingMap.put(	"ar",
								"ISO-8859-6");
		this.encodingMap.put(	"be",
								"ISO-8859-5");
		this.encodingMap.put(	"bg",
								"ISO-8859-5");
		this.encodingMap.put(	"ca",
								"ISO-8859-1");
		this.encodingMap.put(	"cs",
								"ISO-8859-2");
		this.encodingMap.put(	"da",
								"ISO-8859-1");
		this.encodingMap.put(	"de",
								"ISO-8859-1");
		this.encodingMap.put(	"el",
								"ISO-8859-7");
		this.encodingMap.put(	"en",
								"ISO-8859-1");
		this.encodingMap.put(	"es",
								"ISO-8859-1");
		this.encodingMap.put(	"et",
								"ISO-8859-1");
		this.encodingMap.put(	"fi",
								"ISO-8859-1");
		this.encodingMap.put(	"fr",
								"ISO-8859-1");
		this.encodingMap.put(	"hr",
								"ISO-8859-2");
		this.encodingMap.put(	"hu",
								"ISO-8859-2");
		this.encodingMap.put(	"is",
								"ISO-8859-1");
		this.encodingMap.put(	"it",
								"ISO-8859-1");
		this.encodingMap.put(	"iw",
								"ISO-8859-8");
		this.encodingMap.put(	"ja",
								"Shift_JIS");
		this.encodingMap.put(	"ko",
								"EUC-KR");
		this.encodingMap.put(	"lt",
								"ISO-8859-2");
		this.encodingMap.put(	"lv",
								"ISO-8859-2");
		this.encodingMap.put(	"mk",
								"ISO-8859-5");
		this.encodingMap.put(	"nl",
								"ISO-8859-1");
		this.encodingMap.put(	"no",
								"ISO-8859-1");
		this.encodingMap.put(	"pl",
								"ISO-8859-2");
		this.encodingMap.put(	"pt",
								"ISO-8859-1");
		this.encodingMap.put(	"ro",
								"ISO-8859-2");
		this.encodingMap.put(	"ru",
								"ISO-8859-5");
		this.encodingMap.put(	"sh",
								"ISO-8859-5");
		this.encodingMap.put(	"sk",
								"ISO-8859-2");
		this.encodingMap.put(	"sl",
								"ISO-8859-2");
		this.encodingMap.put(	"sq",
								"ISO-8859-2");
		this.encodingMap.put(	"sr",
								"ISO-8859-5");
		this.encodingMap.put(	"sv",
								"ISO-8859-1");
		this.encodingMap.put(	"tr",
								"ISO-8859-9");
		this.encodingMap.put(	"uk",
								"ISO-8859-5");
		this.encodingMap.put(	"zh",
								"GB2312");
		this.encodingMap.put(	"zh_TW",
								"Big5");
	}
}

/**
 * Returns the default (singleton) Configuration object. Note that you can create as many separate configurations as you wish; this global instance is
 * provided for convenience, or when you have no reason to use a separate instance.
 * 
 * @deprecated The usage of the static singleton (the "default") {@link Configuration} instance can easily cause erroneous, unpredictable behavior.
 *             This is because multiple independent software components may use FreeMarker internally inside the same application, so they will
 *             interfere because of the common {@link Configuration} instance. Each such component should use its own private {@link Configuration}
 *             object instead, that it typically creates with <code>new Configuration()</code> when the component is initialized.
 */
// @Deprecated
// static public Configuration getDefaultConfiguration() {
// return defaultConfig;
// }

/**
 * Sets the Configuration object that will be retrieved from future calls to {@link #getDefaultConfiguration()}.
 * 
 * @deprecated Using the "default" {@link Configuration} instance can easily lead to erroneous, unpredictable behaviour. See more
 *             {@link Configuration#getDefaultConfiguration() here...}.
 */
// @Deprecated
// static public void setDefaultConfiguration(Configuration config) {
// defaultConfig = config;
// }

/**
 * Sets the servlet context from which to load templates
 * 
 * @param sctxt
 *            the ServletContext object. Note that the type is <code>Object</code> to prevent class loading errors when user who uses FreeMarker not
 *            for servlets has no javax.servlet in the CLASSPATH.
 * @param path
 *            the path relative to the ServletContext. If this path is absolute, it is taken to be relative to the server's URL, i.e.
 *            http://myserver.com/ and if the path is relative, it is taken to be relative to the web app context, i.e.
 *            http://myserver.context.com/mywebappcontext/
 */
// public void setServletContextForTemplateLoading(Object sctxt,
// String path) {
// try {
// if (path == null) {
// this.setTemplateLoader((TemplateLoader) ClassUtil.forName("freemarker.cache.WebappTemplateLoader").getConstructor(new Class[] {
// ClassUtil.forName("javax.servlet.ServletContext")
// }).newInstance(new Object[] {
// sctxt
// }));
// }
// else {
// this.setTemplateLoader((TemplateLoader) ClassUtil.forName("freemarker.cache.WebappTemplateLoader").getConstructor(new Class[] {
// ClassUtil.forName("javax.servlet.ServletContext"),
// String.class
// }).newInstance(new Object[] {
// sctxt,
// path
// }));
// }
// }
// catch (Exception exc) {
// throw new RuntimeException("Internal FreeMarker error: " + exc);
// }
// }

// @Override
// public Object clone() {
// try {
// Configuration copy = (Configuration) super.clone();
// copy.variables = new HashMap(this.variables);
// copy.encodingMap = new HashMap(this.encodingMap);
// copy.createTemplateCache( this.cache.getTemplateLoader(),
// this.cache.getCacheStorage());
// return copy;
// }
// catch (CloneNotSupportedException e) {
// throw new RuntimeException("Clone is not supported, but it should be: "
// + e.getMessage());
// }
// }
