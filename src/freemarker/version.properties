# $Id: version.properties,v 1.1 2010/09/04 12:00:50 dragon Exp $
# Version info for the builds.

# Version string.
#
# Examples:
# Version number   Means
# 2.2.5            5th bugfix/improved release of the 2.2 series.
# 2.3pre13         13th preview of incoming version "2.3"
# 2.3pre13mod      Unreleased modified version of 2.3pre13. The nightly
#                  builds after releasing 2.3pre13 but before the releasing
#                  of the next version should be marked as this.
# 2.3rc1           1st release candidate of incoming version "2.3"
# 2.3              The 1st release of the 2.3 series.
# 3.0              The 1st release of the 3.0 series.
# 
# Notes on version numbering policy:
# - "pre" and "rc" (lowercase!) means "preview" and "release
#   candidate" respectively. It is must be followed with a
#   number (as "1" for the first release candidate).
# - "mod" after the version number indicates, that it's an
#   unreleased modified version of the released version.
#   After releases, the "mod" should be added, as the nighly builds
#   are such releases.
# - The 2nd version number must be present, and maybe 0,
#   as in "3.0".
# - The 3rd version number is never 0. E.g. the version
#   number string for the first release of the 2.2 series
#   is "2.2", and NOT "2.2.0". 
# - When only the 3rd version number increases
#   (2.2 -> 2.2.1, 2.2.1 -> 2.2.2 etc.), 100% backward compatiblity
#   with the previous version MUST be kept.
#   This means that freemarker.jar can be replaced in an application
#   without risk (as far as the application doesn't depend on the
#   presence of a FreeMarker bug).
#   Note that backward compatibility restrictions do not apply for
#   preview releases.
version=2.3.16

# Version string for Manifest.mf: dot separated numbers.
#
# Examples:
# version     -> versionForMf
# 2.2.5       -> 2.2.5
# 2.3pre13    -> 2.2.98.13
# 2.3pre13mod -> 2.2.98.13.97
# 2.3rc1      -> 2.2.99.1
# 2.3         -> 2.3
# 2.3mod      -> 2.3.0.97
# 3.0pre2     -> 2.98.2
#
# 97 denotes "mod" (a generic nightly build), 98 denotes "pre", 99 denotes "rc".
# In general, for the preview Y or rc Y of version 2.X, the versionForMf is
# 2.X-1.(99|98).Y. Note the X-1.
versionForMf=2.3.16
